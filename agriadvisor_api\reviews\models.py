# reviews/models.py
from django.db import models
from django.core.validators import Min<PERSON><PERSON>ueValidator, MaxValueValidator
from accounts.models import CustomUser
from advisory.models import Booking

class Review(models.Model):
    booking = models.OneToOneField(Booking, on_delete=models.CASCADE, related_name="review")
    farmer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="given_reviews", limit_choices_to={'role': 'farmer'})
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="received_reviews", limit_choices_to={'role': 'expert'})
    
    rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('booking', 'farmer') # A farmer can only review a booking once

    def __str__(self):
        return f"Review for Booking {self.booking.id} by {self.farmer.username}"

