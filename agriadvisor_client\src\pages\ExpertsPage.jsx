// src/pages/ExpertsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, InputGroup, Form, Card } from "react-bootstrap";
import { getExperts, createEx<PERSON>, updateEx<PERSON>, deleteEx<PERSON> } from "../services/expertService";
import ExpertModal from "../components/ExpertModal";
import ExpertCard from "../components/ExpertCard";

// Premium Admin Experts CSS and Animations
const premiumCSS = `
  @keyframes expertSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes expertCardHover {
    from {
      transform: translateY(0) scale(1);
    }
    to {
      transform: translateY(-10px) scale(1.02);
    }
  }

  @keyframes addButtonPulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(67, 233, 123, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(67, 233, 123, 0.6);
    }
  }

  @keyframes searchFocus {
    from {
      box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    to {
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }
  }

  .premium-experts-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: expertSlideIn 0.6s ease-out;
  }

  .premium-experts-card:hover {
    animation: expertCardHover 0.3s ease-out forwards;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .expert-grid-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    animation: expertSlideIn 0.6s ease-out;
    overflow: hidden;
  }

  .expert-grid-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
  }

  .add-expert-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    animation: addButtonPulse 3s infinite;
    position: relative;
    overflow: hidden;
  }

  .add-expert-btn:hover {
    transform: translateY(-2px);
    animation: none;
    box-shadow: 0 15px 30px rgba(67, 233, 123, 0.4);
  }

  .add-expert-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .add-expert-btn:hover::before {
    left: 100%;
  }

  .search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .premium-search-input {
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .premium-search-input:focus {
    box-shadow: 0 5px 25px rgba(102, 126, 234, 0.3);
    animation: searchFocus 0.3s ease;
  }

  .specialty-filter {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-btn:hover, .filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .experts-stats {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(168, 237, 234, 0.3);
  }

  .slide-in-up {
    animation: expertSlideIn 0.6s ease-out;
  }

  .no-experts-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }

  .expert-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.8rem;
    margin: 0 auto 15px;
    border: 4px solid white;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  }

  .specialty-badge {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  }
};

const ExpertsPage = () => {
  const [experts, setExperts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingExpert, setEditingExpert] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [specialtyFilter, setSpecialtyFilter] = useState("all");
  const [filteredExperts, setFilteredExperts] = useState([]);

  // Specialty filters
  const specialtyFilters = [
    { id: "all", name: "All Specialties", icon: "bi-grid-3x3-gap" },
    { id: "crop", name: "Crop Management", icon: "bi-flower1" },
    { id: "soil", name: "Soil Analysis", icon: "bi-geo-alt" },
    { id: "pest", name: "Pest Control", icon: "bi-bug" },
    { id: "irrigation", name: "Irrigation", icon: "bi-droplet" },
    { id: "livestock", name: "Livestock", icon: "bi-house" }
  ];

  // Helper functions
  const getExpertStats = () => {
    const totalExperts = experts.length;
    const specialties = new Set(experts.map(expert => expert.expert_profile?.specialty || 'General')).size;
    const activeExperts = experts.filter(expert => expert.is_active !== false).length;

    return {
      total: totalExperts,
      specialties: specialties,
      active: activeExperts
    };
  };

  // Filter experts based on search term and specialty
  useEffect(() => {
    let filtered = experts;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(expert =>
        expert.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expert.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (expert.expert_profile?.specialty || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by specialty
    if (specialtyFilter !== "all") {
      filtered = filtered.filter(expert =>
        (expert.expert_profile?.specialty || '').toLowerCase().includes(specialtyFilter)
      );
    }

    setFilteredExperts(filtered);
  }, [experts, searchTerm, specialtyFilter]);

  const fetchExperts = async () => {
    try {
      setLoading(true);
      setError("");
      const data = await getExperts();
      setExperts(data);
    } catch (err) {
      setError("Failed to fetch experts.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExperts();
  }, []);

  const handleShowAddModal = () => {
    setEditingExpert(null);
    setShowModal(true);
  };

  const handleShowEditModal = (expert) => {
    setEditingExpert(expert);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingExpert(null);
  };

  const handleSaveExpert = async (expertData) => {
    // Format the data to match the backend serializer
    const formattedData = {
      full_name: expertData.full_name,
      email: expertData.email,
      expert_profile: {
        specialty: expertData.specialty,
        bio: expertData.bio,
      },
    };

    try {
      if (editingExpert) {
        await updateExpert(editingExpert.id, formattedData);
      } else {
        await createExpert(formattedData);
      }
      handleCloseModal();
      fetchExperts();
    } catch (err) {
      console.error("Failed to save expert:", err.response.data);
      alert("Failed to save expert. Please check the console for details.");
    }
  };
  

  const handleDeleteExpert = async (id) => {
    if (window.confirm("Are you sure you want to delete this expert?")) {
      try {
        await deleteExpert(id);
        fetchExperts();
      } catch (err) {
        setError("Failed to delete expert. They might be assigned to a booking.");
      }
    }
  };

  if (loading) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-people me-3"></i>
                  Manage Experts
                </h1>
                <p className="lead mb-0 opacity-90">
                  Loading expert advisors...
                </p>
              </Col>
              <Col xs="auto">
                <Button className="add-expert-btn" disabled>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Loading...
                </Button>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="text-center" style={{ marginTop: '5rem' }}>
            <div className="mx-auto" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              maxWidth: '500px',
              padding: '3rem',
              borderRadius: '25px',
              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
            }}>
              <Spinner animation="border" size="lg" style={{ color: 'white' }} />
              <h3 className="mt-3 mb-2">Loading Expert Advisors</h3>
              <p className="mb-0 opacity-75">Gathering agricultural expertise information...</p>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  const stats = getExpertStats();

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-people me-3"></i>
                Manage Experts
              </h1>
              <p className="lead mb-0 opacity-90">
                Oversee agricultural advisory specialists and their expertise
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {filteredExperts.length} Experts
                  </Badge>
                </div>
                <small className="opacity-75">Currently Showing</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Error Alert */}
        {error && (
          <Alert variant="danger" onClose={() => setError("")} dismissible className="border-0 rounded-4 mb-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Error
            </Alert.Heading>
            {error}
          </Alert>
        )}

        {/* Premium Expert Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="experts-stats slide-in-up">
              <Row className="text-center">
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#667eea' }}>
                    <i className="bi bi-people-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.total}</h6>
                  <small className="text-muted">Total Experts</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                    <i className="bi bi-star-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.specialties}</h6>
                  <small className="text-muted">Specialties</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                    <i className="bi bi-check-circle-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.active}</h6>
                  <small className="text-muted">Active Experts</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Premium Search and Filter Section */}
        <div className="search-container slide-in-up">
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-3 text-white">
                <i className="bi bi-search me-2"></i>
                Find Expert Advisors
              </h4>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search by name, email, or specialty..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="premium-search-input"
                  style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                />
              </InputGroup>
            </Col>
            <Col md={4} className="text-center">
              <div className="text-white">
                <div className="h2 mb-1">
                  <i className="bi bi-award-fill"></i>
                </div>
                <h5 className="mb-0">Expert Network</h5>
                <small className="opacity-75">Agricultural specialists</small>
              </div>
            </Col>
          </Row>
        </div>

        {/* Premium Specialty Filter */}
        <div className="specialty-filter slide-in-up">
          <h5 className="mb-3" style={{ color: '#333' }}>
            <i className="bi bi-funnel me-2"></i>
            Filter by Specialty
          </h5>
          <div className="d-flex flex-wrap justify-content-between align-items-center">
            <div className="d-flex flex-wrap">
              {specialtyFilters.map(filter => (
                <Button
                  key={filter.id}
                  className={`filter-btn ${specialtyFilter === filter.id ? 'active' : ''}`}
                  onClick={() => setSpecialtyFilter(filter.id)}
                >
                  <i className={`${filter.icon} me-2`}></i>
                  {filter.name}
                </Button>
              ))}
            </div>
            <Button className="add-expert-btn" onClick={handleShowAddModal}>
              <i className="bi bi-plus-circle me-2"></i>
              Add New Expert
            </Button>
          </div>
        </div>

        {/* Premium Experts Grid */}
        {filteredExperts.length > 0 ? (
          <Row xs={1} md={2} lg={3} xl={4} className="g-4">
            {filteredExperts.map((expert, index) => (
              <Col key={expert.id}>
                <PremiumExpertCard
                  expert={expert}
                  onEdit={handleShowEditModal}
                  onDelete={handleDeleteExpert}
                  animationDelay={index * 0.1}
                />
              </Col>
            ))}
          </Row>
        ) : experts.length > 0 ? (
          <div className="text-center py-5">
            <div className="premium-experts-card p-5">
              <div className="mb-3">
                <i className="bi bi-search text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <h4 className="text-muted">No experts found</h4>
              <p className="text-muted mb-4">
                Try adjusting your search terms or specialty filter
              </p>
              <Button
                variant="outline-primary"
                onClick={() => {
                  setSearchTerm("");
                  setSpecialtyFilter("all");
                }}
                className="rounded-pill px-4"
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <div className="no-experts-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-people" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">No Experts Yet</h3>
            <p className="mb-4 opacity-90">
              Start building your expert network by adding agricultural specialists to the platform.
            </p>
            <Button
              variant="light"
              size="lg"
              onClick={handleShowAddModal}
              style={{ color: '#333', fontWeight: 'bold' }}
            >
              <i className="bi bi-plus-circle me-2"></i>
              Add Your First Expert
            </Button>
          </div>
        )}
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help managing experts?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>

      <ExpertModal show={showModal} onHide={handleCloseModal} onSave={handleSaveExpert} expert={editingExpert} />
    </div>
  );
};

// Premium Expert Card Component
const PremiumExpertCard = ({ expert, onEdit, onDelete, animationDelay = 0 }) => {
  // Helper function to render star rating
  const renderStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="bi bi-star-fill text-warning"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="bi bi-star-half text-warning"></i>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="bi bi-star text-muted"></i>);
    }

    return stars;
  };

  return (
    <Card
      className="expert-grid-card h-100"
      style={{
        animationDelay: `${animationDelay}s`
      }}
    >
      <Card.Body className="p-4 text-center">
        {/* Expert Avatar */}
        <div className="expert-avatar">
          {expert.full_name ? expert.full_name.split(' ').map(name => name[0]).join('') :
           `${expert.first_name?.[0] || ''}${expert.last_name?.[0] || ''}`}
        </div>

        {/* Expert Info */}
        <h5 className="mb-1" style={{ color: '#333' }}>
          {expert.full_name || `${expert.first_name || ''} ${expert.last_name || ''}`.trim()}
        </h5>
        <p className="text-muted mb-2">{expert.email}</p>

        {/* Star Rating and Reviews */}
        <div className="mb-3">
          <div className="d-flex justify-content-center align-items-center mb-1">
            {renderStarRating(expert.average_rating || 0)}
            <span className="ms-2 text-muted small">
              ({expert.review_count || 0} reviews)
            </span>
          </div>
          <div className="text-center">
            <Badge
              bg="warning"
              text="dark"
              className="px-2 py-1"
              style={{ borderRadius: '10px', fontSize: '0.75rem' }}
            >
              {expert.average_rating ? expert.average_rating.toFixed(1) : '0.0'} ★
            </Badge>
          </div>
        </div>

        {/* Specialty Badge */}
        {expert.expert_profile?.specialty && (
          <div className="mb-3">
            <span className="specialty-badge">
              <i className="bi bi-award me-1"></i>
              {expert.expert_profile.specialty}
            </span>
          </div>
        )}

        {/* Bio/Expertise */}
        {expert.expert_profile?.bio && (
          <div className="mb-3">
            <p className="text-muted small" style={{
              fontSize: '0.85rem',
              lineHeight: '1.4',
              maxHeight: '3.6rem',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical'
            }}>
              {expert.expert_profile.bio}
            </p>
          </div>
        )}

        {/* Expert Stats */}
        <Row className="text-center mb-3">
          <Col xs={6}>
            <div className="p-2">
              <div className="h6 mb-1" style={{ color: '#667eea' }}>
                {expert.expert_profile?.years_experience || 0}
              </div>
              <small className="text-muted">Years Exp.</small>
            </div>
          </Col>
          <Col xs={6}>
            <div className="p-2">
              <div className="h6 mb-1" style={{ color: '#43e97b' }}>
                {expert.is_active !== false ? 'Active' : 'Inactive'}
              </div>
              <small className="text-muted">Status</small>
            </div>
          </Col>
        </Row>

        {/* Hourly Rate (if available) */}
        {expert.expert_profile?.hourly_rate && (
          <div className="mb-3">
            <div className="h5 mb-1" style={{
              color: '#43e97b',
              fontWeight: 'bold'
            }}>
              ${expert.expert_profile.hourly_rate}/hr
            </div>
            <small className="text-muted">Consultation Rate</small>
          </div>
        )}

        {/* Action Buttons */}
        <div className="d-flex gap-2 justify-content-center mb-3">
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => onEdit(expert)}
            className="rounded-pill px-3"
            title="Edit Expert"
          >
            <i className="bi bi-pencil"></i>
          </Button>
          <Button
            variant="outline-danger"
            size="sm"
            onClick={() => onDelete(expert.id)}
            className="rounded-pill px-3"
            title="Delete Expert"
          >
            <i className="bi bi-trash"></i>
          </Button>
          <Button
            variant="outline-info"
            size="sm"
            onClick={() => {
              // Future: View expert details/profile
              alert(`View full profile for ${expert.full_name || `${expert.first_name} ${expert.last_name}`}`);
            }}
            className="rounded-pill px-3"
            title="View Profile"
          >
            <i className="bi bi-eye"></i>
          </Button>
        </div>

        {/* Expert Verification Status */}
        <div className="p-2 rounded-3" style={{
          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
        }}>
          <small className="text-muted">
            <i className="bi bi-shield-check me-1"></i>
            Verified Agricultural Expert
          </small>
        </div>
      </Card.Body>
    </Card>
  );
};

export default ExpertsPage;




