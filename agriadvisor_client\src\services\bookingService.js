// src/services/bookingService.js

import api from "./api";

/**
 * Fetches all bookings for the current tenant.
 */
export const getBookings = async () => {
  try {
    const response = await api.get("/bookings/");
    return response.data;
  } catch (error) {
    console.error("Error fetching bookings:", error);
    throw error;
  }
};

/**
 * Creates a new booking.
 */
export const createBooking = async (bookingData) => {
  try {
    const response = await api.post("/bookings/", bookingData);
    return response.data;
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
};

/**
 * Updates an existing booking.
 */
export const updateBooking = async (id, bookingData) => {
  try {
    const response = await api.put(`/bookings/${id}/`, bookingData);
    return response.data;
  } catch (error) {
    console.error(`Error updating booking ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes a booking.
 */
export const deleteBooking = async (id) => {
  try {
    await api.delete(`/bookings/${id}/`);
  } catch (error) {
    console.error(`Error deleting booking ${id}:`, error);
    throw error;
  }
};



/**
 * Fetches all bookings for the currently logged-in FARMER.
 */
export const getMyBookings = async () => {
  try {
    const response = await api.get('/farmer/my-bookings/');
    return response.data;
  } catch (error) {
    console.error("Error fetching farmer's bookings:", error);
    throw error;
  }
};



/**
 * Fetches all bookings for the currently logged-in EXPERT.
 */
export const getMySchedule = async () => {
  try {
    const response = await api.get('/expert/my-bookings/');
    return response.data;
  } catch (error) {
    console.error("Error fetching expert's schedule:", error);
    throw error;
  }
};


export const markBookingAsComplete = (bookingId) => {
  return api.post(`/expert/my-bookings/${bookingId}/complete/`);
};


