// src/pages/AnnouncementsPage.jsx

import React, { useState, useEffect } from "react";
import { Container, <PERSON>, <PERSON><PERSON>, Spinner, Alert } from "react-bootstrap";
import { getAnnouncements } from "../services/announcementService";
// We will create the modal in the next step
// import AnnouncementModal from '../components/AnnouncementModal';

const AnnouncementsPage = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const response = await getAnnouncements();
        setAnnouncements(response.data);
      } catch (err) {
        setError("Failed to fetch announcements.");
      } finally {
        setLoading(false);
      }
    };
    fetchAnnouncements();
  }, []);

  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  return (
    <Container fluid>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Manage Announcements</h2>
        <Button variant="primary">
          <i className="bi bi-plus-lg me-2"></i>New Announcement
        </Button>
      </div>

      <Table striped bordered hover responsive>
        <thead>
          <tr>
            <th>Title</th>
            <th>Target Audience</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {announcements.length > 0 ? (
            announcements.map((ann) => (
              <tr key={ann.id}>
                <td>{ann.title}</td>
                <td className="text-capitalize">{ann.target_role}</td>
                <td>
                  {ann.is_active ? (
                    <span className="badge bg-success">Active</span>
                  ) : (
                    <span className="badge bg-secondary">Inactive</span>
                  )}
                </td>
                <td>
                  <Button variant="outline-secondary" size="sm" className="me-2">
                    Edit
                  </Button>
                  <Button variant="outline-danger" size="sm">
                    Delete
                  </Button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="4" className="text-center">
                No announcements found.
              </td>
            </tr>
          )}
        </tbody>
      </Table>

      {/* We will add the AnnouncementModal here later */}
    </Container>
  );
};

export default AnnouncementsPage;


