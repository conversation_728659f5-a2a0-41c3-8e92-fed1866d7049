# accounts/management/commands/create_test_users.py

from django.core.management.base import BaseCommand
from accounts.models import CustomU<PERSON>, Tenant, ExpertProfile, FarmerProfile
from advisory.models import Service


class Command(BaseCommand):
    help = 'Create test users with known passwords for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing test users and recreate them',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Deleting existing test users...')
            CustomUser.objects.filter(username__in=['admin_user', 'expert_user', 'farmer_user']).delete()
            Tenant.objects.filter(name__in=['Test Farm Advisory', 'Demo Farm']).delete()

        self.create_test_users()

    def create_test_users(self):
        """Create test users with known passwords"""
        
        # Create test tenant
        tenant, created = Tenant.objects.get_or_create(
            name='Test Farm Advisory',
            defaults={'name': 'Test Farm Advisory'}
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created tenant: {tenant.name}'))
        else:
            self.stdout.write(f'Using existing tenant: {tenant.name}')

        # Create admin user
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'role': 'admin',
                'tenant': tenant,
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(self.style.SUCCESS(f'Created admin user: admin_user / admin123'))
        else:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(f'Reset password for admin user: admin_user / admin123')

        # Create expert user
        expert_user, created = CustomUser.objects.get_or_create(
            username='expert_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Expert',
                'last_name': 'Smith',
                'role': 'expert',
                'tenant': tenant,
            }
        )
        if created:
            expert_user.set_password('expert123')
            expert_user.save()
            self.stdout.write(self.style.SUCCESS(f'Created expert user: expert_user / expert123'))
        else:
            expert_user.set_password('expert123')
            expert_user.save()
            self.stdout.write(f'Reset password for expert user: expert_user / expert123')

        # Create expert profile with payment capabilities
        expert_profile, created = ExpertProfile.objects.get_or_create(
            user=expert_user,
            defaults={
                'specialty': 'Crop Management',
                'bio': 'Experienced agricultural expert specializing in crop management and sustainable farming practices.',
                'stripe_account_id': 'acct_test_1234567890',  # Test Stripe account
                'payouts_enabled': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created expert profile with payment capabilities'))
        else:
            expert_profile.stripe_account_id = 'acct_test_1234567890'
            expert_profile.payouts_enabled = True
            expert_profile.save()
            self.stdout.write(f'Updated expert profile with payment capabilities')

        # Create farmer user
        farmer_user, created = CustomUser.objects.get_or_create(
            username='farmer_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Farmer',
                'last_name': 'Johnson',
                'role': 'farmer',
                'tenant': tenant,
            }
        )
        if created:
            farmer_user.set_password('farmer123')
            farmer_user.save()
            self.stdout.write(self.style.SUCCESS(f'Created farmer user: farmer_user / farmer123'))
        else:
            farmer_user.set_password('farmer123')
            farmer_user.save()
            self.stdout.write(f'Reset password for farmer user: farmer_user / farmer123')

        # Create farmer profile
        farmer_profile, created = FarmerProfile.objects.get_or_create(
            user=farmer_user,
            defaults={
                'location_country': 'United States',
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created farmer profile'))

        # Create a test service
        service, created = Service.objects.get_or_create(
            name='Crop Consultation',
            defaults={
                'description': 'One-on-one consultation for crop management and optimization',
                'price': 50.00,
                'currency': 'USD',
                'tenant': tenant,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created test service: {service.name}'))

        self.stdout.write(self.style.SUCCESS('\n=== LOGIN CREDENTIALS ==='))
        self.stdout.write(f'Admin:  admin_user  / admin123')
        self.stdout.write(f'Expert: expert_user / expert123')
        self.stdout.write(f'Farmer: farmer_user / farmer123')
        self.stdout.write(f'\nAPI URL: http://localhost:8001')
        self.stdout.write(f'Admin Panel: http://localhost:8001/admin/')
