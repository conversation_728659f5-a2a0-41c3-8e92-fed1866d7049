
# FullCalendar React Component [![Build Status](https://travis-ci.com/fullcalendar/fullcalendar-react.svg?branch=master)](https://travis-ci.com/fullcalendar/fullcalendar-react)

An official FullCalendar component for React.

This is more than a mere "connector". It tells the core FullCalendar package to begin rendering with **React** virtual DOM nodes as opposed to the [Preact](https://preactjs.com/) nodes it normally uses, transforming FullCalendar into a "real" React component. You can learn a bit more [from this blog post](https://fullcalendar.io/blog/2020/05/react-ts-v5-beta) (more info to come).

- [FullCalendar React Component Docs](https://fullcalendar.io/docs/react)
- [Example Project](https://github.com/fullcalendar/fullcalendar-example-projects/tree/master/react)
- [Example Project with TypeScript](https://github.com/fullcalendar/fullcalendar-example-projects/tree/master/react-typescript)
- [Contributor Guide](CONTRIBUTORS.md)
