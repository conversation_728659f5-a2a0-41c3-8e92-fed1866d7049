import React, { useState, useEffect } from "react";
import { Alert } from "react-bootstrap";
import api from "../services/api"; // Use raw api for a new service call

const AnnouncementBanner = () => {
  const [announcement, setAnnouncement] = useState(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const fetchAnnouncement = async () => {
      try {
        const response = await api.get("/announcements/current/");
        const currentAnnouncement = response.data;

        if (currentAnnouncement && currentAnnouncement.id) {
          // Check localStorage to see if this specific announcement was already dismissed
          const dismissedId = localStorage.getItem("dismissed_announcement_id");
          if (String(currentAnnouncement.id) !== dismissedId) {
            setAnnouncement(currentAnnouncement);
            setShow(true);
          }
        }
      } catch (error) {
        console.error("Could not fetch announcement", error);
        // Don't set any state on error to prevent re-renders
        // Just silently fail and don't show any announcement
      }
    };

    // Add a small delay to prevent immediate API calls during rapid re-renders
    const timeoutId = setTimeout(fetchAnnouncement, 100);

    return () => clearTimeout(timeoutId);
  }, []);

  const handleDismiss = () => {
    // Remember that this user has dismissed this announcement
    localStorage.setItem("dismissed_announcement_id", announcement.id);
    setShow(false);
  };

  if (!show || !announcement) {
    return null;
  }

  return (
    <Alert variant="info" onClose={handleDismiss} dismissible className="m-3">
      <Alert.Heading>{announcement.title}</Alert.Heading>
      <p>{announcement.message}</p>
    </Alert>
  );
};

export default AnnouncementBanner;



