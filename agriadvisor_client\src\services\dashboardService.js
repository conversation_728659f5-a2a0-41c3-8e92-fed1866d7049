// src/services/dashboardService.js

import api from "./api"; // Our configured Axios instance

/**
 * Fetches the aggregated statistics for the admin dashboard.
 * @returns {Promise<Object>} A promise that resolves to the stats object.
 * e.g., { total_experts: 12, total_services: 8, ... }
 */
export const getDashboardStats = async () => {
  try {
    const response = await api.get("/dashboard/stats/");
    return response.data;
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    // Return a default object or re-throw the error, depending on desired error handling
    throw error;
  }
};



/**
 * Fetches the consolidated data for the FARMER dashboard.
 * @returns {Promise<Object>} A promise that resolves to the dashboard data.
 * e.g., { next_booking: {...}, recent_activity: [...] }
 */
export const getFarmerDashboardData = async () => {
  try {
    const response = await api.get('/farmer/dashboard/');
    return response.data;
  } catch (error) {
    console.error("Error fetching farmer dashboard data:", error);
    throw error;
  }
};



/**
 * Fetches the consolidated data for the EXPERT dashboard.
 * @returns {Promise<Object>} A promise that resolves to the dashboard data.
 * e.g., { stats: {...}, calendar_bookings: [...] }
 */
export const getExpertDashboardData = async () => {
  try {
    const response = await api.get('/expert/dashboard/');
    return response.data;
  } catch (error) {
    console.error("Error fetching expert dashboard data:", error);
    throw error;
  }
};

