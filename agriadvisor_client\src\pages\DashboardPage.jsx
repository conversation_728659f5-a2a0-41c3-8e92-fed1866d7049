// src/pages/DashboardPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ge, ProgressBar } from "react-bootstrap";
import { Link } from "react-router-dom";
import StatCard from "../components/StatCard";
import PaymentModal from "../components/PaymentModal";
import { getDashboardStats } from "../services/dashboardService";
import useAuth from "../hooks/useAuth";

// Premium Admin Dashboard CSS and Animations
const premiumCSS = `
  @keyframes adminSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes statCardPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }
  }

  @keyframes revenueGlow {
    0%, 100% {
      text-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
    }
    50% {
      text-shadow: 0 0 20px rgba(67, 233, 123, 0.8);
    }
  }

  @keyframes bookingRowSlide {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .premium-admin-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: adminSlideIn 0.6s ease-out;
  }

  .premium-admin-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .admin-stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    animation: statCardPulse 3s infinite;
  }

  .admin-stat-card:hover {
    animation: none;
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
  }

  .revenue-display {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    animation: revenueGlow 2s infinite;
  }

  .booking-row {
    border: none;
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 15px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    transition: all 0.3s ease;
    animation: bookingRowSlide 0.6s ease-out;
  }

  .booking-row:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
  }

  .status-confirmed {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
  }

  .status-pending {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
  }

  .farmer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 10px;
    border: 2px solid white;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
  }

  .live-session-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
  }

  .live-session-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
  }

  .admin-overview {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 25px rgba(252, 182, 159, 0.3);
  }

  .system-health {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .slide-in-up {
    animation: adminSlideIn 0.6s ease-out;
  }

  .quick-action-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    margin: 5px;
  }

  .quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(67, 233, 123, 0.4);
    background: linear-gradient(135deg, #38f9d7 0%, #43e97b 100%);
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px' // Add padding to account for sticky header
  }
};

/**
 * Main dashboard page for the Admin.
 * Displays summary statistics and recent bookings.
 */
const DashboardPage = () => {
  console.log("DashboardPage rendering");

  // State for data, loading, and errors
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // State for controlling the payment modal
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);

  const { logout } = useAuth();

  // Fetch all dashboard data on component mount
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const data = await getDashboardStats();
        setStats(data);
      } catch (err) {
        setError("Failed to load dashboard data. Please try again later.");
        console.error("Dashboard error:", err);
        // Set default stats to prevent blank screen
        setStats({
          total_experts: 0,
          total_services: 0,
          total_bookings: 0,
          total_revenue: 0,
          recent_bookings: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []); // Empty array ensures this runs only once

  /**
   * Prepares booking data and opens the payment modal.
   * @param {object} booking - The full booking object from the API.
   */
  const handleShowPaymentModal = (booking) => {
    const bookingDetails = {
      serviceName: booking.service.name,
      date: new Date(booking.booking_time).toLocaleDateString(),
      time: new Date(booking.booking_time).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      amount: parseFloat(booking.service.price),
    };
    setSelectedBooking(bookingDetails);
    setShowPaymentModal(true);
  };

  // --- Conditional Rendering ---
  if (loading) {
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h3 className="mt-3 mb-2">Loading Admin Dashboard</h3>
          <p className="mb-0 opacity-75">Gathering system insights and analytics...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <div className="premium-admin-card p-4">
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Dashboard Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </div>
      </Container>
    );
  }

  // --- ADD THIS GUARD CLAUSE ---
  // If there are no stats loaded yet (even after loading is false),
  // don't try to render the components that depend on it.
  if (!stats) {
    return (
      <div>
        <div style={premiumStyles.pageHeader}>
          <Container>
            <h1 className="display-4 fw-bold mb-2">
              <i className="bi bi-speedometer2 me-3"></i>
              Admin Dashboard
            </h1>
            <p className="lead mb-0 opacity-90">No statistics data available.</p>
          </Container>
        </div>
      </div>
    );
  }
  // --- END OF GUARD CLAUSE ---

  // --- Main Render ---
  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-speedometer2 me-3"></i>
                Admin Dashboard
              </h1>
              <p className="lead mb-0 opacity-90">
                System overview and management center
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    <i className="bi bi-shield-check me-2"></i>
                    Administrator
                  </Badge>
                </div>
                <small className="opacity-75">System Access</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container fluid>
        {/* Premium System Overview */}
        <Row className="mb-4">
          <Col>
            <div className="admin-overview slide-in-up">
              <Row className="text-center">
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#667eea' }}>
                    <i className="bi bi-graph-up-arrow"></i>
                  </div>
                  <h5 className="mb-1">Growing</h5>
                  <small className="text-muted">Platform Health</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                    <i className="bi bi-shield-check-fill"></i>
                  </div>
                  <h5 className="mb-1">Secure</h5>
                  <small className="text-muted">System Status</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                    <i className="bi bi-people-fill"></i>
                  </div>
                  <h5 className="mb-1">Active</h5>
                  <small className="text-muted">User Base</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#fee140' }}>
                    <i className="bi bi-star-fill"></i>
                  </div>
                  <h5 className="mb-1">Premium</h5>
                  <small className="text-muted">Service Level</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        <PremiumStatCardsRow stats={stats} />

        <Row className="mt-4">
          <Col lg={8} className="mb-4">
            <PremiumRecentBookingsTable bookings={stats?.recent_bookings} onPayNowClick={handleShowPaymentModal} />
          </Col>
          <Col lg={4} className="mb-4">
            <PremiumLiveSessionCard />

            {/* Premium Quick Actions */}
            <Card className="premium-admin-card mt-4 slide-in-up">
              <div style={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                color: 'white',
                padding: '1.5rem',
                borderRadius: '25px 25px 0 0'
              }}>
                <h5 className="mb-0">
                  <i className="bi bi-lightning-charge-fill me-2"></i>
                  Quick Actions
                </h5>
              </div>
              <Card.Body className="p-4">
                <div className="d-flex flex-wrap justify-content-center">
                  <Button as={Link} to="/admin/experts" className="quick-action-btn">
                    <i className="bi bi-people me-2"></i>
                    Manage Experts
                  </Button>
                  <Button as={Link} to="/admin/services" className="quick-action-btn">
                    <i className="bi bi-grid me-2"></i>
                    Manage Services
                  </Button>
                  <Button as={Link} to="/admin/bookings" className="quick-action-btn">
                    <i className="bi bi-calendar-check me-2"></i>
                    View Bookings
                  </Button>
                  <Button as={Link} to="/admin/farmers" className="quick-action-btn">
                    <i className="bi bi-house-heart me-2"></i>
                    Manage Farmers
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* System Health Section - Compact */}
        <Row className="mt-5 mb-4">
          <Col lg={8}>
            <Card className="premium-admin-card slide-in-up">
              <div style={{
                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                color: 'white',
                padding: '1rem',
                borderRadius: '20px 20px 0 0'
              }}>
                <h6 className="mb-0">
                  <i className="bi bi-cpu me-2"></i>
                  System Health
                </h6>
              </div>
              <Card.Body className="p-3">
                <Row>
                  <Col md={4}>
                    <div className="mb-2">
                      <div className="d-flex justify-content-between mb-1">
                        <small className="fw-semibold">Server</small>
                        <small>98%</small>
                      </div>
                      <ProgressBar now={98} style={{ height: '6px' }} variant="success" />
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-2">
                      <div className="d-flex justify-content-between mb-1">
                        <small className="fw-semibold">Database</small>
                        <small>95%</small>
                      </div>
                      <ProgressBar now={95} style={{ height: '6px' }} variant="info" />
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="mb-2">
                      <div className="d-flex justify-content-between mb-1">
                        <small className="fw-semibold">Users</small>
                        <small>92%</small>
                      </div>
                      <ProgressBar now={92} style={{ height: '6px' }} variant="warning" />
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Admin help and support"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>

      <PaymentModal
        show={showPaymentModal}
        onHide={() => setShowPaymentModal(false)}
        bookingDetails={selectedBooking}
      />
    </div>
  );
};

// --- Premium Child Components ---

const PremiumStatCardsRow = ({ stats }) => {
  const formatCurrency = (number) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number || 0);

  return (
    <Row className="mb-4">
      <Col md={6} lg={3} className="mb-4">
        <Card className="admin-stat-card text-center h-100 slide-in-up">
          <Card.Body className="p-4">
            <div className="mb-3">
              <i className="bi bi-people" style={{ fontSize: "3rem", opacity: 0.9 }}></i>
            </div>
            <div className="h3 mb-2 fw-bold">{stats?.total_experts || 0}</div>
            <h6 className="mb-2 opacity-90">Expert Advisors</h6>
            <p className="mb-0 opacity-75 small">Agricultural specialists</p>
            <Badge bg="light" text="dark" className="mt-2 px-3 py-1" style={{ borderRadius: '15px' }}>
              Active
            </Badge>
          </Card.Body>
        </Card>
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <Card className="admin-stat-card text-center h-100 slide-in-up" style={{ animationDelay: '0.1s' }}>
          <Card.Body className="p-4">
            <div className="mb-3">
              <i className="bi bi-grid" style={{ fontSize: "3rem", opacity: 0.9 }}></i>
            </div>
            <div className="h3 mb-2 fw-bold">{stats?.total_services || 0}</div>
            <h6 className="mb-2 opacity-90">Services</h6>
            <p className="mb-0 opacity-75 small">Available consultations</p>
            <Badge bg="light" text="dark" className="mt-2 px-3 py-1" style={{ borderRadius: '15px' }}>
              Live
            </Badge>
          </Card.Body>
        </Card>
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <Card className="admin-stat-card text-center h-100 slide-in-up" style={{ animationDelay: '0.2s' }}>
          <Card.Body className="p-4">
            <div className="mb-3">
              <i className="bi bi-calendar-check" style={{ fontSize: "3rem", opacity: 0.9 }}></i>
            </div>
            <div className="h3 mb-2 fw-bold">{stats?.total_bookings || 0}</div>
            <h6 className="mb-2 opacity-90">Total Bookings</h6>
            <p className="mb-0 opacity-75 small">Completed sessions</p>
            <Badge bg="light" text="dark" className="mt-2 px-3 py-1" style={{ borderRadius: '15px' }}>
              Growing
            </Badge>
          </Card.Body>
        </Card>
      </Col>
      <Col md={6} lg={3} className="mb-4">
        <Card className="admin-stat-card text-center h-100 slide-in-up" style={{ animationDelay: '0.3s' }}>
          <Card.Body className="p-4">
            <div className="mb-3">
              <i className="bi bi-cash-stack" style={{ fontSize: "3rem", opacity: 0.9 }}></i>
            </div>
            <div className="revenue-display h3 mb-2 fw-bold">
              {formatCurrency(stats?.total_revenue)}
            </div>
            <h6 className="mb-2 opacity-90">Total Revenue</h6>
            <p className="mb-0 opacity-75 small">Platform earnings</p>
            <Badge bg="light" text="dark" className="mt-2 px-3 py-1" style={{ borderRadius: '15px' }}>
              Profitable
            </Badge>
          </Card.Body>
        </Card>
      </Col>
    </Row>
  );
};

const PremiumRecentBookingsTable = ({ bookings, onPayNowClick }) => (
  <Card className="premium-admin-card slide-in-up">
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      padding: '1.5rem',
      borderRadius: '25px 25px 0 0'
    }}>
      <h4 className="mb-0">
        <i className="bi bi-calendar-event me-2"></i>
        Recent Bookings
      </h4>
    </div>
    <Card.Body className="p-4">
      {bookings && bookings.length > 0 ? (
        <div className="bookings-list">
          {/* Table Headers */}
          <Row className="align-items-center mb-3 pb-2" style={{
            borderBottom: '2px solid #e9ecef',
            fontWeight: '600',
            color: '#495057'
          }}>
            <Col md={3}>
              <small className="text-uppercase">
                <i className="bi bi-person me-1"></i>
                Farmer
              </small>
            </Col>
            <Col md={3}>
              <small className="text-uppercase">
                <i className="bi bi-briefcase me-1"></i>
                Service
              </small>
            </Col>
            <Col md={3} className="text-center">
              <small className="text-uppercase">
                <i className="bi bi-calendar me-1"></i>
                Date & Time
              </small>
            </Col>
            <Col md={2} className="text-center">
              <small className="text-uppercase">
                <i className="bi bi-flag me-1"></i>
                Status
              </small>
            </Col>
            <Col md={1} className="text-center">
              <small className="text-uppercase">
                <i className="bi bi-gear me-1"></i>
                Action
              </small>
            </Col>
          </Row>

          {/* Booking Rows */}
          {bookings.map((booking, index) => (
            <div
              key={booking.id}
              className="booking-row"
              style={{
                animationDelay: `${index * 0.1}s`,
                padding: '12px 0',
                borderBottom: '1px solid #f8f9fa'
              }}
            >
              <Row className="align-items-center">
                <Col md={3}>
                  <div className="d-flex align-items-center">
                    <div className="farmer-avatar">
                      {booking.farmer.first_name[0]}{booking.farmer.last_name[0]}
                    </div>
                    <div>
                      <div className="fw-semibold" style={{ color: '#333' }}>
                        {booking.farmer.first_name} {booking.farmer.last_name}
                      </div>
                      <small className="text-muted">Farmer</small>
                    </div>
                  </div>
                </Col>
                <Col md={3}>
                  <div>
                    <div className="fw-semibold" style={{ color: '#333' }}>
                      {booking.service.name}
                    </div>
                    <small className="text-muted">
                      <i className="bi bi-briefcase me-1"></i>
                      Agricultural Service
                    </small>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="text-center">
                    <div className="fw-semibold" style={{ color: '#333' }}>
                      {new Date(booking.booking_time).toLocaleDateString()}
                    </div>
                    <small className="text-muted">
                      {new Date(booking.booking_time).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </small>
                  </div>
                </Col>
                <Col md={2}>
                  <div className="text-center">
                    <span className={`status-badge ${booking.status === "confirmed" ? "status-confirmed" : "status-pending"}`}>
                      {booking.status.replace("_", " ").toUpperCase()}
                    </span>
                  </div>
                </Col>
                <Col md={1}>
                  <div className="text-center">
                    {booking.status === "pending_payment" ? (
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => onPayNowClick(booking)}
                        className="rounded-pill"
                        title="Process payment"
                      >
                        <i className="bi bi-credit-card"></i>
                      </Button>
                    ) : (
                      <Badge bg="success" className="px-2 py-1" style={{ borderRadius: '10px' }}>
                        <i className="bi bi-check-circle"></i>
                      </Badge>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-5">
          <div className="mb-3">
            <i className="bi bi-calendar-x text-muted" style={{ fontSize: '3rem' }}></i>
          </div>
          <h5 className="text-muted">No recent bookings</h5>
          <p className="text-muted mb-4">
            Booking activity will appear here as farmers schedule consultations
          </p>
          <Button
            variant="outline-primary"
            as={Link}
            to="/admin/bookings"
            className="rounded-pill px-4"
          >
            <i className="bi bi-calendar-check me-2"></i>
            View All Bookings
          </Button>
        </div>
      )}
    </Card.Body>
  </Card>
);

const PremiumLiveSessionCard = () => (
  <Card className="live-session-card h-100 slide-in-up">
    <div style={{
      background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      color: 'white',
      padding: '1.5rem',
      borderRadius: '25px 25px 0 0',
      position: 'relative',
      zIndex: 1
    }}>
      <h5 className="mb-0">
        <i className="bi bi-camera-video me-2"></i>
        Live Session Monitor
      </h5>
    </div>
    <Card.Body className="text-center d-flex flex-column justify-content-center align-items-center p-4" style={{ position: 'relative', zIndex: 1 }}>
      <div className="mb-3">
        <i className="bi bi-camera-video-off" style={{ fontSize: "3rem", opacity: 0.7 }}></i>
      </div>
      <h5 className="mb-2">No Active Sessions</h5>
      <p className="mb-4 opacity-75">
        Monitor live consultation sessions between experts and farmers
      </p>
      <Button
        variant="light"
        disabled
        className="rounded-pill px-4"
        style={{ color: '#333', fontWeight: 'bold' }}
      >
        <i className="bi bi-play-circle me-2"></i>
        Monitor Sessions
      </Button>

      {/* Session Stats */}
      <div className="mt-4 w-100">
        <Row className="text-center">
          <Col xs={6}>
            <div className="p-2">
              <div className="h5 mb-1">0</div>
              <small className="opacity-75">Active Now</small>
            </div>
          </Col>
          <Col xs={6}>
            <div className="p-2">
              <div className="h5 mb-1">24</div>
              <small className="opacity-75">Today</small>
            </div>
          </Col>
        </Row>
      </div>
    </Card.Body>
  </Card>
);

export default DashboardPage;


