// src/layouts/FarmerLayout.jsx

import React from "react";
import { Outlet, NavLink } from "react-router-dom";
import { Navbar, Container, Nav } from "react-bootstrap";
import useAuth from "../hooks/useAuth";

import AnnouncementBanner from "../components/AnnouncementBanner";

const FarmerLayout = () => {
  const { logout } = useAuth();

  return (
    <div>
      <Navbar bg="success" variant="dark" expand="lg" sticky="top" style={{ zIndex: 1020 }}>
        <Container>
          <Navbar.Brand as={NavLink} to="/">
            <i className="bi bi-tree-fill me-2"></i>AgriAdvisor
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              <Nav.Link as={NavLink} to="/farmer/profile">
                My Profile
              </Nav.Link>
              <Nav.Link as={NavLink} to="/">
                My Dashboard
              </Nav.Link>
              <Nav.Link as={NavLink} to="/browse-services">
                Book a Service
              </Nav.Link>
              <Nav.Link as={NavLink} to="/my-bookings">
                My Bookings
              </Nav.Link>
            </Nav>
            <Nav>
              <Nav.Link onClick={logout}>Logout</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      <AnnouncementBanner />
      <Container className="mt-4">
        <Outlet /> {/* Farmer-specific pages will be rendered here */}
      </Container>
    </div>
  );
};

export default FarmerLayout;



