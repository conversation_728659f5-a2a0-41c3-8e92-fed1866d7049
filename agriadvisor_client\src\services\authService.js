// src/services/authService.js

import api from "./api"; // Our configured Axios instance

export const loginUser = async (credentials) => {
  // credentials will be an object like { username: '...', password: '...' }
  const response = await api.post("/accounts/token/", credentials);
  return response.data;
};



export const registerUser = async (userData) => {
  // userData will be { username, password, email, tenant_name, ... }
  const response = await api.post("/accounts/register/", userData);
  return response.data;
};


export const getMyProfile = async () => {
  const response = await api.get("/accounts/me/");
  return response.data;
};



export const forgotPassword = async (emailData) => {
  const response = await api.post("/accounts/password-reset/", emailData);
  return response.data;
};

export const resetPassword = async (resetData) => {
  const response = await api.post("/accounts/password-reset/confirm/", resetData);
  return response.data;
};



export const changePassword = async (passwordData) => {
  // passwordData will be { old_password, new_password }
  const response = await api.post("/accounts/me/change-password/", passwordData);
  return response.data;
};

