// src/components/StatCard.jsx

import React from "react";
import { Card } from "react-bootstrap";

// Basic inline styles to replicate the gradient from the template
const cardStyle = {
  background: "linear-gradient(135deg, #1E90FF, #87CEEB)", // primary-blue to light-blue
  color: "white",
  borderRadius: "15px",
  boxShadow: "0 4px 15px rgba(0,0,0,0.1)",
  border: "none",
};

const iconStyle = {
  fontSize: "2.5rem", // A bit larger for impact
  opacity: 0.8,
};

const StatCard = ({ title, value, icon }) => {
  return (
    <Card style={cardStyle} className="h-100">
      <Card.Body>
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="card-title">{title}</h5>
            <h2>{value}</h2>
          </div>
          {/* The icon class will be passed as a prop, e.g., "bi bi-people" */}
          <i className={icon} style={iconStyle}></i>
        </div>
      </Card.Body>
    </Card>
  );
};

export default StatCard;



