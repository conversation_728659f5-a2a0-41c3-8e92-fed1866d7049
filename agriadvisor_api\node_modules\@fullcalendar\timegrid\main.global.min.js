/*!
FullCalendar v5.11.5
Docs & License: https://fullcalendar.io/
(c) 2022 Adam Shaw
*/
var FullCalendarTimeGrid=function(e,t,n){"use strict";var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.getKeyInfo=function(){return{allDay:{},timed:{}}},n.prototype.getKeysForDateSpan=function(e){return e.allDay?["allDay"]:["timed"]},n.prototype.getKeysForEventDef=function(e){return e.allDay?t.hasBgRendering(e)?["timed","allDay"]:["allDay"]:["timed"]},n}(t.Splitter),s=t.createFormatter({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"});function l(e){var n=["fc-timegrid-slot","fc-timegrid-slot-label",e.isLabeled?"fc-scrollgrid-shrink":"fc-timegrid-slot-minor"];return t.createElement(t.ViewContextType.Consumer,null,(function(r){if(!e.isLabeled)return t.createElement("td",{className:n.join(" "),"data-time":e.isoTimeStr});var o=r.dateEnv,i=r.options,a=r.viewApi,l=null==i.slotLabelFormat?s:Array.isArray(i.slotLabelFormat)?t.createFormatter(i.slotLabelFormat[0]):t.createFormatter(i.slotLabelFormat),d={level:0,time:e.time,date:o.toDate(e.date),view:a,text:o.format(e.date,l)};return t.createElement(t.RenderHook,{hookProps:d,classNames:i.slotLabelClassNames,content:i.slotLabelContent,defaultContent:c,didMount:i.slotLabelDidMount,willUnmount:i.slotLabelWillUnmount},(function(r,o,i,a){return t.createElement("td",{ref:r,className:n.concat(o).join(" "),"data-time":e.isoTimeStr},t.createElement("div",{className:"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame"},t.createElement("div",{className:"fc-timegrid-slot-label-cushion fc-scrollgrid-shrink-cushion",ref:i},a)))}))}))}function c(e){return e.text}var d=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.render=function(){return this.props.slatMetas.map((function(e){return t.createElement("tr",{key:e.key},t.createElement(l,i({},e)))}))},n}(t.BaseComponent),u=t.createFormatter({week:"short"}),p=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.allDaySplitter=new a,n.headerElRef=t.createRef(),n.rootElRef=t.createRef(),n.scrollerElRef=t.createRef(),n.state={slatCoords:null},n.handleScrollTopRequest=function(e){var t=n.scrollerElRef.current;t&&(t.scrollTop=e)},n.renderHeadAxis=function(e,r){void 0===r&&(r="");var o=n.context.options,a=n.props.dateProfile.renderRange,s=1===t.diffDays(a.start,a.end)?t.buildNavLinkAttrs(n.context,a.start,"week"):{};return o.weekNumbers&&"day"===e?t.createElement(t.WeekNumberRoot,{date:a.start,defaultFormat:u},(function(e,n,o,a){return t.createElement("th",{ref:e,"aria-hidden":!0,className:["fc-timegrid-axis","fc-scrollgrid-shrink"].concat(n).join(" ")},t.createElement("div",{className:"fc-timegrid-axis-frame fc-scrollgrid-shrink-frame fc-timegrid-axis-frame-liquid",style:{height:r}},t.createElement("a",i({ref:o,className:"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner"},s),a)))})):t.createElement("th",{"aria-hidden":!0,className:"fc-timegrid-axis"},t.createElement("div",{className:"fc-timegrid-axis-frame",style:{height:r}}))},n.renderTableRowAxis=function(e){var r=n.context,o=r.options,i=r.viewApi,a={text:o.allDayText,view:i};return t.createElement(t.RenderHook,{hookProps:a,classNames:o.allDayClassNames,content:o.allDayContent,defaultContent:f,didMount:o.allDayDidMount,willUnmount:o.allDayWillUnmount},(function(n,r,o,i){return t.createElement("td",{ref:n,"aria-hidden":!0,className:["fc-timegrid-axis","fc-scrollgrid-shrink"].concat(r).join(" ")},t.createElement("div",{className:"fc-timegrid-axis-frame fc-scrollgrid-shrink-frame"+(null==e?" fc-timegrid-axis-frame-liquid":""),style:{height:e}},t.createElement("span",{className:"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner",ref:o},i)))}))},n.handleSlatCoords=function(e){n.setState({slatCoords:e})},n}return o(n,e),n.prototype.renderSimpleLayout=function(e,n,r){var o=this.context,i=this.props,a=[],s=t.getStickyHeaderDates(o.options);return e&&a.push({type:"header",key:"header",isSticky:s,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),n&&(a.push({type:"body",key:"all-day",chunk:{content:n}}),a.push({type:"body",key:"all-day-divider",outerContent:t.createElement("tr",{role:"presentation",className:"fc-scrollgrid-section"},t.createElement("td",{className:"fc-timegrid-divider "+o.theme.getClass("tableCellShaded")}))})),a.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(o.options.expandRows),chunk:{scrollerElRef:this.scrollerElRef,content:r}}),t.createElement(t.ViewRoot,{viewSpec:o.viewSpec,elRef:this.rootElRef},(function(e,n){return t.createElement("div",{className:["fc-timegrid"].concat(n).join(" "),ref:e},t.createElement(t.SimpleScrollGrid,{liquid:!i.isHeightAuto&&!i.forPrint,collapsibleWidth:i.forPrint,cols:[{width:"shrink"}],sections:a}))}))},n.prototype.renderHScrollLayout=function(e,n,r,o,i,a,s){var l=this,c=this.context.pluginHooks.scrollGridImpl;if(!c)throw new Error("No ScrollGrid implementation");var u=this.context,p=this.props,f=!p.forPrint&&t.getStickyHeaderDates(u.options),m=!p.forPrint&&t.getStickyFooterScrollbar(u.options),h=[];e&&h.push({type:"header",key:"header",isSticky:f,syncRowHeights:!0,chunks:[{key:"axis",rowContent:function(e){return t.createElement("tr",{role:"presentation"},l.renderHeadAxis("day",e.rowSyncHeights[0]))}},{key:"cols",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),n&&(h.push({type:"body",key:"all-day",syncRowHeights:!0,chunks:[{key:"axis",rowContent:function(e){return t.createElement("tr",{role:"presentation"},l.renderTableRowAxis(e.rowSyncHeights[0]))}},{key:"cols",content:n}]}),h.push({key:"all-day-divider",type:"body",outerContent:t.createElement("tr",{role:"presentation",className:"fc-scrollgrid-section"},t.createElement("td",{colSpan:2,className:"fc-timegrid-divider "+u.theme.getClass("tableCellShaded")}))}));var g=u.options.nowIndicator;return h.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(u.options.expandRows),chunks:[{key:"axis",content:function(e){return t.createElement("div",{className:"fc-timegrid-axis-chunk"},t.createElement("table",{"aria-hidden":!0,style:{height:e.expandRows?e.clientHeight:""}},e.tableColGroupNode,t.createElement("tbody",null,t.createElement(d,{slatMetas:a}))),t.createElement("div",{className:"fc-timegrid-now-indicator-container"},t.createElement(t.NowTimer,{unit:g?"minute":"day"},(function(e){var n=g&&s&&s.safeComputeTop(e);return"number"==typeof n?t.createElement(t.NowIndicatorRoot,{isAxis:!0,date:e},(function(e,r,o,i){return t.createElement("div",{ref:e,className:["fc-timegrid-now-indicator-arrow"].concat(r).join(" "),style:{top:n}},i)})):null}))))}},{key:"cols",scrollerElRef:this.scrollerElRef,content:r}]}),m&&h.push({key:"footer",type:"footer",isSticky:!0,chunks:[{key:"axis",content:t.renderScrollShim},{key:"cols",content:t.renderScrollShim}]}),t.createElement(t.ViewRoot,{viewSpec:u.viewSpec,elRef:this.rootElRef},(function(e,n){return t.createElement("div",{className:["fc-timegrid"].concat(n).join(" "),ref:e},t.createElement(c,{liquid:!p.isHeightAuto&&!p.forPrint,collapsibleWidth:!1,colGroups:[{width:"shrink",cols:[{width:"shrink"}]},{cols:[{span:o,minWidth:i}]}],sections:h}))}))},n.prototype.getAllDayMaxEventProps=function(){var e=this.context.options,t=e.dayMaxEvents,n=e.dayMaxEventRows;return!0!==t&&!0!==n||(t=void 0,n=5),{dayMaxEvents:t,dayMaxEventRows:n}},n}(t.DateComponent);function f(e){return e.text}var m=function(){function e(e,t,n){this.positions=e,this.dateProfile=t,this.slotDuration=n}return e.prototype.safeComputeTop=function(e){var n=this.dateProfile;if(t.rangeContainsMarker(n.currentRange,e)){var r=t.startOfDay(e),o=e.valueOf()-r.valueOf();if(o>=t.asRoughMs(n.slotMinTime)&&o<t.asRoughMs(n.slotMaxTime))return this.computeTimeTop(t.createDuration(o))}return null},e.prototype.computeDateTop=function(e,n){return n||(n=t.startOfDay(e)),this.computeTimeTop(t.createDuration(e.valueOf()-n.valueOf()))},e.prototype.computeTimeTop=function(e){var n,r,o=this.positions,i=this.dateProfile,a=o.els.length,s=(e.milliseconds-t.asRoughMs(i.slotMinTime))/t.asRoughMs(this.slotDuration);return s=Math.max(0,s),s=Math.min(a,s),n=Math.floor(s),r=s-(n=Math.min(n,a-1)),o.tops[n]+o.getHeight(n)*r},e}(),h=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.render=function(){var e=this.props,n=this.context,r=n.options,o=e.slatElRefs;return t.createElement("tbody",null,e.slatMetas.map((function(a,s){var c={time:a.time,date:n.dateEnv.toDate(a.date),view:n.viewApi},d=["fc-timegrid-slot","fc-timegrid-slot-lane",a.isLabeled?"":"fc-timegrid-slot-minor"];return t.createElement("tr",{key:a.key,ref:o.createRef(a.key)},e.axis&&t.createElement(l,i({},a)),t.createElement(t.RenderHook,{hookProps:c,classNames:r.slotLaneClassNames,content:r.slotLaneContent,didMount:r.slotLaneDidMount,willUnmount:r.slotLaneWillUnmount},(function(e,n,r,o){return t.createElement("td",{ref:e,className:d.concat(n).join(" "),"data-time":a.isoTimeStr},o)})))})))},n}(t.BaseComponent),g=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.rootElRef=t.createRef(),n.slatElRefs=new t.RefMap,n}return o(n,e),n.prototype.render=function(){var e=this.props,n=this.context;return t.createElement("div",{ref:this.rootElRef,className:"fc-timegrid-slots"},t.createElement("table",{"aria-hidden":!0,className:n.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},e.tableColGroupNode,t.createElement(h,{slatElRefs:this.slatElRefs,axis:e.axis,slatMetas:e.slatMetas})))},n.prototype.componentDidMount=function(){this.updateSizing()},n.prototype.componentDidUpdate=function(){this.updateSizing()},n.prototype.componentWillUnmount=function(){this.props.onCoords&&this.props.onCoords(null)},n.prototype.updateSizing=function(){var e,n=this.context,r=this.props;r.onCoords&&null!==r.clientWidth&&(this.rootElRef.current.offsetHeight&&r.onCoords(new m(new t.PositionCache(this.rootElRef.current,(e=this.slatElRefs.currentMap,r.slatMetas.map((function(t){return e[t.key]}))),!1,!0),this.props.dateProfile,n.options.slotDuration)))},n}(t.BaseComponent);function v(e,t){var n,r=[];for(n=0;n<t;n+=1)r.push([]);if(e)for(n=0;n<e.length;n+=1)r[e[n].col].push(e[n]);return r}function y(e,t){var n=[];if(e){for(a=0;a<t;a+=1)n[a]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(var r=0,o=e.segs;r<o.length;r++){var i=o[r];n[i.col].segs.push(i)}}else for(var a=0;a<t;a+=1)n[a]=null;return n}var S=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.rootElRef=t.createRef(),n}return o(n,e),n.prototype.render=function(){var e=this,n=this.props;return t.createElement(t.MoreLinkRoot,{allDayDate:null,moreCnt:n.hiddenSegs.length,allSegs:n.hiddenSegs,hiddenSegs:n.hiddenSegs,alignmentElRef:this.rootElRef,defaultContent:E,extraDateSpan:n.extraDateSpan,dateProfile:n.dateProfile,todayRange:n.todayRange,popoverContent:function(){return T(n.hiddenSegs,n)}},(function(r,o,i,a,s,l,c,d){return t.createElement("a",{ref:function(n){t.setRef(r,n),t.setRef(e.rootElRef,n)},className:["fc-timegrid-more-link"].concat(o).join(" "),style:{top:n.top,bottom:n.bottom},onClick:s,title:l,"aria-expanded":c,"aria-controls":d},t.createElement("div",{ref:i,className:"fc-timegrid-more-link-inner fc-sticky"},a))}))},n}(t.BaseComponent);function E(e){return e.shortText}function R(e,n,r){var o=new t.SegHierarchy;null!=n&&(o.strictOrder=n),null!=r&&(o.maxStackCnt=r);var a,s,l,c=o.addSegs(e),d=t.groupIntersectingEntries(c),u=function(e){var n=e.entriesByLevel,r=b((function(e,t){return e+":"+t}),(function(o,a){var s=D(function(e,n,r){for(var o=e.levelCoords,i=e.entriesByLevel,a=i[n][r],s=o[n]+a.thickness,l=o.length,c=n;c<l&&o[c]<s;c+=1);for(;c<l;c+=1){for(var d=i[c],u=void 0,p=t.binarySearch(d,a.span.start,t.getEntrySpanEnd),f=p[0]+p[1],m=f;(u=d[m])&&u.span.start<a.span.end;)m+=1;if(f<m)return{level:c,lateralStart:f,lateralEnd:m}}return null}(e,o,a),r),l=n[o][a];return[i(i({},l),{nextLevelNodes:s[0]}),l.thickness+s[1]]}));return D(n.length?{level:0,lateralStart:0,lateralEnd:n[0].length}:null,r)[0]}(o);return a=u,s=1,l=b((function(e,n,r){return t.buildEntryKey(e)}),(function(e,t,n){var r,o=e.nextLevelNodes,a=e.thickness,c=a+n,d=a/c,u=[];if(o.length)for(var p=0,f=o;p<f.length;p++){var m=f[p];if(void 0===r)r=(h=l(m,t,c))[0],u.push(h[1]);else{var h=l(m,r,0);u.push(h[1])}}else r=s;var g=(r-t)*d;return[r-g,i(i({},e),{thickness:g,nextLevelNodes:u})]})),{segRects:function(e){var n=[],r=b((function(e,n,r){return t.buildEntryKey(e)}),(function(e,t,r){var a=i(i({},e),{levelCoord:t,stackDepth:r,stackForward:0});return n.push(a),a.stackForward=o(e.nextLevelNodes,t+e.thickness,r+1)+1}));function o(e,t,n){for(var o=0,i=0,a=e;i<a.length;i++){var s=a[i];o=Math.max(r(s,t,n),o)}return o}return o(e,0,0),n}(u=a.map((function(e){return l(e,0,0)[1]}))),hiddenGroups:d}}function D(e,t){if(!e)return[[],0];for(var n=e.level,r=e.lateralStart,o=e.lateralEnd,i=r,a=[];i<o;)a.push(t(n,i)),i+=1;return a.sort(C),[a.map(x),a[0][1]]}function C(e,t){return t[1]-e[1]}function x(e){return e[0]}function b(e,t){var n={};return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var i=e.apply(void 0,r);return i in n?n[i]:n[i]=t.apply(void 0,r)}}function w(e,t,n,r){void 0===n&&(n=null),void 0===r&&(r=0);var o=[];if(n)for(var i=0;i<e.length;i+=1){var a=e[i],s=n.computeDateTop(a.start,t),l=Math.max(s+(r||0),n.computeDateTop(a.end,t));o.push({start:Math.round(s),end:Math.round(l)})}return o}var k=t.createFormatter({hour:"numeric",minute:"2-digit",meridiem:!1}),M=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.render=function(){var e=["fc-timegrid-event","fc-v-event"];return this.props.isShort&&e.push("fc-timegrid-event-short"),t.createElement(t.StandardEvent,i({},this.props,{defaultTimeFormat:k,extraClassNames:e}))},n}(t.BaseComponent),N=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.render=function(){var e=this.props;return t.createElement(t.DayCellContent,{date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,extraHookProps:e.extraHookProps},(function(e,n){return n&&t.createElement("div",{className:"fc-timegrid-col-misc",ref:e},n)}))},n}(t.BaseComponent),P=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.sortEventSegs=t.memoize(t.sortEventSegs),n}return o(n,e),n.prototype.render=function(){var e=this,n=this.props,r=this.context,o=r.options.selectMirror,a=n.eventDrag&&n.eventDrag.segs||n.eventResize&&n.eventResize.segs||o&&n.dateSelectionSegs||[],s=n.eventDrag&&n.eventDrag.affectedInstances||n.eventResize&&n.eventResize.affectedInstances||{},l=this.sortEventSegs(n.fgEventSegs,r.options.eventOrder);return t.createElement(t.DayCellRoot,{elRef:n.elRef,date:n.date,dateProfile:n.dateProfile,todayRange:n.todayRange,extraHookProps:n.extraHookProps},(function(r,c,d){return t.createElement("td",i({ref:r,role:"gridcell",className:["fc-timegrid-col"].concat(c,n.extraClassNames||[]).join(" ")},d,n.extraDataAttrs),t.createElement("div",{className:"fc-timegrid-col-frame"},t.createElement("div",{className:"fc-timegrid-col-bg"},e.renderFillSegs(n.businessHourSegs,"non-business"),e.renderFillSegs(n.bgEventSegs,"bg-event"),e.renderFillSegs(n.dateSelectionSegs,"highlight")),t.createElement("div",{className:"fc-timegrid-col-events"},e.renderFgSegs(l,s,!1,!1,!1)),t.createElement("div",{className:"fc-timegrid-col-events"},e.renderFgSegs(a,{},Boolean(n.eventDrag),Boolean(n.eventResize),Boolean(o))),t.createElement("div",{className:"fc-timegrid-now-indicator-container"},e.renderNowIndicator(n.nowIndicatorSegs)),t.createElement(N,{date:n.date,dateProfile:n.dateProfile,todayRange:n.todayRange,extraHookProps:n.extraHookProps})))}))},n.prototype.renderFgSegs=function(e,t,n,r,o){var i=this.props;return i.forPrint?T(e,i):this.renderPositionedFgSegs(e,t,n,r,o)},n.prototype.renderPositionedFgSegs=function(e,n,r,o,a){var s=this,l=this.context.options,c=l.eventMaxStack,d=l.eventShortHeight,u=l.eventOrderStrict,p=l.eventMinHeight,f=this.props,m=f.date,h=f.slatCoords,g=f.eventSelection,v=f.todayRange,y=f.nowDate,S=r||o||a,E=function(e,t,n,r){for(var o=[],i=[],a=0;a<e.length;a+=1){var s=t[a];s?o.push({index:a,thickness:1,span:s}):i.push(e[a])}for(var l=R(o,n,r),c=l.segRects,d=l.hiddenGroups,u=[],p=0,f=c;p<f.length;p++){var m=f[p];u.push({seg:e[m.index],rect:m})}for(var h=0,g=i;h<g.length;h++){var v=g[h];u.push({seg:v,rect:null})}return{segPlacements:u,hiddenGroups:d}}(e,w(e,m,h,p),u,c),D=E.segPlacements,C=E.hiddenGroups;return t.createElement(t.Fragment,null,this.renderHiddenGroups(C,e),D.map((function(e){var l=e.seg,c=e.rect,u=l.eventRange.instance.instanceId,p=S||Boolean(!n[u]&&c),f=H(c&&c.span),m=!S&&c?s.computeSegHStyle(c):{left:0,right:0},h=Boolean(c)&&c.stackForward>0,E=Boolean(c)&&c.span.end-c.span.start<d;return t.createElement("div",{className:"fc-timegrid-event-harness"+(h?" fc-timegrid-event-harness-inset":""),key:u,style:i(i({visibility:p?"":"hidden"},f),m)},t.createElement(M,i({seg:l,isDragging:r,isResizing:o,isDateSelecting:a,isSelected:u===g,isShort:E},t.getSegMeta(l,v,y))))})))},n.prototype.renderHiddenGroups=function(e,n){var r=this.props,o=r.extraDateSpan,i=r.dateProfile,a=r.todayRange,s=r.nowDate,l=r.eventSelection,c=r.eventDrag,d=r.eventResize;return t.createElement(t.Fragment,null,e.map((function(e){var r,u,p=H(e.span),f=(r=e.entries,u=n,r.map((function(e){return u[e.index]})));return t.createElement(S,{key:t.buildIsoString(t.computeEarliestSegStart(f)),hiddenSegs:f,top:p.top,bottom:p.bottom,extraDateSpan:o,dateProfile:i,todayRange:a,nowDate:s,eventSelection:l,eventDrag:c,eventResize:d})})))},n.prototype.renderFillSegs=function(e,n){var r=this.props,o=this.context,a=w(e,r.date,r.slatCoords,o.options.eventMinHeight).map((function(o,a){var s=e[a];return t.createElement("div",{key:t.buildEventRangeKey(s.eventRange),className:"fc-timegrid-bg-harness",style:H(o)},"bg-event"===n?t.createElement(t.BgEvent,i({seg:s},t.getSegMeta(s,r.todayRange,r.nowDate))):t.renderFill(n))}));return t.createElement(t.Fragment,null,a)},n.prototype.renderNowIndicator=function(e){var n=this.props,r=n.slatCoords,o=n.date;return r?e.map((function(e,n){return t.createElement(t.NowIndicatorRoot,{isAxis:!1,date:o,key:n},(function(n,i,a,s){return t.createElement("div",{ref:n,className:["fc-timegrid-now-indicator-line"].concat(i).join(" "),style:{top:r.computeDateTop(e.start,o)}},s)}))})):null},n.prototype.computeSegHStyle=function(e){var t,n,r=this.context,o=r.isRtl,i=r.options.slotEventOverlap,a=e.levelCoord,s=e.levelCoord+e.thickness;i&&(s=Math.min(1,a+2*(s-a))),o?(t=1-s,n=a):(t=a,n=1-s);var l={zIndex:e.stackDepth+1,left:100*t+"%",right:100*n+"%"};return i&&!e.stackForward&&(l[o?"marginLeft":"marginRight"]=20),l},n}(t.BaseComponent);function T(e,n){var r=n.todayRange,o=n.nowDate,a=n.eventSelection,s=n.eventDrag,l=n.eventResize,c=(s?s.affectedInstances:null)||(l?l.affectedInstances:null)||{};return t.createElement(t.Fragment,null,e.map((function(e){var n=e.eventRange.instance.instanceId;return t.createElement("div",{key:n,style:{visibility:c[n]?"hidden":""}},t.createElement(M,i({seg:e,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:n===a,isShort:!1},t.getSegMeta(e,r,o))))})))}function H(e){return e?{top:e.start,bottom:-e.end}:{top:"",bottom:""}}var W=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.splitFgEventSegs=t.memoize(v),n.splitBgEventSegs=t.memoize(v),n.splitBusinessHourSegs=t.memoize(v),n.splitNowIndicatorSegs=t.memoize(v),n.splitDateSelectionSegs=t.memoize(v),n.splitEventDrag=t.memoize(y),n.splitEventResize=t.memoize(y),n.rootElRef=t.createRef(),n.cellElRefs=new t.RefMap,n}return o(n,e),n.prototype.render=function(){var e=this,n=this.props,r=this.context.options.nowIndicator&&n.slatCoords&&n.slatCoords.safeComputeTop(n.nowDate),o=n.cells.length,i=this.splitFgEventSegs(n.fgEventSegs,o),a=this.splitBgEventSegs(n.bgEventSegs,o),s=this.splitBusinessHourSegs(n.businessHourSegs,o),l=this.splitNowIndicatorSegs(n.nowIndicatorSegs,o),c=this.splitDateSelectionSegs(n.dateSelectionSegs,o),d=this.splitEventDrag(n.eventDrag,o),u=this.splitEventResize(n.eventResize,o);return t.createElement("div",{className:"fc-timegrid-cols",ref:this.rootElRef},t.createElement("table",{role:"presentation",style:{minWidth:n.tableMinWidth,width:n.clientWidth}},n.tableColGroupNode,t.createElement("tbody",{role:"presentation"},t.createElement("tr",{role:"row"},n.axis&&t.createElement("td",{"aria-hidden":!0,className:"fc-timegrid-col fc-timegrid-axis"},t.createElement("div",{className:"fc-timegrid-col-frame"},t.createElement("div",{className:"fc-timegrid-now-indicator-container"},"number"==typeof r&&t.createElement(t.NowIndicatorRoot,{isAxis:!0,date:n.nowDate},(function(e,n,o,i){return t.createElement("div",{ref:e,className:["fc-timegrid-now-indicator-arrow"].concat(n).join(" "),style:{top:r}},i)}))))),n.cells.map((function(r,o){return t.createElement(P,{key:r.key,elRef:e.cellElRefs.createRef(r.key),dateProfile:n.dateProfile,date:r.date,nowDate:n.nowDate,todayRange:n.todayRange,extraHookProps:r.extraHookProps,extraDataAttrs:r.extraDataAttrs,extraClassNames:r.extraClassNames,extraDateSpan:r.extraDateSpan,fgEventSegs:i[o],bgEventSegs:a[o],businessHourSegs:s[o],nowIndicatorSegs:l[o],dateSelectionSegs:c[o],eventDrag:d[o],eventResize:u[o],slatCoords:n.slatCoords,eventSelection:n.eventSelection,forPrint:n.forPrint})}))))))},n.prototype.componentDidMount=function(){this.updateCoords()},n.prototype.componentDidUpdate=function(){this.updateCoords()},n.prototype.updateCoords=function(){var e,n=this.props;n.onColCoords&&null!==n.clientWidth&&n.onColCoords(new t.PositionCache(this.rootElRef.current,(e=this.cellElRefs.currentMap,n.cells.map((function(t){return e[t.key]}))),!0,!1))},n}(t.BaseComponent);var I=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.processSlotOptions=t.memoize(F),n.state={slatCoords:null},n.handleRootEl=function(e){e?n.context.registerInteractiveComponent(n,{el:e,isHitComboAllowed:n.props.isHitComboAllowed}):n.context.unregisterInteractiveComponent(n)},n.handleScrollRequest=function(e){var t=n.props.onScrollTopRequest,r=n.state.slatCoords;if(t&&r){if(e.time){var o=r.computeTimeTop(e.time);(o=Math.ceil(o))&&(o+=1),t(o)}return!0}return!1},n.handleColCoords=function(e){n.colCoords=e},n.handleSlatCoords=function(e){n.setState({slatCoords:e}),n.props.onSlatCoords&&n.props.onSlatCoords(e)},n}return o(n,e),n.prototype.render=function(){var e=this.props,n=this.state;return t.createElement("div",{className:"fc-timegrid-body",ref:this.handleRootEl,style:{width:e.clientWidth,minWidth:e.tableMinWidth}},t.createElement(g,{axis:e.axis,dateProfile:e.dateProfile,slatMetas:e.slatMetas,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,tableColGroupNode:e.axis?e.tableColGroupNode:null,onCoords:this.handleSlatCoords}),t.createElement(W,{cells:e.cells,axis:e.axis,dateProfile:e.dateProfile,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,todayRange:e.todayRange,nowDate:e.nowDate,nowIndicatorSegs:e.nowIndicatorSegs,clientWidth:e.clientWidth,tableMinWidth:e.tableMinWidth,tableColGroupNode:e.tableColGroupNode,slatCoords:n.slatCoords,onColCoords:this.handleColCoords,forPrint:e.forPrint}))},n.prototype.componentDidMount=function(){this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)},n.prototype.componentDidUpdate=function(e){this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)},n.prototype.componentWillUnmount=function(){this.scrollResponder.detach()},n.prototype.queryHit=function(e,n){var r=this.context,o=r.dateEnv,a=r.options,s=this.colCoords,l=this.props.dateProfile,c=this.state.slatCoords,d=this.processSlotOptions(this.props.slotDuration,a.snapDuration),u=d.snapDuration,p=d.snapsPerSlot,f=s.leftToIndex(e),m=c.positions.topToIndex(n);if(null!=f&&null!=m){var h=this.props.cells[f],g=c.positions.tops[m],v=c.positions.getHeight(m),y=(n-g)/v,S=m*p+Math.floor(y*p),E=this.props.cells[f].date,R=t.addDurations(l.slotMinTime,t.multiplyDuration(u,S)),D=o.add(E,R),C=o.add(D,u);return{dateProfile:l,dateSpan:i({range:{start:D,end:C},allDay:!1},h.extraDateSpan),dayEl:s.els[f],rect:{left:s.lefts[f],right:s.rights[f],top:g,bottom:g+v},layer:0}}return null},n}(t.DateComponent);function F(e,n){var r=n||e,o=t.wholeDivideDurations(e,r);return null===o&&(r=e,o=1),{snapDuration:r,snapsPerSlot:o}}var z=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return o(n,e),n.prototype.sliceRange=function(e,n){for(var r=[],o=0;o<n.length;o+=1){var i=t.intersectRanges(e,n[o]);i&&r.push({start:i.start,end:i.end,isStart:i.start.valueOf()===e.start.valueOf(),isEnd:i.end.valueOf()===e.end.valueOf(),col:o})}return r},n}(t.Slicer),G=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.buildDayRanges=t.memoize(L),n.slicer=new z,n.timeColsRef=t.createRef(),n}return o(n,e),n.prototype.render=function(){var e=this,n=this.props,r=this.context,o=n.dateProfile,a=n.dayTableModel,s=r.options.nowIndicator,l=this.buildDayRanges(a,o,r.dateEnv);return t.createElement(t.NowTimer,{unit:s?"minute":"day"},(function(c,d){return t.createElement(I,i({ref:e.timeColsRef},e.slicer.sliceProps(n,o,null,r,l),{forPrint:n.forPrint,axis:n.axis,dateProfile:o,slatMetas:n.slatMetas,slotDuration:n.slotDuration,cells:a.cells[0],tableColGroupNode:n.tableColGroupNode,tableMinWidth:n.tableMinWidth,clientWidth:n.clientWidth,clientHeight:n.clientHeight,expandRows:n.expandRows,nowDate:c,nowIndicatorSegs:s&&e.slicer.sliceNowDate(c,r,l),todayRange:d,onScrollTopRequest:n.onScrollTopRequest,onSlatCoords:n.onSlatCoords}))}))},n}(t.DateComponent);function L(e,t,n){for(var r=[],o=0,i=e.headerDates;o<i.length;o++){var a=i[o];r.push({start:n.add(a,t.slotMinTime),end:n.add(a,t.slotMaxTime)})}return r}var O=[{hours:1},{minutes:30},{minutes:15},{seconds:30},{seconds:15}];function A(e,n,r,o,i){for(var a=new Date(0),s=e,l=t.createDuration(0),c=r||function(e){var n,r,o;for(n=O.length-1;n>=0;n-=1)if(r=t.createDuration(O[n]),null!==(o=t.wholeDivideDurations(r,e))&&o>1)return r;return e}(o),d=[];t.asRoughMs(s)<t.asRoughMs(n);){var u=i.add(a,s),p=null!==t.wholeDivideDurations(l,c);d.push({date:u,time:s,key:u.toISOString(),isoTimeStr:t.formatIsoTimeString(u),isLabeled:p}),s=t.addDurations(s,o),l=t.addDurations(l,o)}return d}var B=function(e){function r(){var n=null!==e&&e.apply(this,arguments)||this;return n.buildTimeColsModel=t.memoize(j),n.buildSlatMetas=t.memoize(A),n}return o(r,e),r.prototype.render=function(){var e=this,r=this.context,o=r.options,a=r.dateEnv,s=r.dateProfileGenerator,l=this.props,c=l.dateProfile,d=this.buildTimeColsModel(c,s),u=this.allDaySplitter.splitProps(l),p=this.buildSlatMetas(c.slotMinTime,c.slotMaxTime,o.slotLabelInterval,o.slotDuration,a),f=o.dayMinWidth,m=!f,h=f,g=o.dayHeaders&&t.createElement(t.DayHeader,{dates:d.headerDates,dateProfile:c,datesRepDistinctDays:!0,renderIntro:m?this.renderHeadAxis:null}),v=!1!==o.allDaySlot&&function(r){return t.createElement(n.DayTable,i({},u.allDay,{dateProfile:c,dayTableModel:d,nextDayThreshold:o.nextDayThreshold,tableMinWidth:r.tableMinWidth,colGroupNode:r.tableColGroupNode,renderRowIntro:m?e.renderTableRowAxis:null,showWeekNumbers:!1,expandRows:!1,headerAlignElRef:e.headerElRef,clientWidth:r.clientWidth,clientHeight:r.clientHeight,forPrint:l.forPrint},e.getAllDayMaxEventProps()))},y=function(n){return t.createElement(G,i({},u.timed,{dayTableModel:d,dateProfile:c,axis:m,slotDuration:o.slotDuration,slatMetas:p,forPrint:l.forPrint,tableColGroupNode:n.tableColGroupNode,tableMinWidth:n.tableMinWidth,clientWidth:n.clientWidth,clientHeight:n.clientHeight,onSlatCoords:e.handleSlatCoords,expandRows:n.expandRows,onScrollTopRequest:e.handleScrollTopRequest}))};return h?this.renderHScrollLayout(g,v,y,d.colCnt,f,p,this.state.slatCoords):this.renderSimpleLayout(g,v,y)},r}(p);function j(e,n){var r=new t.DaySeriesModel(e.renderRange,n);return new t.DayTableModel(r,!1)}var q={allDaySlot:Boolean},U=t.createPlugin({initialView:"timeGridWeek",optionRefiners:q,views:{timeGrid:{component:B,usesMinMaxTime:!0,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},timeGridDay:{type:"timeGrid",duration:{days:1}},timeGridWeek:{type:"timeGrid",duration:{weeks:1}}}});return t.globalPlugins.push(U),e.DayTimeCols=G,e.DayTimeColsSlicer=z,e.DayTimeColsView=B,e.TimeCols=I,e.TimeColsSlatsCoords=m,e.TimeColsView=p,e.buildDayRanges=L,e.buildSlatMetas=A,e.buildTimeColsModel=j,e.default=U,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendarDayGrid);