// src/services/announcementService.js

import api from "./api";

// Fetches all announcements for the admin
export const getAnnouncements = () => api.get("/announcements/admin/");

// Creates a new announcement
export const createAnnouncement = (data) => api.post("/announcements/admin/", data);

// Updates an existing announcement
export const updateAnnouncement = (id, data) => api.put(`/announcements/admin/${id}/`, data);

// Deletes an announcement
export const deleteAnnouncement = (id) => api.delete(`/announcements/admin/${id}/`);


