// src/services/paymentService.js

import api from "./api";

/**
 * Creates a new Stripe Payment Intent on the backend.
 * @param {object} bookingData - The data needed to create the intent.
 * e.g., { service_id, expert_id, booking_time }
 * @returns {Promise<object>} A promise that resolves to the payment intent object
 * containing the clientSecret.
 */
export const createPaymentIntent = async (bookingData) => {
  try {
    const response = await api.post("/payments/create-payment-intent/", bookingData);
    return response.data; // Should return { clientSecret: '...' }
  } catch (error) {
    console.error("Error creating payment intent:", error);

    // Extract the specific error message from the backend response
    const errorMessage = error.response?.data?.error || error.message || "Unknown error occurred";

    // Create a new error with the specific message
    const customError = new Error(errorMessage);
    customError.originalError = error;

    throw customError;
  }
};


