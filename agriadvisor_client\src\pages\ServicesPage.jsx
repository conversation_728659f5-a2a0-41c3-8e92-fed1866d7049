// src/pages/ServicesPage.jsx

import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, InputGroup, Form } from "react-bootstrap";
import { getServices, createService, updateService, deleteService } from "../services/serviceService";
import ServiceModal from "../components/ServiceModal";
import ServiceCard from "../components/ServiceCard";

// Premium Admin Services CSS and Animations
const premiumCSS = `
  @keyframes serviceSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes serviceCardHover {
    from {
      transform: translateY(0) scale(1);
    }
    to {
      transform: translateY(-10px) scale(1.02);
    }
  }

  @keyframes priceGlow {
    0%, 100% {
      text-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
    }
    50% {
      text-shadow: 0 0 20px rgba(67, 233, 123, 0.8);
    }
  }

  @keyframes addServicePulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(67, 233, 123, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(67, 233, 123, 0.6);
    }
  }

  .premium-services-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: serviceSlideIn 0.6s ease-out;
  }

  .premium-services-card:hover {
    animation: serviceCardHover 0.3s ease-out forwards;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .service-grid-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    animation: serviceSlideIn 0.6s ease-out;
    overflow: hidden;
  }

  .service-grid-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
  }

  .add-service-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    animation: addServicePulse 3s infinite;
    position: relative;
    overflow: hidden;
  }

  .add-service-btn:hover {
    transform: translateY(-2px);
    animation: none;
    box-shadow: 0 15px 30px rgba(67, 233, 123, 0.4);
  }

  .add-service-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .add-service-btn:hover::before {
    left: 100%;
  }

  .search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .premium-search-input {
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .premium-search-input:focus {
    box-shadow: 0 5px 25px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
  }

  .category-filter {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-btn:hover, .filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .services-stats {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(168, 237, 234, 0.3);
  }

  .slide-in-up {
    animation: serviceSlideIn 0.6s ease-out;
  }

  .no-services-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }

  .service-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 15px;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }

  .service-price {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.1rem;
    animation: priceGlow 2s infinite;
  }

  .duration-badge {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  }
};

const ServicesPage = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [filteredServices, setFilteredServices] = useState([]);

  // State for managing the modal
  const [showModal, setShowModal] = useState(false);
  const [editingService, setEditingService] = useState(null); // null for 'add', service object for 'edit'

  // Service categories for filtering
  const serviceCategories = [
    { id: "all", name: "All Services", icon: "bi-grid-3x3-gap" },
    { id: "crop", name: "Crop Management", icon: "bi-flower1" },
    { id: "soil", name: "Soil Analysis", icon: "bi-geo-alt" },
    { id: "pest", name: "Pest Control", icon: "bi-bug" },
    { id: "irrigation", name: "Irrigation", icon: "bi-droplet" },
    { id: "consultation", name: "General Consultation", icon: "bi-chat-dots" }
  ];

  // Helper functions
  const getServiceStats = () => {
    const totalServices = services.length;
    const avgPrice = services.length > 0 ?
      (services.reduce((sum, service) => sum + parseFloat(service.price || 0), 0) / services.length).toFixed(2) : 0;
    const avgDuration = services.length > 0 ?
      Math.round(services.reduce((sum, service) => sum + parseInt(service.duration_minutes || 0), 0) / services.length) : 0;

    return {
      total: totalServices,
      avgPrice: avgPrice,
      avgDuration: avgDuration
    };
  };



  // Filter services based on search term and category
  useEffect(() => {
    let filtered = services;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (categoryFilter !== "all") {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(categoryFilter) ||
        service.description.toLowerCase().includes(categoryFilter)
      );
    }

    setFilteredServices(filtered);
  }, [services, searchTerm, categoryFilter]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const data = await getServices();
      setServices(data);
    } catch (err) {
      setError("Failed to fetch services.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  // --- Modal Handling Functions ---
  const handleShowAddModal = () => {
    setEditingService(null);
    setShowModal(true);
  };

  const handleShowEditModal = (service) => {
    setEditingService(service);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingService(null);
  };

  // --- CRUD Functions ---
  const handleSaveService = async (serviceData) => {
    try {
      if (editingService) {
        // Update existing service
        await updateService(editingService.id, serviceData);
      } else {
        // Create new service
        await createService(serviceData);
      }
      handleCloseModal();
      fetchServices(); // Refresh the list after saving
    } catch (err) {
      console.error("Failed to save service:", err);
      // You could add an error state here to display in the modal
    }
  };

  const handleDeleteService = async (id) => {
    // A simple confirmation dialog
    if (window.confirm("Are you sure you want to delete this service?")) {
      try {
        await deleteService(id);
        fetchServices(); // Refresh the list after deleting
      } catch (err) {
        console.error("Failed to delete service:", err);
        setError("Failed to delete service. It might be in use.");
      }
    }
  };

  // ... (render logic for loading and error states is the same)
  if (loading)
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" />
      </Container>
    );
  if (error)
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );

  if (loading) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-grid me-3"></i>
                  Manage Services
                </h1>
                <p className="lead mb-0 opacity-90">
                  Loading agricultural advisory services...
                </p>
              </Col>
              <Col xs="auto">
                <Button className="add-service-btn" disabled>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Loading...
                </Button>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="text-center" style={{ marginTop: '5rem' }}>
            <div className="mx-auto" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              maxWidth: '500px',
              padding: '3rem',
              borderRadius: '25px',
              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
            }}>
              <Spinner animation="border" size="lg" style={{ color: 'white' }} />
              <h3 className="mt-3 mb-2">Loading Services</h3>
              <p className="mb-0 opacity-75">Gathering agricultural advisory services...</p>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-grid me-3"></i>
                  Manage Services
                </h1>
                <p className="lead mb-0 opacity-90">
                  Service management system
                </p>
              </Col>
              <Col xs="auto">
                <Button className="add-service-btn" onClick={handleShowAddModal}>
                  <i className="bi bi-plus-circle me-2"></i>
                  Add New Service
                </Button>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="premium-services-card p-4">
            <Alert variant="danger" className="border-0 rounded-4">
              <Alert.Heading>
                <i className="bi bi-exclamation-triangle-fill me-2"></i>
                Services Unavailable
              </Alert.Heading>
              {error}
              <hr />
              <div className="d-flex justify-content-end">
                <Button variant="outline-danger" onClick={() => window.location.reload()}>
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  Try Again
                </Button>
              </div>
            </Alert>
          </div>
        </Container>
      </div>
    );
  }

  const stats = getServiceStats();

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-grid me-3"></i>
                Manage Services
              </h1>
              <p className="lead mb-0 opacity-90">
                Oversee agricultural advisory services and consultation offerings
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {filteredServices.length} Services
                  </Badge>
                </div>
                <small className="opacity-75">Currently Showing</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Service Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="services-stats slide-in-up">
              <Row className="text-center">
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#667eea' }}>
                    <i className="bi bi-grid-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.total}</h6>
                  <small className="text-muted">Total Services</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                    <i className="bi bi-currency-dollar"></i>
                  </div>
                  <h6 className="mb-1">${stats.avgPrice}</h6>
                  <small className="text-muted">Average Price</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                    <i className="bi bi-clock-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.avgDuration} min</h6>
                  <small className="text-muted">Average Duration</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Premium Search and Filter Section */}
        <div className="search-container slide-in-up">
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-3 text-white">
                <i className="bi bi-search me-2"></i>
                Find Services
              </h4>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search by service name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="premium-search-input"
                  style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                />
              </InputGroup>
            </Col>
            <Col md={4} className="text-center">
              <div className="text-white">
                <div className="h2 mb-1">
                  <i className="bi bi-award-fill"></i>
                </div>
                <h5 className="mb-0">Service Catalog</h5>
                <small className="opacity-75">Agricultural consultations</small>
              </div>
            </Col>
          </Row>
        </div>

        {/* Premium Category Filter */}
        <div className="category-filter slide-in-up">
          <h5 className="mb-3" style={{ color: '#333' }}>
            <i className="bi bi-funnel me-2"></i>
            Filter by Category
          </h5>
          <div className="d-flex flex-wrap justify-content-between align-items-center">
            <div className="d-flex flex-wrap">
              {serviceCategories.map(category => (
                <Button
                  key={category.id}
                  className={`filter-btn ${categoryFilter === category.id ? 'active' : ''}`}
                  onClick={() => setCategoryFilter(category.id)}
                >
                  <i className={`${category.icon} me-2`}></i>
                  {category.name}
                </Button>
              ))}
            </div>
            <Button className="add-service-btn" onClick={handleShowAddModal}>
              <i className="bi bi-plus-circle me-2"></i>
              Add New Service
            </Button>
          </div>
        </div>

        {/* Premium Services Grid */}
        {filteredServices.length > 0 ? (
          <Row xs={1} md={2} lg={3} xl={4} className="g-4">
            {filteredServices.map((service, index) => (
              <Col key={service.id}>
                <PremiumServiceCard
                  service={service}
                  onEdit={handleShowEditModal}
                  onDelete={handleDeleteService}
                  animationDelay={index * 0.1}
                />
              </Col>
            ))}
          </Row>
        ) : services.length > 0 ? (
          <div className="text-center py-5">
            <div className="premium-services-card p-5">
              <div className="mb-3">
                <i className="bi bi-search text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <h4 className="text-muted">No services found</h4>
              <p className="text-muted mb-4">
                Try adjusting your search terms or category filter
              </p>
              <Button
                variant="outline-primary"
                onClick={() => {
                  setSearchTerm("");
                  setCategoryFilter("all");
                }}
                className="rounded-pill px-4"
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <div className="no-services-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-grid" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">No Services Yet</h3>
            <p className="mb-4 opacity-90">
              Start building your service catalog by adding agricultural consultation services.
            </p>
            <Button
              variant="light"
              size="lg"
              onClick={handleShowAddModal}
              style={{ color: '#333', fontWeight: 'bold' }}
            >
              <i className="bi bi-plus-circle me-2"></i>
              Add Your First Service
            </Button>
          </div>
        )}
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help managing services?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>

      <ServiceModal
        show={showModal}
        onHide={handleCloseModal}
        onSave={handleSaveService}
        service={editingService}
      />
    </div>
  );
};

// Premium Service Card Component
const PremiumServiceCard = ({ service, onEdit, onDelete, animationDelay = 0 }) => {
  return (
    <Card
      className="service-grid-card h-100"
      style={{
        animationDelay: `${animationDelay}s`
      }}
    >
      <Card.Body className="p-4 text-center">
        {/* Service Icon */}
        <div className="service-icon">
          <i className={getServiceIcon(service.name)}></i>
        </div>

        {/* Service Info */}
        <h5 className="mb-2" style={{ color: '#333' }}>{service.name}</h5>
        <p className="text-muted mb-3" style={{ fontSize: '0.9rem', lineHeight: '1.5' }}>
          {service.description}
        </p>

        {/* Service Details */}
        <Row className="text-center mb-3">
          <Col xs={6}>
            <div className="p-2">
              <div className="service-price">
                ${service.price}
              </div>
              <small className="text-muted">Price</small>
            </div>
          </Col>
          <Col xs={6}>
            <div className="p-2">
              <span className="duration-badge">
                {service.duration_minutes} min
              </span>
              <div><small className="text-muted">Duration</small></div>
            </div>
          </Col>
        </Row>

        {/* Action Buttons */}
        <div className="d-flex gap-2 justify-content-center mb-3">
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => onEdit(service)}
            className="rounded-pill px-3"
          >
            <i className="bi bi-pencil"></i>
          </Button>
          <Button
            variant="outline-danger"
            size="sm"
            onClick={() => onDelete(service.id)}
            className="rounded-pill px-3"
          >
            <i className="bi bi-trash"></i>
          </Button>
        </div>

        {/* Service Features */}
        <div className="p-2 rounded-3" style={{
          background: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%)'
        }}>
          <small className="text-muted">
            <i className="bi bi-shield-check me-1"></i>
            Professional Agricultural Consultation
          </small>
        </div>
      </Card.Body>
    </Card>
  );
};

// Helper function for service icons (moved outside component for reuse)
const getServiceIcon = (serviceName) => {
  const name = serviceName.toLowerCase();
  if (name.includes('crop')) return 'bi-flower1';
  if (name.includes('soil')) return 'bi-geo-alt';
  if (name.includes('pest')) return 'bi-bug';
  if (name.includes('irrigation')) return 'bi-droplet';
  if (name.includes('consultation')) return 'bi-chat-dots';
  return 'bi-briefcase';
};

export default ServicesPage;


