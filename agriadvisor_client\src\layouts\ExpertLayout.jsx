// src/layouts/ExpertLayout.jsx

import React from "react";
import { Outlet, NavLink } from "react-router-dom";
import { Navbar, Container, Nav } from "react-bootstrap";
import useAuth from "../hooks/useAuth";

import AnnouncementBanner from "../components/AnnouncementBanner";

const ExpertLayout = () => {
  const { logout } = useAuth();

  return (
    <div>
      <Navbar bg="dark" variant="dark" expand="lg" sticky="top" style={{ zIndex: 1020 }}>
        <Container>
          <Navbar.Brand as={NavLink} to="/">
            <i className="bi bi-person-check-fill me-2"></i>Expert Dashboard
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="expert-navbar-nav" />
          <Navbar.Collapse id="expert-navbar-nav">
            <Nav className="me-auto">
              <Nav.Link as={NavLink} to="/expert/availability">
                My Availability
              </Nav.Link>
              <Nav.Link as={NavLink} to="/expert/profile">
                My Profile
              </Nav.Link>
              <Nav.Link as={NavLink} to="/expert/dashboard">
                My Schedule
              </Nav.Link>
              <Nav.Link as={NavLink} to="/expert/payouts">
                Payouts
              </Nav.Link>
            </Nav>
            <Nav>
              <Nav.Link onClick={logout}>Logout</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      <AnnouncementBanner />
      <Container fluid className="mt-4 p-4">
        <Outlet /> {/* Expert-specific pages will be rendered here */}
      </Container>
    </div>
  );
};

export default ExpertLayout;



