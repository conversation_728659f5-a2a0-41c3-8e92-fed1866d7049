# accounts/models.py

from django.db import models
from django.contrib.auth.models import AbstractUser

class Tenant(models.Model):
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    def __str__(self): return self.name

class CustomUser(AbstractUser):
    ROLE_CHOICES = (('admin', 'Admin'), ('expert', 'Expert'), ('farmer', 'Farmer'))
    role = models.CharField(max_length=10, choices=ROLE_CHOICES)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="users", null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    has_completed_onboarding = models.BooleanField(default=False)

class FarmingInterest(models.Model):
    name = models.CharField(max_length=100, unique=True)
    def __str__(self): return self.name

class FarmerProfile(models.Model):
    # CONSISTENT NAMING: 'farmer_profile'
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='farmer_profile')
    location_country = models.CharField(max_length=100, blank=True)
    interests = models.ManyToManyField(FarmingInterest, blank=True)
    def __str__(self): return f"Profile for {self.user.username}"

class ExpertProfile(models.Model):
    # CONSISTENT NAMING: 'expert_profile'
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='expert_profile')
    specialty = models.CharField(max_length=255, blank=True)
    bio = models.TextField(blank=True)
    stripe_account_id = models.CharField(max_length=255, blank=True, null=True)
    payouts_enabled = models.BooleanField(default=False)
    def __str__(self): return f"Profile for {self.user.username}"
    
    
    

