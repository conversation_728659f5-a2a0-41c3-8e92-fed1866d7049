// src/services/serviceService.js

import api from "./api"; // Our configured Axios instance

/**
 * Fetches all services for the current tenant.
 */
export const getServices = async () => {
  try {
    const response = await api.get("/services/");
    return response.data;
  } catch (error) {
    console.error("Error fetching services:", error);
    throw error;
  }
};

/**
 * Creates a new service.
 * @param {Object} serviceData - The data for the new service.
 * e.g., { name, description, price, currency, duration_minutes }
 */
export const createService = async (serviceData) => {
  try {
    const response = await api.post("/services/", serviceData);
    return response.data;
  } catch (error) {
    console.error("Error creating service:", error);
    throw error;
  }
};

/**
 * Updates an existing service.
 * @param {number} id - The ID of the service to update.
 * @param {Object} serviceData - The updated data for the service.
 */
export const updateService = async (id, serviceData) => {
  try {
    const response = await api.put(`/services/${id}/`, serviceData);
    return response.data;
  } catch (error) {
    console.error(`Error updating service ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes a service.
 * @param {number} id - The ID of the service to delete.
 */
export const deleteService = async (id) => {
  try {
    // A DELETE request might not return a body, so we just check for success status.
    await api.delete(`/services/${id}/`);
  } catch (error) {
    console.error(`Error deleting service ${id}:`, error);
    throw error;
  }
};



/**
 * Fetches all available services for a logged-in farmer.
 * Uses the dedicated farmer endpoint.
 */
export const getFarmerServices = async () => {
  try {
    const response = await api.get('/farmer/services/');
    return response.data;
  } catch (error) {
    console.error("Error fetching farmer services:", error);
    throw error;
  }
};

