from django.urls import path

from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import MyAvailabilityRuleViewSet, MyAvailabilityExceptionViewSet
from .views import CheckAvailabilityView 


router = DefaultRouter()
router.register(r'rules', MyAvailabilityRuleViewSet, basename='availability-rule')
router.register(r'exceptions', MyAvailabilityExceptionViewSet, basename='availability-exception')

urlpatterns = router.urls

urlpatterns = router.urls + [
    path('check/', CheckAvailabilityView.as_view(), name='check-availability'),
]

