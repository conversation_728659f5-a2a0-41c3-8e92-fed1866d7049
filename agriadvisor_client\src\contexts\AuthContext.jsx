// src/contexts/AuthContext.jsx

import React, { createContext, useState, useEffect, useMemo, useCallback } from "react";
import { jwtDecode } from "jwt-decode";
import api from "../services/api";
import { loginUser } from "../services/authService";

// 1. Create the context
const AuthContext = createContext();

// 2. Create and EXPORT the provider component.
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  
  useEffect(() => {
    const initializeAuth = () => {
      console.log("AuthContext: Initializing auth...");
      const token = localStorage.getItem("access_token");
      console.log("AuthContext: Token found:", !!token);

      if (token) {
        try {
          const decodedUser = jwtDecode(token);
          console.log("AuthContext: Decoded user:", decodedUser);

          // Check if token is expired
          const currentTime = Date.now() / 1000;
          if (decodedUser.exp > currentTime) {
            console.log("AuthContext: Token valid, setting user");
            setUser(decodedUser);
            api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
          } else {
            // Token expired
            console.log("AuthContext: Token expired, clearing");
            localStorage.removeItem("access_token");
            setUser(null);
            delete api.defaults.headers.common["Authorization"];
          }
        } catch (error) {
          console.error("AuthContext: Invalid token:", error);
          localStorage.removeItem("access_token");
          setUser(null);
          delete api.defaults.headers.common["Authorization"];
        }
      } else {
        console.log("AuthContext: No token found, user is null");
        setUser(null);
      }

      // Always set loading to false after checking
      console.log("AuthContext: Setting loading to false");
      setLoading(false);
    };

    initializeAuth();
  }, []); // Empty dependency array - only run once

  const logout = useCallback(() => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    delete api.defaults.headers.common["Authorization"];
    setUser(null);
  }, []);

  const login = useCallback(async (username, password) => {
    try {
      const data = await loginUser({ username, password });

      // Update localStorage with both tokens
      localStorage.setItem("access_token", data.access);
      localStorage.setItem("refresh_token", data.refresh);

      // Set authorization header
      api.defaults.headers.common["Authorization"] = `Bearer ${data.access}`;

      const decodedUser = jwtDecode(data.access);
      console.log("Decoded user:", decodedUser); // Debug log
      setUser(decodedUser);
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any partial tokens on failed login
      logout();
      throw error;
    }
  }, [logout]);

  const contextData = useMemo(() => ({
    user,
    loading,
    login,
    logout,
  }), [user, loading, login, logout]);

  // 3. Return the provider and always render children
  return <AuthContext.Provider value={contextData}>{children}</AuthContext.Provider>;
};

export default AuthContext;


