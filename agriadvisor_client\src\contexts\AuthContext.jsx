// src/contexts/AuthContext.jsx

import React, { createContext, useState, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import api from "../services/api";
import { loginUser } from "../services/authService";

// 1. Create the context
const AuthContext = createContext();

// 2. Create and EXPORT the provider component.
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  
  useEffect(() => {
    const initializeAuth = () => {
      const token = localStorage.getItem("access_token");

      if (token) {
        try {
          const decodedUser = jwtDecode(token);

          // Check if token is expired
          const currentTime = Date.now() / 1000;
          if (decodedUser.exp > currentTime) {
            setUser(decodedUser);
            api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
          } else {
            // Token expired
            localStorage.removeItem("access_token");
            setUser(null);
            delete api.defaults.headers.common["Authorization"];
          }
        } catch (error) {
          console.error("Invalid token:", error);
          localStorage.removeItem("access_token");
          setUser(null);
          delete api.defaults.headers.common["Authorization"];
        }
      }

      // Always set loading to false after checking
      setLoading(false);
    };

    initializeAuth();
  }, []); // Empty dependency array - only run once

  const login = async (username, password) => {
    try {
      const data = await loginUser({ username, password });

      // Update localStorage with both tokens
      localStorage.setItem("access_token", data.access);
      localStorage.setItem("refresh_token", data.refresh);

      // Set authorization header
      api.defaults.headers.common["Authorization"] = `Bearer ${data.access}`;

      const decodedUser = jwtDecode(data.access);
      console.log("Decoded user:", decodedUser); // Debug log
      setUser(decodedUser);
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any partial tokens on failed login
      logout();
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    delete api.defaults.headers.common["Authorization"];
    setUser(null);
  };

  const contextData = {
    user,
    loading,
    login,
    logout,
  };

  // 3. Return the provider, but don't render children until loading is false
  return <AuthContext.Provider value={contextData}>{!loading && children}</AuthContext.Provider>;
};

export default AuthContext;


