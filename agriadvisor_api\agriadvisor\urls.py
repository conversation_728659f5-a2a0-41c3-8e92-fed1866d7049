"""
URL configuration for agriadvisor project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""


# agriadvisor/urls.py

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    
    # Authentication URLs
    path('api/accounts/', include('accounts.urls')),
    
    # Advisory App URLs
    path('api/', include('advisory.urls')), # <--- ADD THIS LINE
]


urlpatterns += [
    path('api/payments/', include('payments.urls')),
]


urlpatterns += [
    path('api/stripe/', include('stripe_integration.urls')),
]


urlpatterns += [
    path('api/availability/', include('availability.urls')),
]

urlpatterns += [
    path('api/reviews/', include('reviews.urls')),
]

urlpatterns += [
    path('api/announcements/', include('announcements.urls')),
]

