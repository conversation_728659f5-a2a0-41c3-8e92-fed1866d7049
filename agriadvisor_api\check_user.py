#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agriadvisor.settings')
django.setup()

from accounts.models import CustomUser

print("=== Checking User Details ===")

# Check admin user
try:
    admin_user = CustomUser.objects.get(username='admin')
    print(f"Admin User: {admin_user.username}")
    print(f"Role: {admin_user.role}")
    print(f"Tenant: {admin_user.tenant}")
    print(f"Is Staff: {admin_user.is_staff}")
    print(f"Is Superuser: {admin_user.is_superuser}")
    
    if not admin_user.tenant:
        print("⚠️  Admin user has no tenant assigned!")
        print("This will cause dashboard API calls to fail.")
    else:
        print(f"✅ Admin user has tenant: {admin_user.tenant}")
        
except CustomUser.DoesNotExist:
    print("❌ Admin user not found!")

print("\n=== All Users ===")
for user in CustomUser.objects.all():
    print(f"- {user.username} (role: {user.role}, tenant: {user.tenant})")
