# availability/utils.py

from datetime import datetime, timedelta
import calendar

def calculate_available_slots(expert, year, month, duration_minutes):
    """
    Calculates all available booking slots for a given expert and month.

    Args:
        expert (CustomUser): The expert user instance.
        year (int): The year to check.
        month (int): The month to check.
        duration_minutes (int): The duration of the service in minutes.

    Returns:
        list: A list of available datetime start times.
    """
    
    # 1. Get all recurring rules for the expert
    rules = expert.availability_rules.all()
    # 2. Get all one-off exceptions for the expert for the given month
    exceptions = expert.availability_exceptions.filter(date__year=year, date__month=month)
    # 3. Get all existing confirmed bookings for the expert for the given month
    existing_bookings = expert.expert_bookings.filter(
        booking_time__year=year,
        booking_time__month=month,
        status='confirmed'
    )

    # --- Pre-process data for faster lookups ---
    rules_dict = {rule.day_of_week: [] for rule in rules}
    for rule in rules:
        rules_dict[rule.day_of_week].append((rule.start_time, rule.end_time))

    exceptions_dict = {exc.date: exc for exc in exceptions}
    booked_slots_set = {b.booking_time for b in existing_bookings}

    available_slots = []
    
    # --- Iterate through each day of the specified month ---
    num_days = calendar.monthrange(year, month)[1]
    for day in range(1, num_days + 1):
        current_date = datetime(year, month, day).date()
        
        # --- Check for exceptions first ---
        if current_date in exceptions_dict:
            exception = exceptions_dict[current_date]
            if not exception.is_available:
                continue # Skip this day entirely if the expert is unavailable
            else:
                # If there are special hours, use them instead of the weekly rule
                day_start = datetime.combine(current_date, exception.start_time)
                day_end = datetime.combine(current_date, exception.end_time)
        else:
            # --- Use the weekly recurring rule ---
            weekday = current_date.weekday() # Monday is 0, Sunday is 6
            if weekday not in rules_dict:
                continue # No rule for this day of the week
            
            # For simplicity, we'll take the first rule for a day. 
            # This can be expanded to handle multiple rules per day (e.g., morning and afternoon).
            try:
                start_t, end_t = rules_dict[weekday][0]
                day_start = datetime.combine(current_date, start_t)
                day_end = datetime.combine(current_date, end_t)
            except IndexError:
                continue # No rule found for this day

        # --- Generate potential slots and check for conflicts ---
        potential_slot = day_start
        while potential_slot + timedelta(minutes=duration_minutes) <= day_end:
            # Check if this exact slot is already booked
            if potential_slot not in booked_slots_set:
                # Check if this slot is in the future
                if potential_slot > datetime.now():
                    available_slots.append(potential_slot)
            
            # Move to the next slot
            potential_slot += timedelta(minutes=duration_minutes)

    return available_slots

