#!/usr/bin/env python
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agriadvisor.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import CustomUser

User = get_user_model()

print("=== Testing Authentication ===")

# Check if users exist
print("\n1. Checking existing users:")
users = User.objects.all()
for user in users:
    print(f"   - Username: {user.username}, Email: {user.email}, Role: {user.role}")

if not users.exists():
    print("   No users found! Creating admin user...")
    admin_user = User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        first_name='Admin',
        last_name='User',
        role='admin',
        is_staff=True,
        is_superuser=True
    )
    print(f"   Created admin user: {admin_user.username}")

# Test authentication
print("\n2. Testing password verification:")
try:
    admin_user = User.objects.get(username='admin')
    is_valid = admin_user.check_password('admin123')
    print(f"   Password check for admin: {is_valid}")
except User.DoesNotExist:
    print("   Admin user not found!")

# Test API endpoint
print("\n3. Testing API endpoint:")
try:
    # Start Django server programmatically for testing
    from django.core.management import execute_from_command_line
    import threading
    import time
    
    def start_server():
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8002', '--noreload'])
    
    # Start server in background
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(3)  # Wait for server to start
    
    # Test login endpoint
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = requests.post('http://127.0.0.1:8002/api/accounts/token/', 
                           data=json.dumps(login_data),
                           headers={'Content-Type': 'application/json'})
    
    print(f"   API Response Status: {response.status_code}")
    print(f"   API Response: {response.text}")
    
except Exception as e:
    print(f"   API test failed: {e}")

print("\n=== Test Complete ===")
