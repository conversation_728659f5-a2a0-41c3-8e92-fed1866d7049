/*!
FullCalendar v5.11.5
Docs & License: https://fullcalendar.io/
(c) 2022 <PERSON>
*/
var FullCalendarBootstrap5=function(e,t){"use strict";var o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])})(e,t)};var r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(t,e),t}(t.Theme);r.prototype.classes={root:"fc-theme-bootstrap5",tableCellShaded:"fc-theme-bootstrap5-shaded",buttonGroup:"btn-group",button:"btn btn-primary",buttonActive:"active",popover:"popover",popoverHeader:"popover-header",popoverContent:"popover-body"},r.prototype.baseIconClass="bi",r.prototype.iconClasses={close:"bi-x-lg",prev:"bi-chevron-left",next:"bi-chevron-right",prevYear:"bi-chevron-double-left",nextYear:"bi-chevron-double-right"},r.prototype.rtlIconClasses={prev:"bi-chevron-right",next:"bi-chevron-left",prevYear:"bi-chevron-double-right",nextYear:"bi-chevron-double-left"},r.prototype.iconOverrideOption="buttonIcons",r.prototype.iconOverrideCustomButtonOption="icon",r.prototype.iconOverridePrefix="bi-";var n=t.createPlugin({themeClasses:{bootstrap5:r}});return t.globalPlugins.push(n),e.BootstrapTheme=r,e.default=n,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar);