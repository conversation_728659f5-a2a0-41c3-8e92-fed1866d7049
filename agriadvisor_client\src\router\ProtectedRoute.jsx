// src/router/ProtectedRoute.jsx

import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import useAuth from "../hooks/useAuth";

const ProtectedRoute = ({ allowedRoles }) => {
  const { user, loading } = useAuth();

  // While the AuthContext is figuring out if a user is logged in, show a loading message.
  if (loading) {
    return <div>Authenticating...</div>;
  }

  // If loading is done, check for user and role.
  return user && allowedRoles?.includes(user.role) ? (
    <Outlet /> // If authorized, render the nested routes (e.g., the Layout)
  ) : (
    <Navigate to="/login" replace /> // If not authorized, redirect to login
  );
};

export default ProtectedRoute;



