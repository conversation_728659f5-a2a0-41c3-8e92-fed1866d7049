# reviews/serializers.py

from rest_framework import serializers
from .models import Review
from accounts.serializers import CustomUserSerializer
from advisory.models import Booking # <--- IMPORT BOOKING

class ReviewSerializer(serializers.ModelSerializer):
    farmer = CustomUserSerializer(read_only=True)
    
    # --- THIS WAS THE BUG ---
    # It should be validating against the Booking model, not the Review model.
    booking = serializers.PrimaryKeyRelatedField(queryset=Booking.objects.all())

    class Meta:
        model = Review
        fields = ['id', 'rating', 'comment', 'created_at', 'farmer', 'booking']
        read_only_fields = ['farmer', 'expert']
        
    