// src/pages/FarmersPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, InputGroup, Form } from "react-bootstrap";
import { getFarmers } from "../services/farmerService";

// Premium Admin Farmers CSS and Animations
const premiumCSS = `
  @keyframes farmerSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes farmerCardHover {
    from {
      transform: translateY(0) scale(1);
    }
    to {
      transform: translateY(-10px) scale(1.02);
    }
  }

  @keyframes inviteButtonPulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(67, 233, 123, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(67, 233, 123, 0.6);
    }
  }

  @keyframes searchFocus {
    from {
      box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    to {
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }
  }

  .premium-farmers-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: farmerSlideIn 0.6s ease-out;
  }

  .premium-farmers-card:hover {
    animation: farmerCardHover 0.3s ease-out forwards;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .farmer-grid-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    animation: farmerSlideIn 0.6s ease-out;
    overflow: hidden;
  }

  .farmer-grid-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
  }

  .invite-farmer-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    animation: inviteButtonPulse 3s infinite;
    position: relative;
    overflow: hidden;
  }

  .invite-farmer-btn:hover {
    transform: translateY(-2px);
    animation: none;
    box-shadow: 0 15px 30px rgba(67, 233, 123, 0.4);
  }

  .invite-farmer-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .invite-farmer-btn:hover::before {
    left: 100%;
  }

  .search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .premium-search-input {
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .premium-search-input:focus {
    box-shadow: 0 5px 25px rgba(102, 126, 234, 0.3);
    animation: searchFocus 0.3s ease;
  }

  .location-filter {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-btn:hover, .filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .farmers-stats {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(168, 237, 234, 0.3);
  }

  .slide-in-up {
    animation: farmerSlideIn 0.6s ease-out;
  }

  .no-farmers-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }

  .farmer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.8rem;
    margin: 0 auto 15px;
    border: 4px solid white;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  }

  .location-badge {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .farmer-type-badge {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  }
};

const FarmersPage = () => {
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [locationFilter, setLocationFilter] = useState("all");
  const [filteredFarmers, setFilteredFarmers] = useState([]);

  // Location filters (will be populated from actual farmer data)
  const [locationFilters, setLocationFilters] = useState([
    { id: "all", name: "All Locations", icon: "bi-globe" }
  ]);

  // Helper functions
  const getFarmerStats = () => {
    const totalFarmers = farmers.length;
    const locations = new Set(farmers.map(farmer => farmer.farmer_profile?.location_country || farmer.profile?.location_country || 'Unknown')).size;
    const activeFarmers = farmers.filter(farmer => farmer.is_active !== false).length;

    return {
      total: totalFarmers,
      locations: locations,
      active: activeFarmers
    };
  };

  // Generate location filters from farmer data
  const generateLocationFilters = (farmersData) => {
    const locations = new Set();
    farmersData.forEach(farmer => {
      const location = farmer.farmer_profile?.location_country || farmer.profile?.location_country;
      if (location) {
        locations.add(location);
      }
    });

    const filters = [
      { id: "all", name: "All Locations", icon: "bi-globe" },
      ...Array.from(locations).map(location => ({
        id: location.toLowerCase(),
        name: location,
        icon: "bi-geo-alt"
      }))
    ];

    setLocationFilters(filters);
  };

  // Filter farmers based on search term and location
  useEffect(() => {
    let filtered = farmers;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(farmer =>
        `${farmer.first_name} ${farmer.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        farmer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (farmer.phone_number || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by location
    if (locationFilter !== "all") {
      filtered = filtered.filter(farmer => {
        const location = farmer.farmer_profile?.location_country || farmer.profile?.location_country;
        return location && location.toLowerCase() === locationFilter;
      });
    }

    setFilteredFarmers(filtered);
  }, [farmers, searchTerm, locationFilter]);

  useEffect(() => {
    const fetchFarmers = async () => {
      try {
        setLoading(true);
        const data = await getFarmers();
        setFarmers(data);
        generateLocationFilters(data);
      } catch (err) {
        setError("Failed to fetch farmers.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchFarmers();
  }, []);

  if (loading) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-house-heart me-3"></i>
                  Manage Farmers
                </h1>
                <p className="lead mb-0 opacity-90">
                  Loading farmer community members...
                </p>
              </Col>
              <Col xs="auto">
                <Button className="invite-farmer-btn" disabled>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Loading...
                </Button>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="text-center" style={{ marginTop: '5rem' }}>
            <div className="mx-auto" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              maxWidth: '500px',
              padding: '3rem',
              borderRadius: '25px',
              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
            }}>
              <Spinner animation="border" size="lg" style={{ color: 'white' }} />
              <h3 className="mt-3 mb-2">Loading Farmer Community</h3>
              <p className="mb-0 opacity-75">Gathering farmer information...</p>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-house-heart me-3"></i>
                  Manage Farmers
                </h1>
                <p className="lead mb-0 opacity-90">
                  Farmer management system
                </p>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Farmers Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </Container>
      </div>
    );
  }

  const stats = getFarmerStats();

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-house-heart me-3"></i>
                Manage Farmers
              </h1>
              <p className="lead mb-0 opacity-90">
                Oversee farming community members and their agricultural activities
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {filteredFarmers.length} Farmers
                  </Badge>
                </div>
                <small className="opacity-75">Currently Showing</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Farmer Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="farmers-stats slide-in-up">
              <Row className="text-center">
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#667eea' }}>
                    <i className="bi bi-people-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.total}</h6>
                  <small className="text-muted">Total Farmers</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                    <i className="bi bi-geo-alt-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.locations}</h6>
                  <small className="text-muted">Locations</small>
                </Col>
                <Col md={4}>
                  <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                    <i className="bi bi-check-circle-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.active}</h6>
                  <small className="text-muted">Active Farmers</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Premium Search and Filter Section */}
        <div className="search-container slide-in-up">
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-3 text-white">
                <i className="bi bi-search me-2"></i>
                Find Farmers
              </h4>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="premium-search-input"
                  style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                />
              </InputGroup>
            </Col>
            <Col md={4} className="text-center">
              <div className="text-white">
                <div className="h2 mb-1">
                  <i className="bi bi-award-fill"></i>
                </div>
                <h5 className="mb-0">Farmer Network</h5>
                <small className="opacity-75">Agricultural community</small>
              </div>
            </Col>
          </Row>
        </div>

        {/* Premium Location Filter */}
        <div className="location-filter slide-in-up">
          <h5 className="mb-3" style={{ color: '#333' }}>
            <i className="bi bi-funnel me-2"></i>
            Filter by Location
          </h5>
          <div className="d-flex flex-wrap justify-content-between align-items-center">
            <div className="d-flex flex-wrap">
              {locationFilters.map(filter => (
                <Button
                  key={filter.id}
                  className={`filter-btn ${locationFilter === filter.id ? 'active' : ''}`}
                  onClick={() => setLocationFilter(filter.id)}
                >
                  <i className={`${filter.icon} me-2`}></i>
                  {filter.name}
                </Button>
              ))}
            </div>
            <Button
              className="invite-farmer-btn"
              onClick={() => {
                // Future: Open invite farmer modal
                alert('Invite farmer functionality coming soon!');
              }}
            >
              <i className="bi bi-person-plus me-2"></i>
              Invite Farmer
            </Button>
          </div>
        </div>

        {/* Premium Farmers Grid */}
        {filteredFarmers.length > 0 ? (
          <Row xs={1} md={2} lg={3} xl={4} className="g-4">
            {filteredFarmers.map((farmer, index) => (
              <Col key={farmer.id}>
                <PremiumFarmerCard
                  farmer={farmer}
                  animationDelay={index * 0.1}
                />
              </Col>
            ))}
          </Row>
        ) : farmers.length > 0 ? (
          <div className="text-center py-5">
            <div className="premium-farmers-card p-5">
              <div className="mb-3">
                <i className="bi bi-search text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <h4 className="text-muted">No farmers found</h4>
              <p className="text-muted mb-4">
                Try adjusting your search terms or location filter
              </p>
              <Button
                variant="outline-primary"
                onClick={() => {
                  setSearchTerm("");
                  setLocationFilter("all");
                }}
                className="rounded-pill px-4"
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <div className="no-farmers-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-house-heart" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">No Farmers Yet</h3>
            <p className="mb-4 opacity-90">
              Start building your farming community by inviting agricultural professionals to the platform.
            </p>
            <Button
              variant="light"
              size="lg"
              onClick={() => {
                // Future: Open invite farmer modal
                alert('Invite farmer functionality coming soon!');
              }}
              style={{ color: '#333', fontWeight: 'bold' }}
            >
              <i className="bi bi-person-plus me-2"></i>
              Invite Your First Farmer
            </Button>
          </div>
        )}
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help managing farmers?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>
    </div>
  );
};

// Premium Farmer Card Component
const PremiumFarmerCard = ({ farmer, animationDelay = 0 }) => {
  const getInitials = () => {
    return `${farmer.first_name?.[0] || ''}${farmer.last_name?.[0] || ''}`;
  };

  const getLocation = () => {
    return farmer.farmer_profile?.location_country || farmer.profile?.location_country || 'Unknown';
  };

  const getPhone = () => {
    return farmer.phone_number || 'Not provided';
  };

  return (
    <Card
      className="farmer-grid-card h-100"
      style={{
        animationDelay: `${animationDelay}s`
      }}
    >
      <Card.Body className="p-4 text-center">
        {/* Farmer Avatar */}
        <div className="farmer-avatar">
          {getInitials()}
        </div>

        {/* Farmer Info */}
        <h5 className="mb-1" style={{ color: '#333' }}>
          {farmer.first_name} {farmer.last_name}
        </h5>
        <p className="text-muted mb-2">{farmer.email}</p>

        {/* Location Badge */}
        <div className="mb-3">
          <span className="location-badge">
            <i className="bi bi-geo-alt me-1"></i>
            {getLocation()}
          </span>
        </div>

        {/* Farmer Details */}
        <Row className="text-center mb-3">
          <Col xs={12}>
            <div className="p-2">
              <div className="h6 mb-1" style={{ color: '#667eea' }}>
                <i className="bi bi-telephone me-1"></i>
                {getPhone()}
              </div>
              <small className="text-muted">Contact</small>
            </div>
          </Col>
        </Row>

        {/* Farmer Type Badge */}
        {farmer.type && (
          <div className="mb-3">
            <span className="farmer-type-badge">
              <i className="bi bi-person-badge me-1"></i>
              {farmer.type}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="d-flex gap-2 justify-content-center mb-3">
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => {
              // Future: Open farmer details modal
              alert(`View details for ${farmer.first_name} ${farmer.last_name}`);
            }}
            className="rounded-pill px-3"
          >
            <i className="bi bi-eye"></i>
          </Button>
          <Button
            variant="outline-success"
            size="sm"
            onClick={() => {
              // Future: Contact farmer functionality
              window.open(`mailto:${farmer.email}`, '_blank');
            }}
            className="rounded-pill px-3"
          >
            <i className="bi bi-envelope"></i>
          </Button>
        </div>

        {/* Farmer Status */}
        <div className="p-2 rounded-3" style={{
          background: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%)'
        }}>
          <small className="text-muted">
            <i className={`bi ${farmer.is_active !== false ? 'bi-check-circle-fill text-success' : 'bi-pause-circle-fill text-warning'} me-1`}></i>
            {farmer.is_active !== false ? 'Active Member' : 'Inactive Member'}
          </small>
        </div>
      </Card.Body>
    </Card>
  );
};

export default FarmersPage;


