// src/pages/ExpertDashboardPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, Al<PERSON>, ListGroup, <PERSON>ton, Badge, ProgressBar } from "react-bootstrap";
import { Link } from "react-router-dom";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { getExpertDashboardData } from "../services/dashboardService";
import BookingDetailModal from "../components/BookingDetailModal";
import useAuth from "../hooks/useAuth";
// We need the function to mark bookings as complete
import { markBookingAsComplete } from "../services/bookingService";

// Premium Dashboard CSS and Animations
const premiumCSS = `
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
  }

  @keyframes countUp {
    from {
      transform: scale(0.8);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .premium-dashboard-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .premium-dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }

  .stat-card-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
  }

  .stat-card-premium:hover {
    transform: translateY(-5px) scale(1.02);
    animation: pulseGlow 2s infinite;
  }

  .stat-value {
    animation: countUp 0.8s ease-out;
    font-weight: 700;
    font-size: 2.5rem;
  }

  .calendar-premium {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  }

  .quick-action-btn {
    border-radius: 15px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
  }

  .slide-in-up {
    animation: slideInUp 0.6s ease-out;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 25px 25px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  earningsCard: {
    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    color: 'white'
  },
  appointmentsCard: {
    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    color: 'white'
  },
  ratingCard: {
    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    color: '#333'
  },
  quickLinksCard: {
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    border: 'none',
    borderRadius: '20px',
    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
  },
  activityCard: {
    background: 'linear-gradient(135deg, #e3ffe7 0%, #d9e7ff 100%)',
    border: 'none',
    borderRadius: '20px',
    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
  }
};

const ExpertDashboardPage = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const { user } = useAuth();

  // Temporary: Return a simple component to test if routing works
  return (
    <Container className="mt-4">
      <h1>Expert Dashboard</h1>
      <p>Welcome, {user?.username || 'Expert'}!</p>
      <p>Dashboard is loading...</p>
    </Container>
  );

  const fetchData = async () => {
    try {
      setLoading(true);
      const data = await getExpertDashboardData();
      setDashboardData(data);
    } catch (err) {
      console.error("Expert dashboard error:", err);
      // Set default data to prevent blank screen
      setDashboardData({
        upcoming_bookings: [],
        completed_bookings: [],
        total_earnings: 0,
        pending_bookings: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatEvents = (bookings) => {
    return bookings.map((booking) => ({
      id: booking.id,
      title: `${booking.service.name} w/ ${booking.farmer.first_name}`,
      start: booking.booking_time,
      backgroundColor: booking.status === "completed" ? "#198754" : "#0d6efd", // Green for completed, blue for confirmed
      borderColor: booking.status === "completed" ? "#198754" : "#0d6efd",
      extendedProps: {
        ...booking, // Pass all booking data
        farmer: `${booking.farmer.first_name} ${booking.farmer.last_name}`,
        expert: `${booking.expert.first_name} ${booking.expert.last_name}`,
        service: booking.service.name,
      },
    }));
  };

  const handleEventClick = (clickInfo) => {
    setSelectedBooking(clickInfo.event);
    setShowModal(true);
  };

  const handleMarkComplete = async (bookingId) => {
    try {
      await markBookingAsComplete(bookingId);
      setShowModal(false);
      fetchData(); // Refetch all dashboard data to update stats and calendar
    } catch (err) {
      alert("Failed to update booking status.");
    }
  };

  if (loading)
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          ...premiumStyles.earningsCard,
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(79, 172, 254, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h3 className="mt-3 mb-2">Loading Your Dashboard</h3>
          <p className="mb-0 opacity-75">Preparing your expert insights and calendar...</p>
        </div>
      </Container>
    );

  if (error)
    return (
      <Container className="mt-4">
        <div className="premium-dashboard-card p-4">
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Dashboard Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </div>
      </Container>
    );

  return (
    <div>
      {/* Premium Dashboard Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container fluid>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-speedometer2 me-3"></i>
                Expert Dashboard
              </h1>
              <p className="lead mb-0 opacity-90">
                Welcome back! Here's your performance overview and upcoming schedule.
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <i className="bi bi-calendar-check me-2"></i>
                  {dashboardData?.stats.upcoming_count || 0}
                </div>
                <small className="opacity-75">Upcoming Sessions</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container fluid>
        {/* Premium Stat Cards Row */}
        <Row className="mb-4">
          <Col lg={4} className="mb-4">
            <PremiumStatCard
              title="Monthly Earnings"
              value={`$${dashboardData?.stats.monthly_earnings.toFixed(2)}`}
              icon="bi-wallet2"
              gradient={premiumStyles.earningsCard}
              subtitle="80% of total bookings"
              trend="+12%"
            />
          </Col>
          <Col lg={4} className="mb-4">
            <PremiumStatCard
              title="Upcoming Sessions"
              value={dashboardData?.stats.upcoming_count || 0}
              icon="bi-calendar-event"
              gradient={premiumStyles.appointmentsCard}
              subtitle="Next 7 days"
              trend="+3 this week"
            />
          </Col>
          <Col lg={4} className="mb-4">
            <PremiumStatCard
              title="Expert Rating"
              value={`${dashboardData?.stats.average_rating || 0}`}
              icon="bi-star-fill"
              gradient={premiumStyles.ratingCard}
              subtitle="Based on client reviews"
              trend="Excellent"
              showStars={true}
            />
          </Col>
        </Row>

        {/* Premium Performance Insights */}
        <Row className="mb-4">
          <Col>
            <Card className="premium-dashboard-card slide-in-up" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none'
            }}>
              <Card.Body className="p-4">
                <Row className="align-items-center">
                  <Col md={8}>
                    <h4 className="mb-2">
                      <i className="bi bi-graph-up-arrow me-2"></i>
                      Performance Insights
                    </h4>
                    <p className="mb-3 opacity-90">
                      You're doing great! Your expertise is helping farmers achieve better results.
                    </p>
                    <Row>
                      <Col sm={4}>
                        <div className="text-center">
                          <div className="h5 mb-1">{dashboardData?.calendar_bookings?.length || 0}</div>
                          <small className="opacity-75">Total Sessions</small>
                        </div>
                      </Col>
                      <Col sm={4}>
                        <div className="text-center">
                          <div className="h5 mb-1">
                            {dashboardData?.calendar_bookings?.filter(b => b.status === 'completed').length || 0}
                          </div>
                          <small className="opacity-75">Completed</small>
                        </div>
                      </Col>
                      <Col sm={4}>
                        <div className="text-center">
                          <div className="h5 mb-1">
                            {Math.round(((dashboardData?.calendar_bookings?.filter(b => b.status === 'completed').length || 0) /
                            (dashboardData?.calendar_bookings?.length || 1)) * 100)}%
                          </div>
                          <small className="opacity-75">Success Rate</small>
                        </div>
                      </Col>
                    </Row>
                  </Col>
                  <Col md={4} className="text-center">
                    <div className="position-relative">
                      <ProgressBar
                        now={Math.round(((dashboardData?.calendar_bookings?.filter(b => b.status === 'completed').length || 0) /
                        (dashboardData?.calendar_bookings?.length || 1)) * 100)}
                        style={{ height: '8px', borderRadius: '10px' }}
                        className="mb-3"
                      />
                      <div className="h2 mb-0">
                        <i className="bi bi-award-fill text-warning"></i>
                      </div>
                      <small className="opacity-75">Expert Status</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Premium Calendar and Sidebar Layout */}
        <Row>
          {/* Premium Calendar Section */}
          <Col lg={9} className="mb-4">
            <Card className="premium-dashboard-card calendar-premium">
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                padding: '1.5rem',
                borderRadius: '20px 20px 0 0'
              }}>
                <h4 className="mb-0">
                  <i className="bi bi-calendar3 me-2"></i>
                  Your Schedule
                </h4>
                <p className="mb-0 opacity-75">Manage your appointments and sessions</p>
              </div>
              <Card.Body className="p-3">
                <FullCalendar
                  plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                  initialView="timeGridWeek"
                  headerToolbar={{
                    left: "prev,next today",
                    center: "title",
                    right: "dayGridMonth,timeGridWeek,timeGridDay",
                  }}
                  events={formatEvents(dashboardData?.calendar_bookings || [])}
                  eventClick={handleEventClick}
                  height="auto"
                  eventDisplay="block"
                  dayMaxEvents={3}
                  moreLinkClick="popover"
                />
              </Card.Body>
            </Card>
          </Col>

          {/* Premium Sidebar */}
          <Col lg={3}>
            <PremiumQuickLinks />
            <PremiumActionItems items={dashboardData?.action_items || []} />
          </Col>
        </Row>
      </Container>

      {/* Floating Action Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Quick Actions"
        >
          <i className="bi bi-plus-lg"></i>
        </Button>
      </div>

      {/* Booking Detail Modal */}
      <BookingDetailModal
        show={showModal}
        onHide={() => setShowModal(false)}
        booking={selectedBooking}
        onMarkComplete={handleMarkComplete}
        userRole={user?.role}
      />
    </div>
  );
};

// --- Premium Child Components ---

const PremiumStatCard = ({ title, value, icon, gradient, subtitle, trend, showStars }) => (
  <Card className="stat-card-premium text-center h-100 slide-in-up">
    <Card.Body className="p-4" style={gradient}>
      <div className="mb-3">
        <i className={`bi ${icon}`} style={{ fontSize: "3rem", opacity: 0.9 }}></i>
      </div>
      <div className="stat-value mb-2">
        {showStars ? (
          <div>
            {[...Array(5)].map((_, i) => (
              <i
                key={i}
                className={`bi bi-star${i < Math.floor(value) ? '-fill' : ''}`}
                style={{ color: '#FFD700', fontSize: '1.5rem' }}
              ></i>
            ))}
            <div className="h3 mt-2">{value}</div>
          </div>
        ) : (
          value
        )}
      </div>
      <h5 className="mb-2 opacity-90">{title}</h5>
      <p className="mb-2 opacity-75 small">{subtitle}</p>
      {trend && (
        <Badge
          bg="light"
          text="dark"
          className="px-3 py-1"
          style={{ borderRadius: '15px', fontSize: '0.8rem' }}
        >
          {trend}
        </Badge>
      )}
    </Card.Body>
  </Card>
);

const PremiumQuickLinks = () => (
  <Card className="mb-4 premium-dashboard-card slide-in-up" style={premiumStyles.quickLinksCard}>
    <div style={{
      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      color: 'white',
      padding: '1.5rem',
      borderRadius: '20px 20px 0 0'
    }}>
      <h5 className="mb-0">
        <i className="bi bi-lightning-charge-fill me-2"></i>
        Quick Actions
      </h5>
    </div>
    <Card.Body className="p-4">
      <div className="d-grid gap-3">
        <Button
          variant="primary"
          as={Link}
          to="/expert/availability"
          className="quick-action-btn"
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none'
          }}
        >
          <i className="bi bi-calendar-plus me-2"></i>
          Manage Availability
        </Button>
        <Button
          variant="outline-success"
          as={Link}
          to="/expert/payouts"
          className="quick-action-btn"
          style={{ borderWidth: '2px' }}
        >
          <i className="bi bi-wallet2 me-2"></i>
          View Payouts
        </Button>
        <Button
          variant="outline-info"
          as={Link}
          to="/expert/profile"
          className="quick-action-btn"
          style={{ borderWidth: '2px' }}
        >
          <i className="bi bi-person-gear me-2"></i>
          Edit Profile
        </Button>
      </div>
    </Card.Body>
  </Card>
);

const PremiumActionItems = ({ items }) => (
  <Card className="premium-dashboard-card slide-in-up" style={premiumStyles.activityCard}>
    <div style={{
      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      color: 'white',
      padding: '1.5rem',
      borderRadius: '20px 20px 0 0'
    }}>
      <h5 className="mb-0">
        <i className="bi bi-activity me-2"></i>
        Recent Activity
      </h5>
    </div>
    <Card.Body className="p-0">
      {items.length > 0 ? (
        <ListGroup variant="flush">
          {items.map((item, index) => (
            <ListGroup.Item
              key={item.id}
              className="border-0 py-3 px-4"
              style={{
                backgroundColor: index % 2 === 0 ? 'rgba(79, 172, 254, 0.05)' : 'transparent',
                transition: 'background-color 0.2s ease'
              }}
            >
              <div className="d-flex align-items-start">
                <div
                  className="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3 flex-shrink-0"
                  style={{ width: '40px', height: '40px' }}
                >
                  <i className="bi bi-check-lg"></i>
                </div>
                <div className="flex-grow-1">
                  <div className="fw-semibold text-dark mb-1">
                    Session Completed
                  </div>
                  <div className="text-muted small">
                    <strong>{item.service.name}</strong> with {item.farmer.first_name} {item.farmer.last_name}
                  </div>
                  <div className="text-muted small mt-1">
                    <i className="bi bi-clock me-1"></i>
                    {new Date(item.booking_time).toLocaleDateString()}
                  </div>
                </div>
                <Badge bg="success" className="ms-2">
                  <i className="bi bi-currency-dollar"></i>
                </Badge>
              </div>
            </ListGroup.Item>
          ))}
        </ListGroup>
      ) : (
        <div className="text-center py-5">
          <div className="mb-3">
            <i className="bi bi-calendar-x text-muted" style={{ fontSize: '2.5rem' }}></i>
          </div>
          <h6 className="text-muted">No recent activity</h6>
          <p className="text-muted small mb-0">
            Your completed sessions will appear here
          </p>
        </div>
      )}
    </Card.Body>
  </Card>
);

export default ExpertDashboardPage;


