// src/components/ExpertModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const ExpertModal = ({ show, onHide, onSave, expert }) => {
  const [formData, setFormData] = useState({
    full_name: "",
    specialty: "",
    email: "",
    bio: "",
  });

  const isEditing = expert !== null;

  useEffect(() => {
    if (isEditing) {
      setFormData({
        full_name: `${expert.first_name} ${expert.last_name}`,
        specialty: expert.expert_profile?.specialty || "",
        email: expert.email,
        bio: expert.expert_profile?.bio || "",
      });
    } else {
      setFormData({
        full_name: "",
        specialty: "",
        email: "",
        bio: "",
      });
    }
  }, [expert, show, isEditing]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>{isEditing ? "Edit Expert" : "Add New Expert"}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Full Name</Form.Label>
            <Form.Control type="text" name="full_name" value={formData.full_name} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Specialty</Form.Label>
            <Form.Control
              type="text"
              name="specialty"
              value={formData.specialty}
              onChange={handleChange}
              placeholder="e.g., Crop Management"
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Email</Form.Label>
            <Form.Control type="email" name="email" value={formData.email} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Bio</Form.Label>
            <Form.Control as="textarea" rows={3} name="bio" value={formData.bio} onChange={handleChange} />
          </Form.Group>
          <Button type="submit" style={{ display: "none" }} />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSubmit}>
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ExpertModal;



