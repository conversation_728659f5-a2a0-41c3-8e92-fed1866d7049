// src/pages/FarmerBookingsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Row, Col, Badge, InputGroup, Form } from "react-bootstrap";
import { Link } from "react-router-dom";
import { getMyBookings } from "../services/bookingService";
import { createReview } from "../services/reviewService";
import ReviewModal from "../components/ReviewModal";

// Premium My Bookings CSS and Animations
const premiumCSS = `
  @keyframes bookingSlideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes statusPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes reviewButtonGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
    }
  }

  .premium-bookings-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: bookingSlideIn 0.6s ease-out;
  }

  .premium-bookings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .booking-row {
    border: none;
    border-radius: 15px;
    margin-bottom: 15px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    transition: all 0.3s ease;
    animation: bookingSlideIn 0.6s ease-out;
  }

  .booking-row:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    animation: statusPulse 2s infinite;
  }

  .status-confirmed {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
  }

  .status-completed {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .status-pending {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
  }

  .expert-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1rem;
    margin-right: 15px;
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  }

  .review-btn {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    animation: reviewButtonGlow 2s infinite;
  }

  .review-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
    background: linear-gradient(135deg, #ff8f00 0%, #ffc107 100%);
  }

  .service-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-right: 15px;
  }

  .booking-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 5px solid transparent;
  }

  .booking-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
  }

  .booking-card.confirmed {
    border-left-color: #43e97b;
  }

  .booking-card.completed {
    border-left-color: #667eea;
  }

  .booking-card.pending {
    border-left-color: #fa709a;
  }

  .search-filter-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .premium-search-input {
    border: none;
    border-radius: 15px;
    padding: 12px 20px;
    font-size: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .premium-search-input:focus {
    box-shadow: 0 5px 25px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
  }

  .filter-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 20px;
    color: white;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-btn:hover, .filter-btn.active {
    background: white;
    color: #667eea;
    border-color: white;
    transform: translateY(-2px);
  }

  .slide-in-up {
    animation: bookingSlideIn 0.6s ease-out;
  }

  .no-bookings-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  }
};

const FarmerBookingsPage = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [bookingToReview, setBookingToReview] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [filteredBookings, setFilteredBookings] = useState([]);

  // Status filters
  const statusFilters = [
    { id: "all", name: "All Bookings", icon: "bi-list-ul" },
    { id: "confirmed", name: "Confirmed", icon: "bi-check-circle" },
    { id: "completed", name: "Completed", icon: "bi-check-circle-fill" },
    { id: "pending", name: "Pending", icon: "bi-clock" }
  ];

  // Helper functions
  const getServiceIcon = (serviceName) => {
    const name = serviceName.toLowerCase();
    if (name.includes('crop')) return 'bi-flower1';
    if (name.includes('soil')) return 'bi-geo-alt';
    if (name.includes('pest')) return 'bi-bug';
    if (name.includes('irrigation')) return 'bi-droplet';
    if (name.includes('harvest')) return 'bi-calendar-check';
    return 'bi-briefcase';
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'confirmed': return 'confirmed';
      case 'completed': return 'completed';
      default: return 'pending';
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'confirmed': return 'status-confirmed';
      case 'completed': return 'status-completed';
      default: return 'status-pending';
    }
  };

  // Filter bookings based on search term and status
  useEffect(() => {
    let filtered = bookings;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${booking.expert.first_name} ${booking.expert.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    setFilteredBookings(filtered);
  }, [bookings, searchTerm, statusFilter]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const data = await getMyBookings();
      setBookings(data);
    } catch (err) {
      setError("Failed to load your bookings.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleShowReviewModal = (booking) => {
    setBookingToReview(booking);
    setShowReviewModal(true);
  };

  const handleCloseReviewModal = () => {
    setShowReviewModal(false);
    setBookingToReview(null);
  };

  const handleSubmitReview = async (reviewData) => {
    try {
      await createReview({ ...reviewData, booking: bookingToReview.id });
      handleCloseReviewModal();
      fetchBookings();
    } catch (err) {
      console.error("Failed to submit review:", err.response);
      let errorMessage = "An unknown error occurred.";
      if (err.response && err.response.data) {
        const errors = err.response.data;
        const errorDetails = Object.values(errors).flat().join(" ");
        errorMessage = errorDetails || "Failed to submit review.";
      }
      alert(errorMessage);
    }
  }; // <-- This closing brace was the missing piece.

  if (loading) {
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h3 className="mt-3 mb-2">Loading Your Bookings</h3>
          <p className="mb-0 opacity-75">Retrieving your consultation history and upcoming appointments...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <div className="premium-bookings-card p-4">
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Bookings Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </div>
      </Container>
    );
  }

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-calendar-check me-3"></i>
                My Bookings
              </h1>
              <p className="lead mb-0 opacity-90">
                Track your consultation history and manage upcoming appointments
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {filteredBookings.length} Bookings
                  </Badge>
                </div>
                <small className="opacity-75">Total Found</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Search and Filter Section */}
        <div className="search-filter-container slide-in-up">
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-3">
                <i className="bi bi-search me-2"></i>
                Find Your Bookings
              </h4>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search by service name or expert..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="premium-search-input"
                  style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                />
              </InputGroup>
            </Col>
            <Col md={4} className="text-center">
              <div>
                <div className="h2 mb-1">
                  <i className="bi bi-calendar-heart-fill"></i>
                </div>
                <h5 className="mb-0">Your Journey</h5>
                <small className="opacity-75">Agricultural consultations</small>
              </div>
            </Col>
          </Row>

          {/* Status Filters */}
          <div className="mt-4">
            <h6 className="mb-3">
              <i className="bi bi-funnel me-2"></i>
              Filter by Status
            </h6>
            <div className="d-flex flex-wrap">
              {statusFilters.map(filter => (
                <Button
                  key={filter.id}
                  className={`filter-btn ${statusFilter === filter.id ? 'active' : ''}`}
                  onClick={() => setStatusFilter(filter.id)}
                >
                  <i className={`${filter.icon} me-2`}></i>
                  {filter.name}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Premium Bookings Display */}
        {filteredBookings.length > 0 ? (
          <div className="bookings-grid">
            {filteredBookings.map((booking, index) => (
              <div
                key={booking.id}
                className={`booking-card ${getStatusClass(booking.status)} slide-in-up`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <Row className="align-items-center">
                  <Col md={1}>
                    <div className="service-icon">
                      <i className={getServiceIcon(booking.service.name)}></i>
                    </div>
                  </Col>
                  <Col md={3}>
                    <h5 className="mb-1" style={{ color: '#333' }}>{booking.service.name}</h5>
                    <small className="text-muted">Agricultural Consultation</small>
                  </Col>
                  <Col md={3}>
                    <div className="d-flex align-items-center">
                      <div className="expert-avatar">
                        {booking.expert.first_name[0]}{booking.expert.last_name[0]}
                      </div>
                      <div>
                        <div className="fw-semibold" style={{ color: '#333' }}>
                          {booking.expert.first_name} {booking.expert.last_name}
                        </div>
                        <small className="text-muted">Expert Advisor</small>
                      </div>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center">
                      <div className="fw-semibold" style={{ color: '#333' }}>
                        {new Date(booking.booking_time).toLocaleDateString()}
                      </div>
                      <small className="text-muted">
                        {new Date(booking.booking_time).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </small>
                    </div>
                  </Col>
                  <Col md={2}>
                    <div className="text-center">
                      <span className={`status-badge ${getStatusBadgeClass(booking.status)}`}>
                        {booking.status.replace("_", " ").toUpperCase()}
                      </span>
                    </div>
                  </Col>
                  <Col md={1}>
                    <div className="text-center">
                      {booking.status === "completed" && !booking.review ? (
                        <Button
                          className="review-btn"
                          size="sm"
                          onClick={() => handleShowReviewModal(booking)}
                          title="Leave a review"
                        >
                          <i className="bi bi-star-fill"></i>
                        </Button>
                      ) : booking.review ? (
                        <Badge bg="success" className="px-2 py-1" style={{ borderRadius: '10px' }}>
                          <i className="bi bi-check-circle"></i>
                        </Badge>
                      ) : (
                        <Button
                          variant="outline-primary"
                          size="sm"
                          as={Link}
                          to={`/live-session/${booking.id}`}
                          className="rounded-pill"
                          title="Join session"
                        >
                          <i className="bi bi-camera-video"></i>
                        </Button>
                      )}
                    </div>
                  </Col>
                </Row>
              </div>
            ))}
          </div>
        ) : bookings.length > 0 ? (
          <div className="text-center py-5">
            <div className="premium-bookings-card p-5">
              <div className="mb-3">
                <i className="bi bi-search text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <h4 className="text-muted">No bookings found</h4>
              <p className="text-muted mb-4">
                Try adjusting your search terms or status filter
              </p>
              <Button
                variant="outline-primary"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                }}
                className="rounded-pill px-4"
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <div className="no-bookings-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-calendar-x" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">No Bookings Yet</h3>
            <p className="mb-4 opacity-90">
              Start your agricultural journey by booking your first consultation with our expert advisors.
            </p>
            <Button
              variant="light"
              size="lg"
              as={Link}
              to="/browse-services"
              style={{ color: '#333', fontWeight: 'bold' }}
            >
              <i className="bi bi-plus-circle me-2"></i>
              Book Your First Service
            </Button>
          </div>
        )}
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help with your bookings?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>

      <ReviewModal show={showReviewModal} onHide={handleCloseReviewModal} onSubmit={handleSubmitReview} />
    </div>
  );
};

export default FarmerBookingsPage;


