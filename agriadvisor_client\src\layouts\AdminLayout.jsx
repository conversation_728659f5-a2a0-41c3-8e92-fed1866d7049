// src/layouts/AdminLayout.jsx

import React from "react";
import { Outlet } from "react-router-dom"; // We'll use this later to set the title
import Sidebar from "../components/Sidebar";
import Header from "../components/Header"; // <-- IMPORT HEADER

const layoutStyle = {
  display: "flex",
  height: "100vh",
  backgroundColor: "#f8f9fa",
};

const mainContentWrapperStyle = {
  flexGrow: 1,
  overflowY: "auto",
  display: "flex",
  flexDirection: "column",
};

const contentAreaStyle = {
  padding: "0 20px 20px 20px",
  flexGrow: 1,
  paddingTop: "20px", // Add top padding to account for sticky header
};

const AdminLayout = () => {
  // We can make this dynamic later based on the route
  const pageTitle = "Admin Dashboard";

  return (
    <div style={layoutStyle}>
      <Sidebar />
      <div style={mainContentWrapperStyle}>
        <Header pageTitle={pageTitle} />
        <main style={contentAreaStyle}>
          <Outlet /> {/* Child pages will be rendered here */}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;



