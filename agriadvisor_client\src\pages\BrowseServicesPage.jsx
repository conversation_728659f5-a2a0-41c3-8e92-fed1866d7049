import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, InputGroup, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { getFarmerServices } from "../services/serviceService";

// Premium Browse Services CSS and Animations
const premiumCSS = `
  @keyframes serviceCardSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes serviceCardHover {
    from {
      transform: translateY(0) scale(1);
    }
    to {
      transform: translateY(-10px) scale(1.02);
    }
  }

  @keyframes priceGlow {
    0%, 100% {
      text-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
    }
    50% {
      text-shadow: 0 0 20px rgba(67, 233, 123, 0.8);
    }
  }

  @keyframes searchPulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.1);
    }
  }

  .premium-service-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    animation: serviceCardSlideIn 0.6s ease-out;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }

  .premium-service-card:hover {
    animation: serviceCardHover 0.3s ease-out forwards;
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
  }

  .service-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 25px 25px 0 0;
    position: relative;
    overflow: hidden;
  }

  .service-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
  }

  .premium-service-card:hover .service-card-header::before {
    animation: shimmer 1s ease-in-out;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
  }

  .service-price {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 1.8rem;
    animation: priceGlow 2s infinite;
  }

  .service-duration {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
  }

  .book-service-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 15px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .book-service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(67, 233, 123, 0.4);
  }

  .book-service-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .book-service-btn:hover::before {
    left: 100%;
  }

  .search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .premium-search-input {
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .premium-search-input:focus {
    box-shadow: 0 5px 25px rgba(102, 126, 234, 0.3);
    animation: searchPulse 2s infinite;
  }

  .category-filter {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }

  .filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .filter-btn:hover, .filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .slide-in-up {
    animation: serviceCardSlideIn 0.6s ease-out;
  }

  .no-services-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  }
};

const BrowseServicesPage = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [filteredServices, setFilteredServices] = useState([]);

  // Service categories for filtering
  const categories = [
    { id: "all", name: "All Services", icon: "bi-grid-3x3-gap" },
    { id: "crop", name: "Crop Management", icon: "bi-flower1" },
    { id: "soil", name: "Soil Analysis", icon: "bi-geo-alt" },
    { id: "pest", name: "Pest Control", icon: "bi-bug" },
    { id: "irrigation", name: "Irrigation", icon: "bi-droplet" },
    { id: "harvest", name: "Harvest Planning", icon: "bi-calendar-check" }
  ];

  // Filter services based on search term and category
  useEffect(() => {
    let filtered = services;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category (simplified - in real app, services would have categories)
    if (selectedCategory !== "all") {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(selectedCategory) ||
        service.description.toLowerCase().includes(selectedCategory)
      );
    }

    setFilteredServices(filtered);
  }, [services, searchTerm, selectedCategory]);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const data = await getFarmerServices();
        setServices(data);
      } catch (err) {
        setError("Failed to load services. Please try again later.");
        console.error("Error fetching farmer services:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h3 className="mt-3 mb-2">Loading Services</h3>
          <p className="mb-0 opacity-75">Discovering the best agricultural advisory services for you...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <div className="premium-service-card p-4">
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Services Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </div>
      </Container>
    );
  }

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-shop me-3"></i>
                Browse Services
              </h1>
              <p className="lead mb-0 opacity-90">
                Discover expert agricultural advisory services tailored for your farming needs
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {filteredServices.length} Services
                  </Badge>
                </div>
                <small className="opacity-75">Available Now</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Search and Filter Section */}
        <div className="search-container slide-in-up">
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-3 text-white">
                <i className="bi bi-search me-2"></i>
                Find Your Perfect Service
              </h4>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search services by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="premium-search-input"
                  style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                />
              </InputGroup>
            </Col>
            <Col md={4} className="text-center">
              <div className="text-white">
                <div className="h2 mb-1">
                  <i className="bi bi-award-fill"></i>
                </div>
                <h5 className="mb-0">Expert Services</h5>
                <small className="opacity-75">Trusted by farmers</small>
              </div>
            </Col>
          </Row>
        </div>

        {/* Premium Category Filter */}
        <div className="category-filter slide-in-up">
          <h5 className="mb-3" style={{ color: '#333' }}>
            <i className="bi bi-funnel me-2"></i>
            Filter by Category
          </h5>
          <div className="d-flex flex-wrap">
            {categories.map(category => (
              <Button
                key={category.id}
                className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <i className={`${category.icon} me-2`}></i>
                {category.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Premium Services Grid */}
        {filteredServices.length > 0 ? (
          <Row xs={1} md={2} lg={3} xl={4} className="g-4">
            {filteredServices.map((service, index) => (
              <Col key={service.id}>
                <PremiumServiceCard
                  service={service}
                  animationDelay={index * 0.1}
                />
              </Col>
            ))}
          </Row>
        ) : services.length > 0 ? (
          <div className="text-center py-5">
            <div className="premium-service-card p-5">
              <div className="mb-3">
                <i className="bi bi-search text-muted" style={{ fontSize: '3rem' }}></i>
              </div>
              <h4 className="text-muted">No services found</h4>
              <p className="text-muted mb-4">
                Try adjusting your search terms or category filter
              </p>
              <Button
                variant="outline-primary"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("all");
                }}
                className="rounded-pill px-4"
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <div className="no-services-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-shop" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">No Services Available</h3>
            <p className="mb-4 opacity-90">
              We're working hard to bring you the best agricultural advisory services.
              Please check back soon!
            </p>
            <Button
              variant="light"
              size="lg"
              onClick={() => window.location.reload()}
              style={{ color: '#333', fontWeight: 'bold' }}
            >
              <i className="bi bi-arrow-clockwise me-2"></i>
              Refresh Page
            </Button>
          </div>
        )}
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help choosing a service?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>
    </div>
  );
};

// Premium Service Card Component
const PremiumServiceCard = ({ service, animationDelay = 0 }) => {
  const navigate = useNavigate();

  const handleBookService = () => {
    // Navigate to the NewBookingPage with the service data
    navigate(`/new-booking/${service.id}`, {
      state: { service }
    });
  };

  // Get service icon based on name/description
  const getServiceIcon = (service) => {
    const name = service.name.toLowerCase();
    const desc = service.description.toLowerCase();

    if (name.includes('crop') || desc.includes('crop')) return 'bi-flower1';
    if (name.includes('soil') || desc.includes('soil')) return 'bi-geo-alt';
    if (name.includes('pest') || desc.includes('pest')) return 'bi-bug';
    if (name.includes('irrigation') || desc.includes('irrigation')) return 'bi-droplet';
    if (name.includes('harvest') || desc.includes('harvest')) return 'bi-calendar-check';
    return 'bi-briefcase';
  };

  // Get service category color
  const getCategoryColor = (service) => {
    const name = service.name.toLowerCase();
    const desc = service.description.toLowerCase();

    if (name.includes('crop') || desc.includes('crop')) return '#43e97b';
    if (name.includes('soil') || desc.includes('soil')) return '#667eea';
    if (name.includes('pest') || desc.includes('pest')) return '#ff6b6b';
    if (name.includes('irrigation') || desc.includes('irrigation')) return '#4facfe';
    if (name.includes('harvest') || desc.includes('harvest')) return '#fa709a';
    return '#764ba2';
  };

  return (
    <Card
      className="premium-service-card h-100"
      style={{
        animationDelay: `${animationDelay}s`,
        '--category-color': getCategoryColor(service)
      }}
    >
      <div
        className="service-card-header"
        style={{
          background: `linear-gradient(135deg, ${getCategoryColor(service)} 0%, ${getCategoryColor(service)}dd 100%)`
        }}
      >
        <Row className="align-items-center">
          <Col xs={8}>
            <h5 className="mb-1 fw-bold">{service.name}</h5>
            <small className="opacity-75">Professional Advisory</small>
          </Col>
          <Col xs={4} className="text-end">
            <div
              className="rounded-circle bg-white d-flex align-items-center justify-content-center"
              style={{
                width: '50px',
                height: '50px',
                color: getCategoryColor(service),
                fontSize: '1.5rem'
              }}
            >
              <i className={getServiceIcon(service)}></i>
            </div>
          </Col>
        </Row>
      </div>

      <Card.Body className="d-flex flex-column p-4">
        <Card.Text className="flex-grow-1 mb-4" style={{ color: '#666', lineHeight: '1.6' }}>
          {service.description}
        </Card.Text>

        {/* Service Features */}
        <div className="mb-4">
          <Row className="text-center">
            <Col xs={6}>
              <div className="p-2">
                <i className="bi bi-clock-fill text-primary mb-1" style={{ fontSize: '1.2rem' }}></i>
                <div className="service-duration">
                  {service.duration_minutes} min
                </div>
              </div>
            </Col>
            <Col xs={6}>
              <div className="p-2">
                <i className="bi bi-star-fill text-warning mb-1" style={{ fontSize: '1.2rem' }}></i>
                <div style={{ fontSize: '0.85rem', fontWeight: '600', color: '#666' }}>
                  Expert Level
                </div>
              </div>
            </Col>
          </Row>
        </div>

        {/* Price and Book Button */}
        <div className="mt-auto">
          <div className="text-center mb-3">
            <div className="service-price">
              ${service.price}
            </div>
            <small className="text-muted">per consultation</small>
          </div>

          <Button
            className="book-service-btn w-100"
            onClick={handleBookService}
          >
            <i className="bi bi-calendar-plus me-2"></i>
            Book This Service
          </Button>
        </div>

        {/* Service Benefits */}
        <div className="mt-3 p-3 rounded-3" style={{
          background: 'linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%)'
        }}>
          <div className="d-flex align-items-center justify-content-center">
            <i className="bi bi-shield-check text-success me-2"></i>
            <small className="text-muted fw-semibold">
              Expert Guidance • Proven Results • 24/7 Support
            </small>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default BrowseServicesPage;

