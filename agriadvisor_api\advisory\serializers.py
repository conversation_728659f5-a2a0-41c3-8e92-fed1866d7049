# advisory/serializers.py

import decimal # <-- IMPORT at the top level
from rest_framework import serializers
from .models import Service, Booking
from accounts.models import CustomUser
from accounts.serializers import CustomUserSerializer # <-- IMPORTED

# --- This serializer should already be here ---
class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for the Service model.
    """
    class Meta:
        model = Service
        fields = ['id', 'name', 'description', 'price', 'currency', 'duration_minutes', 'tenant']
        read_only_fields = ['tenant']

# --- This is the complete and corrected BookingSerializer ---
class BookingSerializer(serializers.ModelSerializer):
    """
    Serializer for the Booking model.
    Handles nested representation for reads, flat IDs for writes,
    and calculates expert earnings.
    """
    # For READ operations: Use nested serializers to show full details.
    service = ServiceSerializer(read_only=True)
    expert = CustomUserSerializer(read_only=True)
    farmer = CustomUserSerializer(read_only=True)

    # For WRITE operations: Use PrimaryKeyRelatedField to accept simple IDs.
    service_id = serializers.PrimaryKeyRelatedField(
        queryset=Service.objects.all(), source='service', write_only=True
    )
    expert_id = serializers.PrimaryKeyRelatedField(
        queryset=CustomUser.objects.filter(role='expert'), source='expert', write_only=True
    )
    farmer_id = serializers.PrimaryKeyRelatedField(
        queryset=CustomUser.objects.filter(role='farmer'), source='farmer', write_only=True
    )

    # New calculated field for earnings
    expert_earnings = serializers.SerializerMethodField()

    class Meta:
        model = Booking
        # The complete fields list
        fields = [
            'id', 'booking_time', 'status',
            'service', 'expert', 'farmer',     # Read-only nested objects
            'expert_earnings',                # Read-only calculated field
            'service_id', 'expert_id', 'farmer_id' # Write-only ID fields
        ]
    
    def get_expert_earnings(self, obj):
        """Calculates the expert's earnings for a booking."""
        if obj.status in ['completed', 'confirmed'] and obj.service:
            platform_fee_percentage = 0.20 # This could be a setting
            earnings = obj.service.price * (1 - decimal.Decimal(platform_fee_percentage))
            return round(earnings, 2)
        return decimal.Decimal('0.00')

    def __init__(self, *args, **kwargs):
        """
        Dynamically filter the queryset for writeable fields based on the user's tenant.
        """
        super().__init__(*args, **kwargs)
        
        request = self.context.get('request')
        if request and hasattr(request, "user") and request.user.is_authenticated:
            user = request.user
            if user.tenant:
                self.fields['service_id'].queryset = Service.objects.filter(tenant=user.tenant)
                self.fields['expert_id'].queryset = CustomUser.objects.filter(tenant=user.tenant, role='expert')
                self.fields['farmer_id'].queryset = CustomUser.objects.filter(tenant=user.tenant, role='farmer')



# # advisory/serializers.py

# from rest_framework import serializers
# # Import all the models and the other serializers we need
# from .models import Service, Booking
# from accounts.models import CustomUser
# from accounts.serializers import CustomUserSerializer

# # ... (ServiceSerializer from previous step should be here) ...

# class ServiceSerializer(serializers.ModelSerializer):
#     """
#     Serializer for the Service model.
#     """
#     class Meta:
#         model = Service
#         # We explicitly list the fields to be included in the API representation.
#         fields = ['id', 'name', 'description', 'price', 'currency', 'duration_minutes', 'tenant']
        
#         # Make the 'tenant' field read-only in the API response.
#         # We don't want users to be able to assign a service to a different tenant.
#         # Our BaseTenantViewSet will handle setting the tenant automatically.
#         read_only_fields = ['tenant']



# class BookingSerializer(serializers.ModelSerializer):
#     """
#     Serializer for the Booking model.
#     Handles nested representation for reads and flat ID representation for writes.
#     """
#     # For READ operations: Use nested serializers to show full details.
#     # The 'read_only=True' is key.
#     service = ServiceSerializer(read_only=True)
#     expert = CustomUserSerializer(read_only=True)
#     farmer = CustomUserSerializer(read_only=True)

#     # For WRITE operations: Use PrimaryKeyRelatedField to accept simple IDs.
#     # The 'write_only=True' is key.
#     service_id = serializers.PrimaryKeyRelatedField(
#         queryset=Service.objects.all(), source='service', write_only=True
#     )
#     expert_id = serializers.PrimaryKeyRelatedField(
#         queryset=CustomUser.objects.filter(role='expert'), source='expert', write_only=True
#     )
#     farmer_id = serializers.PrimaryKeyRelatedField(
#         queryset=CustomUser.objects.filter(role='farmer'), source='farmer', write_only=True
#     )

#     class Meta:
#         model = Booking
#         fields = [
#             'id', 'booking_time', 'status',
#             'service', 'expert', 'farmer', # For reading
#             'service_id', 'expert_id', 'farmer_id' # For writing
#         ]

#     def __init__(self, *args, **kwargs):
#         """
#         Dynamically filter the queryset for writeable fields based on the user's tenant.
#         This prevents an admin from booking a service/expert from another tenant.
#         """
#         super().__init__(*args, **kwargs)
        
#         # Check if the request context is available
#         request = self.context.get('request')
#         if request and hasattr(request, "user"):
#             user = request.user
#             # Filter the choices for service_id, expert_id, and farmer_id
#             # to only show options from the admin's own tenant.
#             self.fields['service_id'].queryset = Service.objects.filter(tenant=user.tenant)
#             self.fields['expert_id'].queryset = CustomUser.objects.filter(tenant=user.tenant, role='expert')
#             self.fields['farmer_id'].queryset = CustomUser.objects.filter(tenant=user.tenant, role='farmer')