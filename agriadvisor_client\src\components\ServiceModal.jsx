// src/components/ServiceModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const ServiceModal = ({ show, onHide, onSave, service }) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    currency: "USD",
    duration_minutes: "",
  });

  const isEditing = service !== null;

  // This effect runs when the 'service' prop changes.
  // If we are editing, it pre-fills the form with the service's data.
  // If we are adding, it resets the form.
  useEffect(() => {
    if (isEditing) {
      setFormData({
        name: service.name,
        description: service.description,
        price: service.price,
        currency: service.currency,
        duration_minutes: service.duration_minutes,
      });
    } else {
      // Reset form for adding a new service
      setFormData({
        name: "",
        description: "",
        price: "",
        currency: "USD",
        duration_minutes: "",
      });
    }
  }, [service, show]); // Re-run effect when the service or show state changes

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>{isEditing ? "Edit Service" : "Add New Service"}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Service Name</Form.Label>
            <Form.Control type="text" name="name" value={formData.name} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Price</Form.Label>
            <Form.Control
              type="number"
              name="price"
              step="0.01"
              value={formData.price}
              onChange={handleChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Duration (minutes)</Form.Label>
            <Form.Control
              type="number"
              name="duration_minutes"
              value={formData.duration_minutes}
              onChange={handleChange}
              required
            />
          </Form.Group>
          {/* We hide the submit button in the form itself and use the modal footer */}
          <Button type="submit" style={{ display: "none" }} />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSubmit}>
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ServiceModal;



