# availability/views.py

from rest_framework.views import APIView
from rest_framework.response import Response
from accounts.models import CustomUser
from advisory.models import Service
from .utils import calculate_available_slots

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsExpertUser
from .models import AvailabilityRule, AvailabilityException
from .serializers import AvailabilityRuleSerializer, AvailabilityExceptionSerializer


class MyAvailabilityRuleViewSet(viewsets.ModelViewSet):
    """
    An endpoint for an Expert to manage their own recurring weekly availability.
    """
    serializer_class = AvailabilityRuleSerializer
    permission_classes = [IsAuthenticated, IsExpertUser]

    def get_queryset(self):
        return AvailabilityRule.objects.filter(expert=self.request.user)

    def perform_create(self, serializer):
        serializer.save(expert=self.request.user)

class MyAvailabilityExceptionViewSet(viewsets.ModelViewSet):
    """
    An endpoint for an Expert to manage their own one-off availability exceptions.
    """
    serializer_class = AvailabilityExceptionSerializer
    permission_classes = [IsAuthenticated, IsExpertUser]

    def get_queryset(self):
        return AvailabilityException.objects.filter(expert=self.request.user)

    def perform_create(self, serializer):
        serializer.save(expert=self.request.user)
        
        

class CheckAvailabilityView(APIView):
    """
    A read-only endpoint for a Farmer to check an expert's available slots.
    Accepts query parameters: expert_id, service_id, year, month.
    """
    permission_classes = [IsAuthenticated] # Any authenticated user can check

    def get(self, request, *args, **kwargs):
        try:
            expert_id = int(request.query_params.get('expert_id'))
            service_id = int(request.query_params.get('service_id'))
            year = int(request.query_params.get('year'))
            month = int(request.query_params.get('month'))
        except (TypeError, ValueError):
            return Response({"error": "Invalid or missing query parameters."}, status=400)

        try:
            # We check that the expert and service belong to the farmer's tenant for security
            farmer = request.user
            expert = CustomUser.objects.get(id=expert_id, tenant=farmer.tenant, role='expert')
            service = Service.objects.get(id=service_id, tenant=farmer.tenant)
        except (CustomUser.DoesNotExist, Service.DoesNotExist):
            return Response({"error": "Expert or service not found in your organization."}, status=404)

        available_slots = calculate_available_slots(expert, year, month, service.duration_minutes)
        
        # Convert datetime objects to ISO 8601 string format for JSON
        slots_iso = [slot.isoformat() for slot in available_slots]

        return Response(slots_iso)
