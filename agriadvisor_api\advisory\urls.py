# advisory/urls.py

from django.urls import path, include
from rest_framework.routers import DefaultRouter
# Make sure to import BookingViewSet
from .views import (ServiceViewSet, BookingViewSet, 
                    DashboardStatsView, FarmerServiceViewSet, 
                    ExpertBookingViewSet, FarmerBookingViewSet, 
                    FarmerDashboardView, ExpertDashboardView)

# Create a router and register our viewsets with it.
router = DefaultRouter()

# Admin endpoints
router.register(r'services', ServiceViewSet, basename='service')
router.register(r'bookings', BookingViewSet, basename='booking')

# Farmer endpoints
router.register(r'farmer/services', FarmerServiceViewSet, basename='farmer-service')

# --- ADD THIS NEW EXPERT ENDPOINT ---
router.register(r'expert/my-bookings', ExpertBookingViewSet, basename='expert-booking')

router.register(r'farmer/my-bookings', FarmerBookingViewSet, basename='farmer-booking')

# The API URLs are now determined automatically by the router.
urlpatterns = router.urls


# Add our custom view URL to the list

urlpatterns = router.urls + [
    # This URL is for the Admin dashboard stats
    path('dashboard/stats/', DashboardStatsView.as_view(), name='dashboard-stats'),
    
    # This is the new URL for the Farmer dashboard
    path('farmer/dashboard/', FarmerDashboardView.as_view(), name='farmer-dashboard'),
    
    # This is the new URL for the Expert dashboard
    path('expert/dashboard/', ExpertDashboardView.as_view(), name='expert-dashboard'),
]




