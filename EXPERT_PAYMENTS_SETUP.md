# Expert Payments Setup Guide

This guide explains how to resolve the "Could not initiate payment. The selected expert may not have payments enabled" error.

## Problem

The error occurs when trying to book an expert who doesn't have a Stripe Connect account configured. The payment system requires each expert to have a valid `stripe_account_id` to receive payments.

## Solution

### Option 1: Using Django Admin (Recommended for Quick Setup)

1. Log into the Django admin panel at `/admin/`
2. Navigate to **Accounts > Expert profiles**
3. Find the expert you want to enable payments for
4. Edit their profile and add a `stripe_account_id`
5. Set `payouts_enabled` to `True`
6. Save the changes

### Option 2: Using Management Command

Use the custom management command to list experts and set up payments:

```bash
# List all experts and their payment status
python manage.py setup_expert_payments --list

# Set up payments for a specific expert
python manage.py setup_expert_payments --expert-id 123 --stripe-account-id acct_1234567890
```

### Option 3: For Development/Testing

For development purposes, you can use test Stripe account IDs:

```bash
# Example with a test account ID
python manage.py setup_expert_payments --expert-id 1 --stripe-account-id acct_test_1234567890
```

## How the Fix Works

### Backend Changes

1. **Filtered Expert List**: The `FarmerExpertViewSet` now only returns experts who have valid Stripe account IDs
2. **Better Error Messages**: The payment service now returns specific error messages from the backend
3. **Payment Status Field**: Added a `payments_enabled` field to the expert profile serializer

### Frontend Changes

1. **Improved Error Handling**: The NewBookingPage now displays specific error messages from the backend
2. **Visual Indicators**: Expert selection dropdown shows payment status with checkmarks/warning icons
3. **Empty State**: Shows a helpful message when no payment-enabled experts are available

## Stripe Connect Setup (Production)

For production use, you'll need to:

1. Set up Stripe Connect in your Stripe dashboard
2. Implement the Stripe Connect onboarding flow for experts
3. Store the returned account IDs in the expert profiles

## Verification

After setting up an expert's payment capabilities:

1. The expert should appear in the booking form dropdown with a ✓ icon
2. Booking attempts should proceed to the payment modal without errors
3. In the admin panel, the expert's payment status should show "✓ Enabled"

## Troubleshooting

- **Expert not appearing in dropdown**: Check that they have a valid `stripe_account_id`
- **Still getting payment errors**: Verify the Stripe account ID is correct and active
- **No experts available**: Ensure at least one expert has payment capabilities enabled
