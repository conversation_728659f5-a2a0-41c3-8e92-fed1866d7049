#!/usr/bin/env python3
"""
Quick test script to verify authentication and API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8001/api"

def test_login():
    """Test login with admin credentials"""
    login_data = {
        "username": "admin_user",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/accounts/token/", json=login_data)
    print(f"Login Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        access_token = data.get('access')
        print("✅ Login successful!")
        return access_token
    else:
        print(f"❌ Login failed: {response.text}")
        return None

def test_experts_endpoint(token):
    """Test the experts endpoint with authentication"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/accounts/experts/", headers=headers)
    print(f"Experts Endpoint Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Found {len(data)} experts")
        for expert in data:
            rating = expert.get('average_rating', 0)
            reviews = expert.get('review_count', 0)
            print(f"  - {expert['first_name']} {expert['last_name']}: ⭐{rating} ({reviews} reviews)")
        return True
    else:
        print(f"❌ Experts endpoint failed: {response.text}")
        return False

def test_create_expert(token):
    """Test creating a new expert"""
    headers = {"Authorization": f"Bearer {token}"}
    
    expert_data = {
        "full_name": "Test Expert New",
        "email": "<EMAIL>",
        "expert_profile": {
            "specialty": "Livestock Management",
            "bio": "Expert in livestock management and animal health."
        }
    }
    
    response = requests.post(f"{BASE_URL}/accounts/experts/", json=expert_data, headers=headers)
    print(f"Create Expert Status: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        print(f"✅ Created expert: {data['first_name']} {data['last_name']}")
        return True
    else:
        print(f"❌ Create expert failed: {response.text}")
        return False

if __name__ == "__main__":
    print("🧪 Testing API Authentication and Endpoints\n")
    
    # Test login
    token = test_login()
    if not token:
        exit(1)
    
    print()
    
    # Test experts endpoint
    test_experts_endpoint(token)
    
    print()
    
    # Test creating expert
    test_create_expert(token)
    
    print("\n🎉 Test completed!")
