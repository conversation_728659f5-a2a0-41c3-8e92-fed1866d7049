// src/pages/BookingsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, InputGroup, Form } from "react-bootstrap";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import bootstrap5Plugin from "@fullcalendar/bootstrap5";


// Import all the services we need
import { getBookings, createBooking, updateBooking, deleteBooking } from "../services/bookingService";
import { getServices } from "../services/serviceService";
import { getExperts } from "../services/expertService";
import { getFarmers } from "../services/farmerService";

// Import our new modal
import BookingModal from "../components/BookingModal";

// Premium Admin Bookings CSS and Animations
const premiumCSS = `
  @keyframes calendarSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes eventPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }

  @keyframes statsGlow {
    0%, 100% {
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    }
    50% {
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }
  }

  .premium-bookings-container {
    animation: calendarSlideIn 0.6s ease-out;
  }

  .premium-calendar-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }

  .premium-calendar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .calendar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 25px 25px 0 0;
  }

  .stats-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(168, 237, 234, 0.3);
    transition: all 0.3s ease;
    animation: statsGlow 3s infinite;
  }

  .stats-card:hover {
    transform: translateY(-3px);
    animation: none;
  }

  .filter-container {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(252, 182, 159, 0.3);
  }

  .view-btn {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 8px 16px;
    margin: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .view-btn:hover, .view-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .add-booking-btn {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .add-booking-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(67, 233, 123, 0.4);
  }

  .add-booking-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .add-booking-btn:hover::before {
    left: 100%;
  }

  .slide-in-up {
    animation: calendarSlideIn 0.6s ease-out;
  }

  .no-data-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 25px;
    color: white;
    text-align: center;
    padding: 60px 40px;
    box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
  }

  /* Custom FullCalendar Styling */
  .fc {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  .fc-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 15px;
    margin-bottom: 20px;
  }

  .fc-toolbar-title {
    color: white !important;
    font-weight: 700;
    font-size: 1.5rem;
  }

  .fc-button {
    background: rgba(255,255,255,0.2) !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
  }

  .fc-button:hover {
    background: white !important;
    color: #667eea !important;
    transform: translateY(-2px);
  }

  .fc-button-active {
    background: white !important;
    color: #667eea !important;
  }

  .fc-event {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3) !important;
    transition: all 0.3s ease !important;
  }

  .fc-event:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 5px 15px rgba(67, 233, 123, 0.5) !important;
    animation: eventPulse 1s infinite;
  }

  .fc-daygrid-day {
    transition: all 0.3s ease;
  }

  .fc-daygrid-day:hover {
    background: rgba(102, 126, 234, 0.05) !important;
  }

  .fc-day-today {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%) !important;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  }
};

const BookingsPage = () => {
  // State for data
  const [events, setEvents] = useState([]);
  const [allBookings, setAllBookings] = useState([]);
  const [services, setServices] = useState([]);
  const [experts, setExperts] = useState([]);
  const [farmers, setFarmers] = useState([]);

  // State for UI control
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentView, setCurrentView] = useState("dayGridMonth");

  // State for modal management
  const [showModal, setShowModal] = useState(false);
  const [editingBooking, setEditingBooking] = useState(null); // The full event object from FullCalendar
  const [selectedDate, setSelectedDate] = useState(null); // The date string like '2025-08-25'

  // Status filters
  const statusFilters = [
    { id: "all", name: "All Bookings", icon: "bi-list-ul" },
    { id: "confirmed", name: "Confirmed", icon: "bi-check-circle" },
    { id: "completed", name: "Completed", icon: "bi-check-circle-fill" },
    { id: "pending_payment", name: "Pending Payment", icon: "bi-clock" }
  ];

  // Calendar view options
  const viewOptions = [
    { id: "dayGridMonth", name: "Month", icon: "bi-calendar3" },
    { id: "timeGridWeek", name: "Week", icon: "bi-calendar-week" },
    { id: "timeGridDay", name: "Day", icon: "bi-calendar-day" }
  ];

  // Helper functions
  const getBookingStats = () => {
    const totalBookings = allBookings.length;
    const confirmedBookings = allBookings.filter(b => b.status === 'confirmed').length;
    const completedBookings = allBookings.filter(b => b.status === 'completed').length;
    const pendingBookings = allBookings.filter(b => b.status === 'pending_payment').length;

    return {
      total: totalBookings,
      confirmed: confirmedBookings,
      completed: completedBookings,
      pending: pendingBookings
    };
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return '#43e97b';
      case 'completed': return '#667eea';
      case 'pending_payment': return '#fa709a';
      default: return '#6c757d';
    }
  };

  // Filter bookings based on search and status
  const filterBookings = (bookings) => {
    let filtered = bookings;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${booking.farmer.first_name} ${booking.farmer.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${booking.expert.first_name} ${booking.expert.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    return filtered;
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      // Fetch all required data in parallel for efficiency
      const [bookingsData, servicesData, expertsData, farmersData] = await Promise.all([
        getBookings(),
        getServices(),
        getExperts(),
        getFarmers(),
      ]);

      setAllBookings(bookingsData);
      const filteredBookings = filterBookings(bookingsData);
      const formatted = formatEvents(filteredBookings);
      setEvents(formatted);
      setServices(servicesData);
      setExperts(expertsData);
      setFarmers(farmersData);
    } catch (err) {
      setError("Failed to load page data. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Update events when filters change
  useEffect(() => {
    if (allBookings.length > 0) {
      const filteredBookings = filterBookings(allBookings);
      const formatted = formatEvents(filteredBookings);
      setEvents(formatted);
    }
  }, [searchTerm, statusFilter, allBookings]);

  useEffect(() => {
    fetchData();
  }, []);

  const formatEvents = (bookings) => {
    return bookings.map((booking) => ({
      id: booking.id,
      title: `${booking.service.name} - ${booking.farmer.first_name} ${booking.farmer.last_name}`,
      start: booking.booking_time,
      backgroundColor: getStatusColor(booking.status),
      borderColor: getStatusColor(booking.status),
      textColor: 'white',
      extendedProps: {
        ...booking,
        booking: booking, // Store the full booking object for modal use
        status: booking.status,
      },
    }));
  };

  // --- Event Handlers for Calendar ---
  const handleDateClick = (arg) => {
    // --- ADD THIS LOG ---
    console.log("Date clicked!", arg.dateStr);
    // Open modal for CREATING a new event
    setSelectedDate(arg.dateStr); // e.g., "2025-08-26"
    setEditingBooking(null);
    setShowModal(true);
  };

  const handleEventClick = (clickInfo) => {
    // Open modal for EDITING an existing event
    setSelectedDate(null);
    setEditingBooking(clickInfo.event);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingBooking(null);
    setSelectedDate(null);
  };

  // --- CRUD Handlers ---
  const handleSaveBooking = async (formData) => {
    try {
      if (editingBooking) {
        // Update logic
        await updateBooking(editingBooking.id, formData);
      } else {
        // Create logic
        await createBooking(formData);
      }
      handleCloseModal();
      fetchData(); // Refresh all data
    } catch (err) {
      console.error("Failed to save booking:", err);
      // You could set an error state here to show in the modal
      alert("Failed to save booking. Check console for details.");
    }
  };

  const handleDeleteBooking = async () => {
    if (editingBooking && window.confirm("Are you sure you want to delete this booking?")) {
      try {
        await deleteBooking(editingBooking.id);
        handleCloseModal();
        fetchData(); // Refresh all data
      } catch (err) {
        console.error("Failed to delete booking:", err);
        alert("Failed to delete booking.");
      }
    }
  };

  if (loading) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-calendar-event me-3"></i>
                  Manage Bookings
                </h1>
                <p className="lead mb-0 opacity-90">
                  Loading booking calendar and appointments...
                </p>
              </Col>
              <Col xs="auto">
                <Button className="add-booking-btn" disabled>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Loading...
                </Button>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="text-center" style={{ marginTop: '5rem' }}>
            <div className="mx-auto" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              maxWidth: '500px',
              padding: '3rem',
              borderRadius: '25px',
              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
            }}>
              <Spinner animation="border" size="lg" style={{ color: 'white' }} />
              <h3 className="mt-3 mb-2">Loading Booking Calendar</h3>
              <p className="mb-0 opacity-75">Preparing appointment management system...</p>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-calendar-event me-3"></i>
                  Manage Bookings
                </h1>
                <p className="lead mb-0 opacity-90">
                  Booking management system
                </p>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Bookings Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </Container>
      </div>
    );
  }

  // Ensure we have the necessary data to populate the modal's dropdowns
  if (!services.length || !experts.length || !farmers.length) {
    return (
      <div>
        {/* Premium Page Header */}
        <div style={premiumStyles.pageHeader}>
          <Container>
            <Row className="align-items-center">
              <Col>
                <h1 className="display-4 fw-bold mb-2">
                  <i className="bi bi-calendar-event me-3"></i>
                  Manage Bookings
                </h1>
                <p className="lead mb-0 opacity-90">
                  Booking management system
                </p>
              </Col>
            </Row>
          </Container>
        </div>

        <Container>
          <div className="no-data-card slide-in-up">
            <div className="mb-4">
              <i className="bi bi-exclamation-triangle" style={{ fontSize: '4rem' }}></i>
            </div>
            <h3 className="mb-3">Setup Required</h3>
            <p className="mb-4 opacity-90">
              Please add at least one Service, Expert, and Farmer before creating bookings.
            </p>
            <div className="d-flex gap-3 justify-content-center flex-wrap">
              <Button variant="light" href="/admin/services" style={{ color: '#333', fontWeight: 'bold' }}>
                <i className="bi bi-grid me-2"></i>
                Add Services
              </Button>
              <Button variant="light" href="/admin/experts" style={{ color: '#333', fontWeight: 'bold' }}>
                <i className="bi bi-people me-2"></i>
                Add Experts
              </Button>
              <Button variant="light" href="/admin/farmers" style={{ color: '#333', fontWeight: 'bold' }}>
                <i className="bi bi-house-heart me-2"></i>
                Add Farmers
              </Button>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  const stats = getBookingStats();

  return (
    <div className="premium-bookings-container">
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-calendar-event me-3"></i>
                Manage Bookings
              </h1>
              <p className="lead mb-0 opacity-90">
                Comprehensive booking calendar and appointment management
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {events.length} Events
                  </Badge>
                </div>
                <small className="opacity-75">Currently Showing</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Booking Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="stats-card slide-in-up p-4">
              <Row className="text-center">
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#667eea' }}>
                    <i className="bi bi-calendar-check-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.total}</h6>
                  <small className="text-muted">Total Bookings</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                    <i className="bi bi-check-circle-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.confirmed}</h6>
                  <small className="text-muted">Confirmed</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#764ba2' }}>
                    <i className="bi bi-check-all"></i>
                  </div>
                  <h6 className="mb-1">{stats.completed}</h6>
                  <small className="text-muted">Completed</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                    <i className="bi bi-clock-fill"></i>
                  </div>
                  <h6 className="mb-1">{stats.pending}</h6>
                  <small className="text-muted">Pending Payment</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Premium Filter and Search Section */}
        <div className="filter-container slide-in-up">
          <Row className="align-items-center">
            <Col md={6}>
              <h5 className="mb-3" style={{ color: '#333' }}>
                <i className="bi bi-search me-2"></i>
                Search & Filter Bookings
              </h5>
              <InputGroup size="lg">
                <InputGroup.Text style={{
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '15px 0 0 15px'
                }}>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search by service, farmer, or expert..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    borderLeft: 'none',
                    borderRadius: '0 15px 15px 0',
                    fontSize: '1.1rem',
                    padding: '15px 20px'
                  }}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <h5 className="mb-3" style={{ color: '#333' }}>
                <i className="bi bi-funnel me-2"></i>
                Status Filter
              </h5>
              <div className="d-flex flex-wrap">
                {statusFilters.map(filter => (
                  <Button
                    key={filter.id}
                    className={`view-btn ${statusFilter === filter.id ? 'active' : ''}`}
                    onClick={() => setStatusFilter(filter.id)}
                  >
                    <i className={`${filter.icon} me-2`}></i>
                    {filter.name}
                  </Button>
                ))}
              </div>
            </Col>
          </Row>
        </div>

        {/* Add Booking Button */}
        <Row className="mb-4">
          <Col className="text-end">
            <Button
              className="add-booking-btn"
              onClick={() => {
                setSelectedDate(new Date().toISOString().split('T')[0]);
                setEditingBooking(null);
                setShowModal(true);
              }}
            >
              <i className="bi bi-plus-circle me-2"></i>
              Add New Booking
            </Button>
          </Col>
        </Row>

        {/* Premium Calendar - Preserving Original Functionality */}
        <div className="premium-calendar-card slide-in-up">
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, bootstrap5Plugin]}
            themeSystem="bootstrap5"
            initialView="dayGridMonth"
            headerToolbar={{
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay",
            }}
            events={events}
            eventClick={handleEventClick}
            dateClick={handleDateClick}
            editable={true}
            selectable={true}
          />
        </div>

        {/* Calendar Legend */}
        <Row className="mt-4">
          <Col>
            <Card className="border-0 rounded-4" style={{ background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)' }}>
              <Card.Body className="p-3">
                <h6 className="mb-3">
                  <i className="bi bi-info-circle me-2"></i>
                  Booking Status Legend
                </h6>
                <Row className="text-center">
                  <Col xs={6} md={3}>
                    <div className="d-flex align-items-center justify-content-center">
                      <div
                        style={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: getStatusColor('confirmed'),
                          borderRadius: '4px',
                          marginRight: '8px'
                        }}
                      ></div>
                      <small>Confirmed</small>
                    </div>
                  </Col>
                  <Col xs={6} md={3}>
                    <div className="d-flex align-items-center justify-content-center">
                      <div
                        style={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: getStatusColor('completed'),
                          borderRadius: '4px',
                          marginRight: '8px'
                        }}
                      ></div>
                      <small>Completed</small>
                    </div>
                  </Col>
                  <Col xs={6} md={3}>
                    <div className="d-flex align-items-center justify-content-center">
                      <div
                        style={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: getStatusColor('pending_payment'),
                          borderRadius: '4px',
                          marginRight: '8px'
                        }}
                      ></div>
                      <small>Pending Payment</small>
                    </div>
                  </Col>
                  <Col xs={6} md={3}>
                    <div className="text-center">
                      <small className="text-muted">
                        <i className="bi bi-mouse me-1"></i>
                        Click events to edit
                      </small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help managing bookings?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>

      {/* Conditionally render the modal to ensure it gets the latest props */}
      {showModal && (
        <BookingModal
          show={showModal}
          onHide={handleCloseModal}
          onSave={handleSaveBooking}
          booking={editingBooking?.extendedProps || null} // Pass the raw booking data
          selectedDate={selectedDate}
          services={services}
          experts={experts}
          farmers={farmers}
          onDelete={handleDeleteBooking}
        />
      )}
    </div>
  );
};

export default BookingsPage;
