{"version": 3, "file": "main.js", "sources": ["src/AllDaySplitter.ts", "src/TimeColsAxisCell.tsx", "src/TimeBodyAxis.tsx", "src/TimeColsView.tsx", "src/TimeColsSlatsCoords.ts", "src/TimeColsSlatsBody.tsx", "src/TimeColsSlats.tsx", "src/TimeColsSeg.ts", "src/TimeColMoreLink.tsx", "src/seg-web.ts", "src/event-placement.ts", "src/TimeColEvent.tsx", "src/TimeColMisc.tsx", "src/TimeCol.tsx", "src/TimeColsContent.tsx", "src/TimeCols.tsx", "src/DayTimeColsSlicer.ts", "src/DayTimeCols.tsx", "src/time-slat-meta.ts", "src/DayTimeColsView.tsx", "src/options.ts", "src/main.ts"], "sourcesContent": ["import {\n  Splitter,\n  hasBgRendering,\n  EventDef,\n  DateSpan,\n} from '@fullcalendar/common'\n\nexport class AllDaySplitter extends Splitter {\n  getKeyInfo() {\n    return {\n      allDay: {},\n      timed: {},\n    }\n  }\n\n  getKeysForDateSpan(dateSpan: DateSpan): string[] {\n    if (dateSpan.allDay) {\n      return ['allDay']\n    }\n\n    return ['timed']\n  }\n\n  getKeysForEventDef(eventDef: EventDef): string[] {\n    if (!eventDef.allDay) {\n      return ['timed']\n    }\n\n    if (hasBgRendering(eventDef)) {\n      return ['timed', 'allDay']\n    }\n\n    return ['allDay']\n  }\n}\n", "import {\n  createElement,\n  ViewContext,\n  createFormatter,\n  ViewContextType,\n  RenderHook,\n  SlotLabelContentArg,\n} from '@fullcalendar/common'\nimport { TimeSlatMeta } from './time-slat-meta'\n\nconst DEFAULT_SLAT_LABEL_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'short',\n})\n\nexport function TimeColsAxisCell(props: TimeSlatMeta) {\n  let classNames = [\n    'fc-timegrid-slot',\n    'fc-timegrid-slot-label',\n    props.isLabeled ? 'fc-scrollgrid-shrink' : 'fc-timegrid-slot-minor',\n  ]\n\n  return (\n    <ViewContextType.Consumer>\n      {(context: ViewContext) => {\n        if (!props.isLabeled) {\n          return (\n            <td className={classNames.join(' ')} data-time={props.isoTimeStr} />\n          )\n        }\n\n        let { dateEnv, options, viewApi } = context\n        let labelFormat = // TODO: fully pre-parse\n          options.slotLabelFormat == null ? DEFAULT_SLAT_LABEL_FORMAT :\n            Array.isArray(options.slotLabelFormat) ? createFormatter(options.slotLabelFormat[0]) :\n              createFormatter(options.slotLabelFormat)\n\n        let hookProps: SlotLabelContentArg = {\n          level: 0,\n          time: props.time,\n          date: dateEnv.toDate(props.date),\n          view: viewApi,\n          text: dateEnv.format(props.date, labelFormat),\n        }\n\n        return (\n          <RenderHook<SlotLabelContentArg> // needed?\n            hookProps={hookProps}\n            classNames={options.slotLabelClassNames}\n            content={options.slotLabelContent}\n            defaultContent={renderInnerContent}\n            didMount={options.slotLabelDidMount}\n            willUnmount={options.slotLabelWillUnmount}\n          >\n            {(rootElRef, customClassNames, innerElRef, innerContent) => (\n              <td ref={rootElRef} className={classNames.concat(customClassNames).join(' ')} data-time={props.isoTimeStr}>\n                <div className=\"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame\">\n                  <div className=\"fc-timegrid-slot-label-cushion fc-scrollgrid-shrink-cushion\" ref={innerElRef}>\n                    {innerContent}\n                  </div>\n                </div>\n              </td>\n            )}\n          </RenderHook>\n        )\n      }}\n    </ViewContextType.Consumer>\n  )\n}\n\nfunction renderInnerContent(props) { // TODO: add types\n  return props.text\n}\n", "import { createElement, BaseComponent } from '@fullcalendar/common'\nimport { TimeColsAxisCell } from './TimeColsAxisCell'\nimport { TimeSlatMeta } from './time-slat-meta'\n\n/* Thin Axis\n------------------------------------------------------------------------------------------------------------------*/\n\ninterface TimeBodyAxisProps {\n  slatMetas: TimeSlatMeta[]\n}\n\nexport class TimeBodyAxis extends BaseComponent<TimeBodyAxisProps> { // just <tr> content\n  render() {\n    return this.props.slatMetas.map((slatMeta: TimeSlatMeta) => (\n      <tr key={slatMeta.key}>\n        <TimeColsAxisCell {...slatMeta} />\n      </tr>\n    ))\n  }\n}\n", "import {\n  createElement, createRef,\n  diffDays,\n  SimpleScrollGridSection,\n  VNode,\n  SimpleScrollGrid,\n  ChunkContentCallbackArgs,\n  ScrollGridSectionConfig,\n  buildNavLinkAttrs,\n  ViewRoot,\n  WeekNumberRoot,\n  RenderHook,\n  DateComponent,\n  ViewProps,\n  RefObject,\n  renderScrollShim,\n  getStickyHeaderDates,\n  getStickyFooterScrollbar,\n  createFormatter,\n  AllDayContentArg,\n  CssDimValue,\n  NowTimer,\n  DateMarker,\n  NowIndicatorRoot,\n} from '@fullcalendar/common'\nimport { AllDaySplitter } from './AllDaySplitter'\nimport { TimeSlatMeta } from './time-slat-meta'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { TimeBodyAxis } from './TimeBodyAxis'\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'short' })\nconst AUTO_ALL_DAY_MAX_EVENT_ROWS = 5\n\n/* An abstract class for all timegrid-related views. Displays one more columns with time slots running vertically.\n----------------------------------------------------------------------------------------------------------------------*/\n// Is a manager for the TimeCols subcomponent and possibly the DayGrid subcomponent (if allDaySlot is on).\n// Responsible for managing width/height.\n\ninterface TimeColsViewState {\n  slatCoords: TimeColsSlatsCoords | null\n}\n\nexport abstract class TimeColsView extends DateComponent<ViewProps, TimeColsViewState> {\n  protected allDaySplitter = new AllDaySplitter() // for use by subclasses\n\n  protected headerElRef: RefObject<HTMLTableCellElement> = createRef<HTMLTableCellElement>()\n  private rootElRef: RefObject<HTMLDivElement> = createRef<HTMLDivElement>()\n  private scrollerElRef: RefObject<HTMLDivElement> = createRef<HTMLDivElement>()\n\n  state = {\n    slatCoords: null,\n  }\n\n  // rendering\n  // ----------------------------------------------------------------------------------------------------\n\n  renderSimpleLayout(\n    headerRowContent: VNode | null,\n    allDayContent: ((contentArg: ChunkContentCallbackArgs) => VNode) | null,\n    timeContent: ((contentArg: ChunkContentCallbackArgs) => VNode) | null,\n  ) {\n    let { context, props } = this\n    let sections: SimpleScrollGridSection[] = []\n    let stickyHeaderDates = getStickyHeaderDates(context.options)\n\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent,\n        },\n      })\n    }\n\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        chunk: { content: allDayContent },\n      })\n      sections.push({\n        type: 'body',\n        key: 'all-day-divider',\n        outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n          <tr role=\"presentation\" className=\"fc-scrollgrid-section\">\n            <td\n              className={'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')}\n            />\n          </tr>\n        ),\n      })\n    }\n\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunk: {\n        scrollerElRef: this.scrollerElRef,\n        content: timeContent,\n      },\n    })\n\n    return (\n      <ViewRoot viewSpec={context.viewSpec} elRef={this.rootElRef}>\n        {(rootElRef, classNames) => (\n          <div className={['fc-timegrid'].concat(classNames).join(' ')} ref={rootElRef}>\n            <SimpleScrollGrid\n              liquid={!props.isHeightAuto && !props.forPrint}\n              collapsibleWidth={props.forPrint}\n              cols={[{ width: 'shrink' }]}\n              sections={sections}\n            />\n          </div>\n        )}\n      </ViewRoot>\n    )\n  }\n\n  renderHScrollLayout(\n    headerRowContent: VNode | null,\n    allDayContent: ((contentArg: ChunkContentCallbackArgs) => VNode) | null,\n    timeContent: ((contentArg: ChunkContentCallbackArgs) => VNode) | null,\n    colCnt: number,\n    dayMinWidth: number,\n    slatMetas: TimeSlatMeta[],\n    slatCoords: TimeColsSlatsCoords | null, // yuck\n  ) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl\n\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation')\n    }\n\n    let { context, props } = this\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options)\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options)\n    let sections: ScrollGridSectionConfig[] = []\n\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        syncRowHeights: true,\n        chunks: [\n          {\n            key: 'axis',\n            rowContent: (arg: ChunkContentCallbackArgs) => (\n              <tr role=\"presentation\">\n                {this.renderHeadAxis('day', arg.rowSyncHeights[0])}\n              </tr>\n            ),\n          },\n          {\n            key: 'cols',\n            elRef: this.headerElRef,\n            tableClassName: 'fc-col-header',\n            rowContent: headerRowContent,\n          },\n        ],\n      })\n    }\n\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        syncRowHeights: true,\n        chunks: [\n          {\n            key: 'axis',\n            rowContent: (contentArg: ChunkContentCallbackArgs) => (\n              <tr role=\"presentation\">\n                {this.renderTableRowAxis(contentArg.rowSyncHeights[0])}\n              </tr>\n            ),\n          },\n          {\n            key: 'cols',\n            content: allDayContent,\n          },\n        ],\n      })\n      sections.push({\n        key: 'all-day-divider',\n        type: 'body',\n        outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n          <tr role=\"presentation\" className=\"fc-scrollgrid-section\">\n            <td\n              colSpan={2}\n              className={'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')}\n            />\n          </tr>\n        ),\n      })\n    }\n\n    let isNowIndicator = context.options.nowIndicator\n\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunks: [\n        {\n          key: 'axis',\n          content: (arg) => (\n            // TODO: make this now-indicator arrow more DRY with TimeColsContent\n            <div className=\"fc-timegrid-axis-chunk\">\n              <table aria-hidden style={{ height: arg.expandRows ? arg.clientHeight : '' }}>\n                {arg.tableColGroupNode}\n                <tbody>\n                  <TimeBodyAxis slatMetas={slatMetas} />\n                </tbody>\n              </table>\n              <div className=\"fc-timegrid-now-indicator-container\">\n                <NowTimer unit={isNowIndicator ? 'minute' : 'day' /* hacky */}>\n                  {(nowDate: DateMarker) => {\n                    let nowIndicatorTop =\n                      isNowIndicator &&\n                      slatCoords &&\n                      slatCoords.safeComputeTop(nowDate) // might return void\n\n                    if (typeof nowIndicatorTop === 'number') {\n                      return (\n                        <NowIndicatorRoot isAxis date={nowDate}>\n                          {(rootElRef, classNames, innerElRef, innerContent) => (\n                            <div\n                              ref={rootElRef}\n                              className={['fc-timegrid-now-indicator-arrow'].concat(classNames).join(' ')}\n                              style={{ top: nowIndicatorTop }}\n                            >\n                              {innerContent}\n                            </div>\n                          )}\n                        </NowIndicatorRoot>\n                      )\n                    }\n\n                    return null\n                  }}\n                </NowTimer>\n              </div>\n            </div>\n          ),\n        },\n        {\n          key: 'cols',\n          scrollerElRef: this.scrollerElRef,\n          content: timeContent,\n        },\n      ],\n    })\n\n    if (stickyFooterScrollbar) {\n      sections.push({\n        key: 'footer',\n        type: 'footer',\n        isSticky: true,\n        chunks: [\n          {\n            key: 'axis',\n            content: renderScrollShim,\n          },\n          {\n            key: 'cols',\n            content: renderScrollShim,\n          },\n        ],\n      })\n    }\n\n    return (\n      <ViewRoot viewSpec={context.viewSpec} elRef={this.rootElRef}>\n        {(rootElRef, classNames) => (\n          <div className={['fc-timegrid'].concat(classNames).join(' ')} ref={rootElRef}>\n            <ScrollGrid\n              liquid={!props.isHeightAuto && !props.forPrint}\n              collapsibleWidth={false}\n              colGroups={[\n                { width: 'shrink', cols: [{ width: 'shrink' }] }, // TODO: allow no specify cols\n                { cols: [{ span: colCnt, minWidth: dayMinWidth }] },\n              ]}\n              sections={sections}\n            />\n          </div>\n        )}\n      </ViewRoot>\n    )\n  }\n\n  handleScrollTopRequest = (scrollTop: number) => {\n    let scrollerEl = this.scrollerElRef.current\n\n    if (scrollerEl) { // TODO: not sure how this could ever be null. weirdness with the reducer\n      scrollerEl.scrollTop = scrollTop\n    }\n  }\n\n  /* Dimensions\n  ------------------------------------------------------------------------------------------------------------------*/\n\n  getAllDayMaxEventProps() {\n    let { dayMaxEvents, dayMaxEventRows } = this.context.options\n\n    if (dayMaxEvents === true || dayMaxEventRows === true) { // is auto?\n      dayMaxEvents = undefined\n      dayMaxEventRows = AUTO_ALL_DAY_MAX_EVENT_ROWS // make sure \"auto\" goes to a real number\n    }\n\n    return { dayMaxEvents, dayMaxEventRows }\n  }\n\n  /* Header Render Methods\n  ------------------------------------------------------------------------------------------------------------------*/\n\n  renderHeadAxis = (rowKey: 'day' | string, frameHeight: CssDimValue = '') => {\n    let { options } = this.context\n    let { dateProfile } = this.props\n    let range = dateProfile.renderRange\n    let dayCnt = diffDays(range.start, range.end)\n\n    let navLinkAttrs = (dayCnt === 1) // only do in day views (to avoid doing in week views that dont need it)\n      ? buildNavLinkAttrs(this.context, range.start, 'week')\n      : {}\n\n    if (options.weekNumbers && rowKey === 'day') {\n      return (\n        <WeekNumberRoot date={range.start} defaultFormat={DEFAULT_WEEK_NUM_FORMAT}>\n          {(rootElRef, classNames, innerElRef, innerContent) => (\n            <th\n              ref={rootElRef}\n              aria-hidden\n              className={[\n                'fc-timegrid-axis',\n                'fc-scrollgrid-shrink',\n              ].concat(classNames).join(' ')}\n            >\n              <div\n                className=\"fc-timegrid-axis-frame fc-scrollgrid-shrink-frame fc-timegrid-axis-frame-liquid\"\n                style={{ height: frameHeight }}\n              >\n                <a\n                  ref={innerElRef}\n                  className=\"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner\"\n                  {...navLinkAttrs}\n                >\n                  {innerContent}\n                </a>\n              </div>\n            </th>\n          )}\n        </WeekNumberRoot>\n      )\n    }\n\n    return (\n      <th aria-hidden className=\"fc-timegrid-axis\">\n        <div className=\"fc-timegrid-axis-frame\" style={{ height: frameHeight }} />\n      </th>\n    )\n  }\n\n  /* Table Component Render Methods\n  ------------------------------------------------------------------------------------------------------------------*/\n\n  // only a one-way height sync. we don't send the axis inner-content height to the DayGrid,\n  // but DayGrid still needs to have classNames on inner elements in order to measure.\n  renderTableRowAxis = (rowHeight?: number) => {\n    let { options, viewApi } = this.context\n    let hookProps: AllDayContentArg = {\n      text: options.allDayText,\n      view: viewApi,\n    }\n\n    return (\n      // TODO: make reusable hook. used in list view too\n      <RenderHook<AllDayContentArg>\n        hookProps={hookProps}\n        classNames={options.allDayClassNames}\n        content={options.allDayContent}\n        defaultContent={renderAllDayInner}\n        didMount={options.allDayDidMount}\n        willUnmount={options.allDayWillUnmount}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent) => (\n          <td\n            ref={rootElRef}\n            aria-hidden\n            className={[\n              'fc-timegrid-axis',\n              'fc-scrollgrid-shrink',\n            ].concat(classNames).join(' ')}\n          >\n            <div\n              className={'fc-timegrid-axis-frame fc-scrollgrid-shrink-frame' + (rowHeight == null ? ' fc-timegrid-axis-frame-liquid' : '')}\n              style={{ height: rowHeight }}\n            >\n              <span className=\"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner\" ref={innerElRef}>\n                {innerContent}\n              </span>\n            </div>\n          </td>\n        )}\n      </RenderHook>\n    )\n  }\n\n  handleSlatCoords = (slatCoords: TimeColsSlatsCoords) => {\n    this.setState({ slatCoords })\n  }\n}\n\nfunction renderAllDayInner(hookProps) {\n  return hookProps.text\n}\n", "import {\n  <PERSON>si<PERSON><PERSON><PERSON>,\n  DateMarker,\n  startOfDay,\n  createDuration,\n  asRoughMs,\n  DateProfile,\n  Duration,\n  rangeContainsMarker,\n} from '@fullcalendar/common'\n\nexport class TimeColsSlatsCoords {\n  constructor(\n    public positions: PositionCache,\n    private dateProfile: DateProfile,\n    private slotDuration: Duration,\n  ) {\n  }\n\n  safeComputeTop(date: DateMarker) { // TODO: DRY with computeDateTop\n    let { dateProfile } = this\n\n    if (rangeContainsMarker(dateProfile.currentRange, date)) {\n      let startOfDayDate = startOfDay(date)\n      let timeMs = date.valueOf() - startOfDayDate.valueOf()\n\n      if (\n        timeMs >= asRoughMs(dateProfile.slotMinTime) &&\n        timeMs < asRoughMs(dateProfile.slotMaxTime)\n      ) {\n        return this.computeTimeTop(createDuration(timeMs))\n      }\n    }\n\n    return null\n  }\n\n  // Computes the top coordinate, relative to the bounds of the grid, of the given date.\n  // A `startOfDayDate` must be given for avoiding ambiguity over how to treat midnight.\n  computeDateTop(when: DateMarker, startOfDayDate?: DateMarker) {\n    if (!startOfDayDate) {\n      startOfDayDate = startOfDay(when)\n    }\n    return this.computeTimeTop(createDuration(when.valueOf() - startOfDayDate.valueOf()))\n  }\n\n  // Computes the top coordinate, relative to the bounds of the grid, of the given time (a Duration).\n  // This is a makeshify way to compute the time-top. Assumes all slatMetas dates are uniform.\n  // Eventually allow computation with arbirary slat dates.\n  computeTimeTop(duration: Duration): number {\n    let { positions, dateProfile } = this\n    let len = positions.els.length\n\n    // floating-point value of # of slots covered\n    let slatCoverage = (duration.milliseconds - asRoughMs(dateProfile.slotMinTime)) / asRoughMs(this.slotDuration)\n    let slatIndex\n    let slatRemainder\n\n    // compute a floating-point number for how many slats should be progressed through.\n    // from 0 to number of slats (inclusive)\n    // constrained because slotMinTime/slotMaxTime might be customized.\n    slatCoverage = Math.max(0, slatCoverage)\n    slatCoverage = Math.min(len, slatCoverage)\n\n    // an integer index of the furthest whole slat\n    // from 0 to number slats (*exclusive*, so len-1)\n    slatIndex = Math.floor(slatCoverage)\n    slatIndex = Math.min(slatIndex, len - 1)\n\n    // how much further through the slatIndex slat (from 0.0-1.0) must be covered in addition.\n    // could be 1.0 if slatCoverage is covering *all* the slots\n    slatRemainder = slatCoverage - slatIndex\n\n    return positions.tops[slatIndex] +\n      positions.getHeight(slatIndex) * slatRemainder\n  }\n}\n", "import {\n  createElement,\n  BaseComponent,\n  RefMap,\n  RenderHook,\n  SlotLaneContentArg,\n} from '@fullcalendar/common'\nimport { TimeColsAxisCell } from './TimeColsAxisCell'\nimport { TimeSlatMeta } from './time-slat-meta'\n\nexport interface TimeColsSlatsBodyProps {\n  axis: boolean\n  slatMetas: TimeSlatMeta[]\n  slatElRefs: RefMap<HTMLTableRowElement>\n}\n\nexport class TimeColsSlatsBody extends BaseComponent<TimeColsSlatsBodyProps> {\n  render() {\n    let { props, context } = this\n    let { options } = context\n    let { slatElRefs } = props\n\n    return (\n      <tbody>\n        {props.slatMetas.map((slatMeta, i) => {\n          let hookProps: SlotLaneContentArg = {\n            time: slatMeta.time,\n            date: context.dateEnv.toDate(slatMeta.date),\n            view: context.viewApi,\n          }\n\n          let classNames = [\n            'fc-timegrid-slot',\n            'fc-timegrid-slot-lane',\n            slatMeta.isLabeled ? '' : 'fc-timegrid-slot-minor',\n          ]\n\n          return (\n            <tr\n              key={slatMeta.key}\n              ref={slatElRefs.createRef(slatMeta.key)}\n            >\n              {props.axis && (\n                <TimeColsAxisCell {...slatMeta} />\n              )}\n              <RenderHook\n                hookProps={hookProps}\n                classNames={options.slotLaneClassNames}\n                content={options.slotLaneContent}\n                didMount={options.slotLaneDidMount}\n                willUnmount={options.slotLaneWillUnmount}\n              >\n                {(rootElRef, customClassNames, innerElRef, innerContent) => (\n                  <td\n                    ref={rootElRef}\n                    className={classNames.concat(customClassNames).join(' ')}\n                    data-time={slatMeta.isoTimeStr}\n                  >\n                    {innerContent}\n                  </td>\n                )}\n              </RenderHook>\n            </tr>\n          )\n        })}\n      </tbody>\n    )\n  }\n}\n", "import {\n  createElement,\n  VNode,\n  BaseComponent,\n  RefMap,\n  CssDimValue,\n  createRef,\n  PositionCache,\n  DateProfile,\n} from '@fullcalendar/common'\nimport { TimeSlatMeta } from './time-slat-meta'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { TimeColsSlatsBody } from './TimeColsSlatsBody'\n\nexport interface TimeColsSlatsProps extends TimeColsSlatsContentProps {\n  dateProfile: DateProfile\n  clientWidth: number | null\n  minHeight: CssDimValue\n  tableMinWidth: CssDimValue\n  tableColGroupNode: VNode\n  onCoords?: (coords: TimeColsSlatsCoords | null) => void\n}\n\ninterface TimeColsSlatsContentProps {\n  axis: boolean\n  slatMetas: TimeSlatMeta[]\n}\n\n/*\nfor the horizontal \"slats\" that run width-wise. Has a time axis on a side. Depends on RTL.\n*/\n\nexport class TimeColsSlats extends BaseComponent<TimeColsSlatsProps> {\n  private rootElRef = createRef<HTMLDivElement>()\n  private slatElRefs = new RefMap<HTMLTableRowElement>()\n\n  render() {\n    let { props, context } = this\n\n    return (\n      <div ref={this.rootElRef} className=\"fc-timegrid-slots\">\n        <table\n          aria-hidden\n          className={context.theme.getClass('table')}\n          style={{\n            minWidth: props.tableMinWidth,\n            width: props.clientWidth,\n            height: props.minHeight,\n          }}\n        >\n          {props.tableColGroupNode /* relies on there only being a single <col> for the axis */}\n          <TimeColsSlatsBody\n            slatElRefs={this.slatElRefs}\n            axis={props.axis}\n            slatMetas={props.slatMetas}\n          />\n        </table>\n      </div>\n    )\n  }\n\n  componentDidMount() {\n    this.updateSizing()\n  }\n\n  componentDidUpdate() {\n    this.updateSizing()\n  }\n\n  componentWillUnmount() {\n    if (this.props.onCoords) {\n      this.props.onCoords(null)\n    }\n  }\n\n  updateSizing() {\n    let { context, props } = this\n\n    if (\n      props.onCoords &&\n      props.clientWidth !== null // means sizing has stabilized\n    ) {\n      let rootEl = this.rootElRef.current\n\n      if (rootEl.offsetHeight) { // not hidden by css\n        props.onCoords(\n          new TimeColsSlatsCoords(\n            new PositionCache(\n              this.rootElRef.current,\n              collectSlatEls(this.slatElRefs.currentMap, props.slatMetas),\n              false,\n              true, // vertical\n            ),\n            this.props.dateProfile,\n            context.options.slotDuration,\n          ),\n        )\n      }\n    }\n  }\n}\n\nfunction collectSlatEls(elMap: { [key: string]: HTMLElement }, slatMetas: TimeSlatMeta[]) {\n  return slatMetas.map((slatMeta) => elMap[slatMeta.key])\n}\n", "import { DateMarker, Seg, EventSegUiInteractionState } from '@fullcalendar/common'\n\n// JUST A DATA STRUCTURE, not a component\n\nexport interface TimeColsSeg extends Seg {\n  col: number\n  start: DateMarker\n  end: DateMarker\n}\n\nexport function splitSegsByCol(segs: TimeColsSeg[] | null, colCnt: number) { // can be given null/undefined!\n  let segsByCol: TimeColsSeg[][] = []\n  let i\n\n  for (i = 0; i < colCnt; i += 1) {\n    segsByCol.push([])\n  }\n\n  if (segs) {\n    for (i = 0; i < segs.length; i += 1) {\n      segsByCol[segs[i].col].push(segs[i])\n    }\n  }\n\n  return segsByCol\n}\n\nexport function splitInteractionByCol(ui: EventSegUiInteractionState | null, colCnt: number) {\n  let byRow: EventSegUiInteractionState[] = []\n\n  if (!ui) {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = null\n    }\n  } else {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: [],\n      }\n    }\n\n    for (let seg of ui.segs) {\n      byRow[seg.col].segs.push(seg)\n    }\n  }\n\n  return byRow\n}\n", "import {\n  createElement, MoreLinkContentArg, MoreLinkRoot, BaseComponent, createRef, setRef,\n  Dictionary, DateProfile, DateRange, DateMarker, EventSegUiInteractionState, CssDimValue,\n} from '@fullcalendar/common'\nimport { renderPlainFgSegs } from './TimeCol'\nimport { TimeColsSeg } from './TimeColsSeg'\n\nexport interface TimeColMoreLinkProps {\n  hiddenSegs: TimeColsSeg[]\n  top: CssDimValue\n  bottom: CssDimValue\n  extraDateSpan?: Dictionary\n  dateProfile: DateProfile\n  todayRange: DateRange\n  nowDate: DateMarker\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState\n  eventResize: EventSegUiInteractionState\n}\n\nexport class TimeColMoreLink extends BaseComponent<TimeColMoreLinkProps> {\n  rootElRef = createRef<HTMLElement>()\n\n  render() {\n    let { props } = this\n    return (\n      <MoreLinkRoot\n        allDayDate={null}\n        moreCnt={props.hiddenSegs.length}\n        allSegs={props.hiddenSegs}\n        hiddenSegs={props.hiddenSegs}\n        alignmentElRef={this.rootElRef}\n        defaultContent={renderMoreLinkInner}\n        extraDateSpan={props.extraDateSpan}\n        dateProfile={props.dateProfile}\n        todayRange={props.todayRange}\n        popoverContent={() => renderPlainFgSegs(props.hiddenSegs, props)}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent, handleClick, title, isExpanded, popoverId) => (\n          <a\n            ref={(el: HTMLElement | null) => {\n              setRef(rootElRef, el)\n              setRef(this.rootElRef, el)\n            }}\n            className={['fc-timegrid-more-link'].concat(classNames).join(' ')}\n            style={{ top: props.top, bottom: props.bottom }}\n            onClick={handleClick}\n            title={title}\n            aria-expanded={isExpanded}\n            aria-controls={popoverId}\n          >\n            <div ref={innerElRef} className=\"fc-timegrid-more-link-inner fc-sticky\">\n              {innerContent}\n            </div>\n          </a>\n        )}\n      </MoreLinkRoot>\n    )\n  }\n}\n\nfunction renderMoreLinkInner(props: MoreLinkContentArg) {\n  return props.shortText\n}\n", "import {\n  SegEntry,\n  SegHierarchy,\n  SegRect,\n  buildEntry<PERSON>ey,\n  getEntrySpanEnd,\n  binarySearch,\n  SegEntryGroup,\n  groupIntersectingEntries,\n} from '@fullcalendar/common'\n\ninterface SegNode extends SegEntry {\n  nextLevelNodes: SegNode[] // with highest-pressure first\n}\n\ntype SegNodeAndPressure = [ SegNode, number ]\n\ninterface SegSiblingRange { // will ALWAYS have span of 1 or more items. if not, will be null\n  level: number\n  lateralStart: number\n  lateralEnd: number\n}\n\nexport interface SegWebRect extends SegRect {\n  stackDepth: number\n  stackForward: number\n}\n\n// segInputs assumed sorted\nexport function buildPositioning(\n  segInputs: SegEntry[],\n  strictOrder?: boolean,\n  maxStackCnt?: number,\n): { segRects: SegWebRect[], hiddenGroups: SegEntryGroup[] } {\n  let hierarchy = new SegHierarchy()\n  if (strictOrder != null) {\n    hierarchy.strictOrder = strictOrder\n  }\n  if (maxStackCnt != null) {\n    hierarchy.maxStackCnt = maxStackCnt\n  }\n\n  let hiddenEntries = hierarchy.addSegs(segInputs)\n  let hiddenGroups = groupIntersectingEntries(hiddenEntries)\n\n  let web = buildWeb(hierarchy)\n  web = stretchWeb(web, 1) // all levelCoords/thickness will have 0.0-1.0\n  let segRects = webToRects(web)\n\n  return { segRects, hiddenGroups }\n}\n\nfunction buildWeb(hierarchy: SegHierarchy): SegNode[] {\n  const { entriesByLevel } = hierarchy\n\n  const buildNode = cacheable(\n    (level: number, lateral: number) => level + ':' + lateral,\n    (level: number, lateral: number): SegNodeAndPressure => {\n      let siblingRange = findNextLevelSegs(hierarchy, level, lateral)\n      let nextLevelRes = buildNodes(siblingRange, buildNode)\n      let entry = entriesByLevel[level][lateral]\n\n      return [\n        { ...entry, nextLevelNodes: nextLevelRes[0] },\n        entry.thickness + nextLevelRes[1], // the pressure builds\n      ]\n    },\n  )\n\n  return buildNodes(\n    entriesByLevel.length\n      ? { level: 0, lateralStart: 0, lateralEnd: entriesByLevel[0].length }\n      : null,\n    buildNode,\n  )[0]\n}\n\nfunction buildNodes(\n  siblingRange: SegSiblingRange | null,\n  buildNode: (level: number, lateral: number) => SegNodeAndPressure,\n): [SegNode[], number] { // number is maxPressure\n  if (!siblingRange) {\n    return [[], 0]\n  }\n\n  let { level, lateralStart, lateralEnd } = siblingRange\n  let lateral = lateralStart\n  let pairs: SegNodeAndPressure[] = []\n\n  while (lateral < lateralEnd) {\n    pairs.push(buildNode(level, lateral))\n    lateral += 1\n  }\n\n  pairs.sort(cmpDescPressures)\n\n  return [\n    pairs.map(extractNode),\n    pairs[0][1], // first item's pressure\n  ]\n}\n\nfunction cmpDescPressures(a: SegNodeAndPressure, b: SegNodeAndPressure) { // sort pressure high -> low\n  return b[1] - a[1]\n}\n\nfunction extractNode(a: SegNodeAndPressure): SegNode {\n  return a[0]\n}\n\nfunction findNextLevelSegs(hierarchy: SegHierarchy, subjectLevel: number, subjectLateral: number): SegSiblingRange | null {\n  let { levelCoords, entriesByLevel } = hierarchy\n  let subjectEntry = entriesByLevel[subjectLevel][subjectLateral]\n  let afterSubject = levelCoords[subjectLevel] + subjectEntry.thickness\n  let levelCnt = levelCoords.length\n  let level = subjectLevel\n\n  // skip past levels that are too high up\n  for (; level < levelCnt && levelCoords[level] < afterSubject; level += 1) ; // do nothing\n\n  for (; level < levelCnt; level += 1) {\n    let entries = entriesByLevel[level]\n    let entry: SegEntry\n    let searchIndex = binarySearch(entries, subjectEntry.span.start, getEntrySpanEnd)\n    let lateralStart = searchIndex[0] + searchIndex[1] // if exact match (which doesn't collide), go to next one\n    let lateralEnd = lateralStart\n\n    while ( // loop through entries that horizontally intersect\n      (entry = entries[lateralEnd]) && // but not past the whole seg list\n      entry.span.start < subjectEntry.span.end\n    ) { lateralEnd += 1 }\n\n    if (lateralStart < lateralEnd) {\n      return { level, lateralStart, lateralEnd }\n    }\n  }\n\n  return null\n}\n\nfunction stretchWeb(topLevelNodes: SegNode[], totalThickness: number): SegNode[] {\n  const stretchNode = cacheable(\n    (node: SegNode, startCoord: number, prevThickness: number) => buildEntryKey(node),\n    (node: SegNode, startCoord: number, prevThickness: number): [number, SegNode] => { // [startCoord, node]\n      let { nextLevelNodes, thickness } = node\n      let allThickness = thickness + prevThickness\n      let thicknessFraction = thickness / allThickness\n      let endCoord: number\n      let newChildren: SegNode[] = []\n\n      if (!nextLevelNodes.length) {\n        endCoord = totalThickness\n      } else {\n        for (let childNode of nextLevelNodes) {\n          if (endCoord === undefined) {\n            let res = stretchNode(childNode, startCoord, allThickness)\n            endCoord = res[0]\n            newChildren.push(res[1])\n          } else {\n            let res = stretchNode(childNode, endCoord, 0)\n            newChildren.push(res[1])\n          }\n        }\n      }\n\n      let newThickness = (endCoord - startCoord) * thicknessFraction\n      return [endCoord - newThickness, {\n        ...node,\n        thickness: newThickness,\n        nextLevelNodes: newChildren,\n      }]\n    },\n  )\n\n  return topLevelNodes.map((node: SegNode) => stretchNode(node, 0, 0)[1])\n}\n\n// not sorted in any particular order\nfunction webToRects(topLevelNodes: SegNode[]): SegWebRect[] {\n  let rects: SegWebRect[] = []\n\n  const processNode = cacheable(\n    (node: SegNode, levelCoord: number, stackDepth: number) => buildEntryKey(node),\n    (node: SegNode, levelCoord: number, stackDepth: number) => { // returns forwardPressure\n      let rect: SegWebRect = {\n        ...node,\n        levelCoord,\n        stackDepth,\n        stackForward: 0, // will assign after recursing\n      }\n      rects.push(rect)\n\n      return (\n        rect.stackForward = processNodes(node.nextLevelNodes, levelCoord + node.thickness, stackDepth + 1) + 1\n      )\n    },\n  )\n\n  function processNodes(nodes: SegNode[], levelCoord: number, stackDepth: number) { // returns stackForward\n    let stackForward = 0\n    for (let node of nodes) {\n      stackForward = Math.max(processNode(node, levelCoord, stackDepth), stackForward)\n    }\n    return stackForward\n  }\n\n  processNodes(topLevelNodes, 0, 0)\n  return rects // TODO: sort rects by levelCoord to be consistent with toRects?\n}\n\n// TODO: move to general util\n\nfunction cacheable<Args extends any[], Res>(\n  keyFunc: (...args: Args) => string,\n  workFunc: (...args: Args) => Res,\n): ((...args: Args) => Res) {\n  const cache: { [key: string]: Res } = {}\n\n  return (...args: Args) => {\n    let key = keyFunc(...args)\n    return (key in cache)\n      ? cache[key]\n      : (cache[key] = workFunc(...args))\n  }\n}\n", "import {\n  Seg<PERSON><PERSON>,\n  Seg<PERSON><PERSON><PERSON>,\n  Seg<PERSON><PERSON>ryG<PERSON>,\n  DateMarker,\n} from '@fullcalendar/common'\nimport { TimeColsSeg } from './TimeColsSeg'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { SegWebRect, buildPositioning } from './seg-web'\n\n// public interface\n// ------------------------------------------------------------------------------------------\n\nexport interface TimeColFgSegPlacement {\n  seg: TimeColsSeg\n  rect: SegWebRect | null\n}\n\nexport function computeSegVCoords(\n  segs: TimeColsSeg[],\n  colDate: DateMarker,\n  slatCoords: TimeColsSlatsCoords = null,\n  eventMinHeight: number = 0, // might be null/undefined :(\n): SegSpan[] {\n  let vcoords: SegSpan[] = []\n\n  if (slatCoords) {\n    for (let i = 0; i < segs.length; i += 1) {\n      let seg = segs[i]\n      let spanStart = slatCoords.computeDateTop(seg.start, colDate)\n      let spanEnd = Math.max(\n        spanStart + (eventMinHeight || 0), // :(\n        slatCoords.computeDateTop(seg.end, colDate),\n      )\n      vcoords.push({\n        start: Math.round(spanStart), // for barely-overlapping collisions\n        end: Math.round(spanEnd), //\n      })\n    }\n  }\n\n  return vcoords\n}\n\nexport function computeFgSegPlacements(\n  segs: TimeColsSeg[],\n  segVCoords: SegSpan[], // might not have for every seg\n  eventOrderStrict?: boolean,\n  eventMaxStack?: number,\n): { segPlacements: TimeColFgSegPlacement[], hiddenGroups: SegEntryGroup[] } {\n  let segInputs: SegEntry[] = []\n  let dumbSegs: TimeColsSeg[] = [] // segs without coords\n\n  for (let i = 0; i < segs.length; i += 1) {\n    let vcoords = segVCoords[i]\n    if (vcoords) {\n      segInputs.push({\n        index: i,\n        thickness: 1,\n        span: vcoords,\n      })\n    } else {\n      dumbSegs.push(segs[i])\n    }\n  }\n\n  let { segRects, hiddenGroups } = buildPositioning(segInputs, eventOrderStrict, eventMaxStack)\n  let segPlacements: TimeColFgSegPlacement[] = []\n\n  for (let segRect of segRects) {\n    segPlacements.push({\n      seg: segs[segRect.index],\n      rect: segRect,\n    })\n  }\n\n  for (let dumbSeg of dumbSegs) {\n    segPlacements.push({ seg: dumbSeg, rect: null })\n  }\n\n  return { segPlacements, hiddenGroups }\n}\n", "import { createElement, StandardEvent, BaseComponent, MinimalEventProps, createFormatter } from '@fullcalendar/common'\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: false,\n})\n\nexport interface TimeColEventProps extends MinimalEventProps {\n  isShort: boolean\n}\n\nexport class TimeColEvent extends BaseComponent<TimeColEventProps> {\n  render() {\n    let classNames = [\n      'fc-timegrid-event',\n      'fc-v-event',\n    ]\n\n    if (this.props.isShort) {\n      classNames.push('fc-timegrid-event-short')\n    }\n\n    return (\n      <StandardEvent\n        {...this.props}\n        defaultTimeFormat={DEFAULT_TIME_FORMAT}\n        extraClassNames={classNames}\n      />\n    )\n  }\n}\n", "import {\n  DateMarker, BaseComponent, createElement,\n  DateR<PERSON><PERSON>, Day<PERSON><PERSON><PERSON>ontent, DateProfile,\n} from '@fullcalendar/common'\n\nexport interface TimeColMiscProps { // should be given nowDate too??\n  dateProfile: DateProfile\n  date: DateMarker\n  todayRange: DateRange\n  extraHookProps?: any\n}\n\nexport class TimeColMisc extends BaseComponent<TimeColMiscProps> {\n  render() {\n    let { props } = this\n\n    return (\n      <DayCellContent date={props.date} dateProfile={props.dateProfile} todayRange={props.todayRange} extraHookProps={props.extraHookProps}>\n        {(innerElRef, innerContent) => (\n          innerContent &&\n            <div className=\"fc-timegrid-col-misc\" ref={innerElRef}>{innerContent}</div>\n        )}\n      </DayCellContent>\n    )\n  }\n}\n", "import {\n  Ref, DateMarker, BaseComponent, createElement, EventSegUiInteractionState, Seg, getSegMeta,\n  DateRange, Fragment, DayCellRoot, NowIndicatorRoot, BgEvent, renderFill, buildIsoString, computeEarliestSegStart,\n  DateProfile, buildEventRangeKey, sortEventSegs, memoize, SegEntryGroup, SegEntry, Dictionary, SegSpan, CssDimValue,\n} from '@fullcalendar/common'\nimport { TimeColMoreLink } from './TimeColMoreLink'\nimport { TimeColsSeg } from './TimeColsSeg'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { SegWebRect } from './seg-web'\nimport { computeFgSegPlacements, computeSegVCoords } from './event-placement'\nimport { TimeColEvent } from './TimeColEvent'\nimport { TimeColMisc } from './TimeColMisc'\n\nexport interface TimeColProps {\n  elRef?: Ref<HTMLTableCellElement>\n  dateProfile: DateProfile\n  date: DateMarker\n  nowDate: DateMarker\n  todayRange: DateRange\n  extraDataAttrs?: any\n  extraHookProps?: any\n  extraClassNames?: string[]\n  extraDateSpan?: Dictionary\n  fgEventSegs: TimeColsSeg[]\n  bgEventSegs: TimeColsSeg[]\n  businessHourSegs: TimeColsSeg[]\n  nowIndicatorSegs: TimeColsSeg[]\n  dateSelectionSegs: TimeColsSeg[]\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  slatCoords: TimeColsSlatsCoords\n  forPrint: boolean\n}\n\nexport class TimeCol extends BaseComponent<TimeColProps> {\n  sortEventSegs = memoize(sortEventSegs)\n  // TODO: memoize event-placement?\n\n  render() {\n    let { props, context } = this\n    let isSelectMirror = context.options.selectMirror\n\n    let mirrorSegs: Seg[] = // yuck\n      (props.eventDrag && props.eventDrag.segs) ||\n      (props.eventResize && props.eventResize.segs) ||\n      (isSelectMirror && props.dateSelectionSegs) ||\n      []\n\n    let interactionAffectedInstances = // TODO: messy way to compute this\n      (props.eventDrag && props.eventDrag.affectedInstances) ||\n      (props.eventResize && props.eventResize.affectedInstances) ||\n      {}\n\n    let sortedFgSegs = this.sortEventSegs(props.fgEventSegs, context.options.eventOrder) as TimeColsSeg[]\n\n    return (\n      <DayCellRoot\n        elRef={props.elRef}\n        date={props.date}\n        dateProfile={props.dateProfile}\n        todayRange={props.todayRange}\n        extraHookProps={props.extraHookProps}\n      >\n        {(rootElRef, classNames, dataAttrs) => (\n          <td\n            ref={rootElRef}\n            role=\"gridcell\"\n            className={['fc-timegrid-col'].concat(classNames, props.extraClassNames || []).join(' ')}\n            {...dataAttrs}\n            {...props.extraDataAttrs}\n          >\n            <div className=\"fc-timegrid-col-frame\">\n              <div className=\"fc-timegrid-col-bg\">\n                {this.renderFillSegs(props.businessHourSegs, 'non-business')}\n                {this.renderFillSegs(props.bgEventSegs, 'bg-event')}\n                {this.renderFillSegs(props.dateSelectionSegs, 'highlight')}\n              </div>\n              <div className=\"fc-timegrid-col-events\">\n                {this.renderFgSegs(\n                  sortedFgSegs,\n                  interactionAffectedInstances,\n                  false,\n                  false,\n                  false,\n                )}\n              </div>\n              <div className=\"fc-timegrid-col-events\">\n                {this.renderFgSegs(\n                  mirrorSegs as TimeColsSeg[],\n                  {},\n                  Boolean(props.eventDrag),\n                  Boolean(props.eventResize),\n                  Boolean(isSelectMirror),\n                )}\n              </div>\n              <div className=\"fc-timegrid-now-indicator-container\">\n                {this.renderNowIndicator(props.nowIndicatorSegs)}\n              </div>\n              <TimeColMisc\n                date={props.date}\n                dateProfile={props.dateProfile}\n                todayRange={props.todayRange}\n                extraHookProps={props.extraHookProps}\n              />\n            </div>\n          </td>\n        )}\n      </DayCellRoot>\n    )\n  }\n\n  renderFgSegs(\n    sortedFgSegs: TimeColsSeg[],\n    segIsInvisible: { [instanceId: string]: any },\n    isDragging: boolean,\n    isResizing: boolean,\n    isDateSelecting: boolean,\n  ) {\n    let { props } = this\n    if (props.forPrint) {\n      return renderPlainFgSegs(sortedFgSegs, props)\n    }\n    return this.renderPositionedFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting)\n  }\n\n  renderPositionedFgSegs(\n    segs: TimeColsSeg[], // if not mirror, needs to be sorted\n    segIsInvisible: { [instanceId: string]: any },\n    isDragging: boolean,\n    isResizing: boolean,\n    isDateSelecting: boolean,\n  ) {\n    let { eventMaxStack, eventShortHeight, eventOrderStrict, eventMinHeight } = this.context.options\n    let { date, slatCoords, eventSelection, todayRange, nowDate } = this.props\n    let isMirror = isDragging || isResizing || isDateSelecting\n    let segVCoords = computeSegVCoords(segs, date, slatCoords, eventMinHeight)\n    let { segPlacements, hiddenGroups } = computeFgSegPlacements(segs, segVCoords, eventOrderStrict, eventMaxStack)\n\n    return (\n      <Fragment>\n        {this.renderHiddenGroups(hiddenGroups, segs)}\n        {segPlacements.map((segPlacement) => {\n          let { seg, rect } = segPlacement\n          let instanceId = seg.eventRange.instance.instanceId\n          let isVisible = isMirror || Boolean(!segIsInvisible[instanceId] && rect)\n          let vStyle = computeSegVStyle(rect && rect.span)\n          let hStyle = (!isMirror && rect) ? this.computeSegHStyle(rect) : { left: 0, right: 0 }\n          let isInset = Boolean(rect) && rect.stackForward > 0\n          let isShort = Boolean(rect) && (rect.span.end - rect.span.start) < eventShortHeight // look at other places for this problem\n\n          return (\n            <div\n              className={\n                'fc-timegrid-event-harness' +\n                (isInset ? ' fc-timegrid-event-harness-inset' : '')\n              }\n              key={instanceId}\n              style={{\n                visibility: isVisible ? ('' as any) : 'hidden',\n                ...vStyle,\n                ...hStyle,\n              }}\n            >\n              <TimeColEvent\n                seg={seg}\n                isDragging={isDragging}\n                isResizing={isResizing}\n                isDateSelecting={isDateSelecting}\n                isSelected={instanceId === eventSelection}\n                isShort={isShort}\n                {...getSegMeta(seg, todayRange, nowDate)}\n              />\n            </div>\n          )\n        })}\n      </Fragment>\n    )\n  }\n\n  // will already have eventMinHeight applied because segInputs already had it\n  renderHiddenGroups(hiddenGroups: SegEntryGroup[], segs: TimeColsSeg[]) {\n    let { extraDateSpan, dateProfile, todayRange, nowDate, eventSelection, eventDrag, eventResize } = this.props\n    return (\n      <Fragment>\n        {hiddenGroups.map((hiddenGroup) => {\n          let positionCss = computeSegVStyle(hiddenGroup.span)\n          let hiddenSegs = compileSegsFromEntries(hiddenGroup.entries, segs)\n          return (\n            <TimeColMoreLink\n              key={buildIsoString(computeEarliestSegStart(hiddenSegs))}\n              hiddenSegs={hiddenSegs}\n              top={positionCss.top}\n              bottom={positionCss.bottom}\n              extraDateSpan={extraDateSpan}\n              dateProfile={dateProfile}\n              todayRange={todayRange}\n              nowDate={nowDate}\n              eventSelection={eventSelection}\n              eventDrag={eventDrag}\n              eventResize={eventResize}\n            />\n          )\n        })}\n      </Fragment>\n    )\n  }\n\n  renderFillSegs(segs: TimeColsSeg[], fillType: string) {\n    let { props, context } = this\n    let segVCoords = computeSegVCoords(segs, props.date, props.slatCoords, context.options.eventMinHeight) // don't assume all populated\n\n    let children = segVCoords.map((vcoords, i) => {\n      let seg = segs[i]\n      return (\n        <div\n          key={buildEventRangeKey(seg.eventRange)}\n          className=\"fc-timegrid-bg-harness\"\n          style={computeSegVStyle(vcoords)}\n        >\n          {fillType === 'bg-event' ?\n            <BgEvent seg={seg} {...getSegMeta(seg, props.todayRange, props.nowDate)} /> :\n            renderFill(fillType)}\n        </div>\n      )\n    })\n\n    return <Fragment>{children}</Fragment>\n  }\n\n  renderNowIndicator(segs: TimeColsSeg[]) {\n    let { slatCoords, date } = this.props\n\n    if (!slatCoords) { return null }\n\n    return segs.map((seg, i) => (\n      <NowIndicatorRoot\n        isAxis={false}\n        date={date}\n        // key doesn't matter. will only ever be one\n        key={i} // eslint-disable-line react/no-array-index-key\n      >\n        {(rootElRef, classNames, innerElRef, innerContent) => (\n          <div\n            ref={rootElRef}\n            className={['fc-timegrid-now-indicator-line'].concat(classNames).join(' ')}\n            style={{ top: slatCoords.computeDateTop(seg.start, date) }}\n          >\n            {innerContent}\n          </div>\n        )}\n      </NowIndicatorRoot>\n    ))\n  }\n\n  computeSegHStyle(segHCoords: SegWebRect) {\n    let { isRtl, options } = this.context\n    let shouldOverlap = options.slotEventOverlap\n    let nearCoord = segHCoords.levelCoord // the left side if LTR. the right side if RTL. floating-point\n    let farCoord = segHCoords.levelCoord + segHCoords.thickness // the right side if LTR. the left side if RTL. floating-point\n    let left // amount of space from left edge, a fraction of the total width\n    let right // amount of space from right edge, a fraction of the total width\n\n    if (shouldOverlap) {\n      // double the width, but don't go beyond the maximum forward coordinate (1.0)\n      farCoord = Math.min(1, nearCoord + (farCoord - nearCoord) * 2)\n    }\n\n    if (isRtl) {\n      left = 1 - farCoord\n      right = nearCoord\n    } else {\n      left = nearCoord\n      right = 1 - farCoord\n    }\n\n    let props = {\n      zIndex: segHCoords.stackDepth + 1, // convert from 0-base to 1-based\n      left: left * 100 + '%',\n      right: right * 100 + '%',\n    }\n\n    if (shouldOverlap && !segHCoords.stackForward) {\n      // add padding to the edge so that forward stacked events don't cover the resizer's icon\n      props[isRtl ? 'marginLeft' : 'marginRight'] = 10 * 2 // 10 is a guesstimate of the icon's width\n    }\n\n    return props\n  }\n}\n\nexport function renderPlainFgSegs(\n  sortedFgSegs: TimeColsSeg[],\n  { todayRange, nowDate, eventSelection, eventDrag, eventResize }: {\n    todayRange: DateRange\n    nowDate: DateMarker\n    eventSelection: string\n    eventDrag: EventSegUiInteractionState | null\n    eventResize: EventSegUiInteractionState | null\n  },\n) {\n  let hiddenInstances =\n    (eventDrag ? eventDrag.affectedInstances : null) ||\n    (eventResize ? eventResize.affectedInstances : null) ||\n    {}\n  return (\n    <Fragment>\n      {sortedFgSegs.map((seg) => {\n        let instanceId = seg.eventRange.instance.instanceId\n        return (\n          <div\n            key={instanceId}\n            style={{ visibility: hiddenInstances[instanceId] ? 'hidden' : ('' as any) }}\n          >\n            <TimeColEvent\n              seg={seg}\n              isDragging={false}\n              isResizing={false}\n              isDateSelecting={false}\n              isSelected={instanceId === eventSelection}\n              isShort={false}\n              {...getSegMeta(seg, todayRange, nowDate)}\n            />\n          </div>\n        )\n      })}\n    </Fragment>\n  )\n}\n\nfunction computeSegVStyle(segVCoords: SegSpan | null): { top: CssDimValue, bottom: CssDimValue } {\n  if (!segVCoords) {\n    return { top: '', bottom: '' }\n  }\n  return {\n    top: segVCoords.start,\n    bottom: -segVCoords.end,\n  }\n}\n\nfunction compileSegsFromEntries(\n  segEntries: SegEntry[],\n  allSegs: TimeColsSeg[],\n): TimeColsSeg[] {\n  return segEntries.map((segEntry) => allSegs[segEntry.index])\n}\n", "import {\n  createElement, VNode,\n  BaseComponent,\n  EventSegUiInteractionState,\n  CssDimV<PERSON>ue,\n  DateMarker,\n  RefMap,\n  createRef,\n  PositionCache,\n  memoize,\n  DateRange,\n  NowIndicatorRoot,\n  DateProfile,\n  DayTableCell,\n} from '@fullcalendar/common'\nimport { TimeColsSeg, splitSegsByCol, splitInteractionByCol } from './TimeColsSeg'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { TimeCol } from './TimeCol'\n\nexport interface TimeColsContentProps {\n  axis: boolean\n  cells: DayTableCell[]\n  dateProfile: DateProfile\n  nowDate: DateMarker\n  todayRange: DateRange\n  businessHourSegs: TimeColsSeg[]\n  bgEventSegs: TimeColsSeg[]\n  fgEventSegs: TimeColsSeg[]\n  dateSelectionSegs: TimeColsSeg[]\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  nowIndicatorSegs: TimeColsSeg[]\n  clientWidth: number | null\n  tableMinWidth: CssDimValue\n  tableColGroupNode: VNode\n  slatCoords: TimeColsSlatsCoords\n  onColCoords?: (colCoords: PositionCache) => void\n  forPrint: boolean\n}\n\nexport class TimeColsContent extends BaseComponent<TimeColsContentProps> { // TODO: rename\n  private splitFgEventSegs = memoize(splitSegsByCol)\n  private splitBgEventSegs = memoize(splitSegsByCol)\n  private splitBusinessHourSegs = memoize(splitSegsByCol)\n  private splitNowIndicatorSegs = memoize(splitSegsByCol)\n  private splitDateSelectionSegs = memoize(splitSegsByCol)\n  private splitEventDrag = memoize(splitInteractionByCol)\n  private splitEventResize = memoize(splitInteractionByCol)\n  private rootElRef = createRef<HTMLDivElement>()\n  private cellElRefs = new RefMap<HTMLTableCellElement>()\n\n  render() {\n    let { props, context } = this\n    let nowIndicatorTop =\n      context.options.nowIndicator &&\n      props.slatCoords &&\n      props.slatCoords.safeComputeTop(props.nowDate) // might return void\n\n    let colCnt = props.cells.length\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, colCnt)\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, colCnt)\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, colCnt)\n    let nowIndicatorSegsByRow = this.splitNowIndicatorSegs(props.nowIndicatorSegs, colCnt)\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, colCnt)\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, colCnt)\n    let eventResizeByRow = this.splitEventResize(props.eventResize, colCnt)\n\n    return (\n      <div className=\"fc-timegrid-cols\" ref={this.rootElRef}>\n        <table\n          role=\"presentation\"\n          style={{\n            minWidth: props.tableMinWidth,\n            width: props.clientWidth,\n          }}\n        >\n          {props.tableColGroupNode}\n          <tbody role=\"presentation\">\n            <tr role=\"row\">\n              {props.axis && (\n                <td aria-hidden className=\"fc-timegrid-col fc-timegrid-axis\">\n                  <div className=\"fc-timegrid-col-frame\">\n                    <div className=\"fc-timegrid-now-indicator-container\">\n                      {typeof nowIndicatorTop === 'number' && (\n                        <NowIndicatorRoot isAxis date={props.nowDate}>\n                          {(rootElRef, classNames, innerElRef, innerContent) => (\n                            <div\n                              ref={rootElRef}\n                              className={['fc-timegrid-now-indicator-arrow'].concat(classNames).join(' ')}\n                              style={{ top: nowIndicatorTop }}\n                            >\n                              {innerContent}\n                            </div>\n                          )}\n                        </NowIndicatorRoot>\n                      )}\n                    </div>\n                  </div>\n                </td>\n              )}\n              {props.cells.map((cell, i) => (\n                <TimeCol\n                  key={cell.key}\n                  elRef={this.cellElRefs.createRef(cell.key)}\n                  dateProfile={props.dateProfile}\n                  date={cell.date}\n                  nowDate={props.nowDate}\n                  todayRange={props.todayRange}\n                  extraHookProps={cell.extraHookProps}\n                  extraDataAttrs={cell.extraDataAttrs}\n                  extraClassNames={cell.extraClassNames}\n                  extraDateSpan={cell.extraDateSpan}\n                  fgEventSegs={fgEventSegsByRow[i]}\n                  bgEventSegs={bgEventSegsByRow[i]}\n                  businessHourSegs={businessHourSegsByRow[i]}\n                  nowIndicatorSegs={nowIndicatorSegsByRow[i]}\n                  dateSelectionSegs={dateSelectionSegsByRow[i]}\n                  eventDrag={eventDragByRow[i]}\n                  eventResize={eventResizeByRow[i]}\n                  slatCoords={props.slatCoords}\n                  eventSelection={props.eventSelection}\n                  forPrint={props.forPrint}\n                />\n              ))}\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    )\n  }\n\n  componentDidMount() {\n    this.updateCoords()\n  }\n\n  componentDidUpdate() {\n    this.updateCoords()\n  }\n\n  updateCoords() {\n    let { props } = this\n\n    if (\n      props.onColCoords &&\n      props.clientWidth !== null // means sizing has stabilized\n    ) {\n      props.onColCoords(\n        new PositionCache(\n          this.rootElRef.current,\n          collectCellEls(this.cellElRefs.currentMap, props.cells),\n          true, // horizontal\n          false,\n        ),\n      )\n    }\n  }\n}\n\nfunction collectCellEls(elMap: { [key: string]: HTMLElement }, cells: DayTableCell[]) {\n  return cells.map((cell) => elMap[cell.key])\n}\n", "import {\n  createElement, VNode,\n  addDurations,\n  multiplyDuration,\n  wholeDivideDurations,\n  DateMarker,\n  EventSegUiInteractionState,\n  memoize,\n  CssDimValue,\n  PositionCache,\n  ScrollResponder,\n  ScrollRequest,\n  DateRange,\n  Duration,\n  DateProfile,\n  DayTableCell,\n  Hit,\n  DateComponent,\n} from '@fullcalendar/common'\nimport { TimeColsSlats } from './TimeColsSlats'\nimport { TimeSlatMeta } from './time-slat-meta'\nimport { TimeColsContent } from './TimeColsContent'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { TimeColsSeg } from './TimeColsSeg'\n\nexport interface TimeColsProps {\n  cells: DayTableCell[]\n  dateProfile: DateProfile\n  slotDuration: Duration\n  nowDate: DateMarker\n  todayRange: DateRange\n  businessHourSegs: TimeColsSeg[]\n  bgEventSegs: TimeColsSeg[]\n  fgEventSegs: TimeColsSeg[]\n  dateSelectionSegs: TimeColsSeg[]\n  eventSelection: string\n  eventDrag: EventSegUiInteractionState | null\n  eventResize: EventSegUiInteractionState | null\n  tableColGroupNode: VNode\n  tableMinWidth: CssDimValue\n  clientWidth: number | null\n  clientHeight: number | null\n  expandRows: boolean\n  nowIndicatorSegs: TimeColsSeg[]\n  onScrollTopRequest?: (scrollTop: number) => void\n  forPrint: boolean\n  axis: boolean\n  slatMetas: TimeSlatMeta[]\n  onSlatCoords?: (slatCoords: TimeColsSlatsCoords) => void\n  isHitComboAllowed?: (hit0: Hit, hit1: Hit) => boolean\n}\n\ninterface TimeColsState {\n  slatCoords: TimeColsSlatsCoords | null\n}\n\n/* A component that renders one or more columns of vertical time slots\n----------------------------------------------------------------------------------------------------------------------*/\n\nexport class TimeCols extends DateComponent<TimeColsProps, TimeColsState> {\n  private processSlotOptions = memoize(processSlotOptions)\n  private scrollResponder: ScrollResponder\n  private colCoords: PositionCache\n\n  state = {\n    slatCoords: null,\n  }\n\n  render() {\n    let { props, state } = this\n\n    return (\n      <div\n        className=\"fc-timegrid-body\"\n        ref={this.handleRootEl}\n        style={{\n          // these props are important to give this wrapper correct dimensions for interactions\n          // TODO: if we set it here, can we avoid giving to inner tables?\n          width: props.clientWidth,\n          minWidth: props.tableMinWidth,\n        }}\n      >\n        <TimeColsSlats\n          axis={props.axis}\n          dateProfile={props.dateProfile}\n          slatMetas={props.slatMetas}\n          clientWidth={props.clientWidth}\n          minHeight={props.expandRows ? props.clientHeight : ''}\n          tableMinWidth={props.tableMinWidth}\n          tableColGroupNode={props.axis ? props.tableColGroupNode : null /* axis depends on the colgroup's shrinking */}\n          onCoords={this.handleSlatCoords}\n        />\n        <TimeColsContent\n          cells={props.cells}\n          axis={props.axis}\n          dateProfile={props.dateProfile}\n          businessHourSegs={props.businessHourSegs}\n          bgEventSegs={props.bgEventSegs}\n          fgEventSegs={props.fgEventSegs}\n          dateSelectionSegs={props.dateSelectionSegs}\n          eventSelection={props.eventSelection}\n          eventDrag={props.eventDrag}\n          eventResize={props.eventResize}\n          todayRange={props.todayRange}\n          nowDate={props.nowDate}\n          nowIndicatorSegs={props.nowIndicatorSegs}\n          clientWidth={props.clientWidth}\n          tableMinWidth={props.tableMinWidth}\n          tableColGroupNode={props.tableColGroupNode}\n          slatCoords={state.slatCoords}\n          onColCoords={this.handleColCoords}\n          forPrint={props.forPrint}\n        />\n      </div>\n    )\n  }\n\n  handleRootEl = (el: HTMLElement | null) => {\n    if (el) {\n      this.context.registerInteractiveComponent(this, {\n        el,\n        isHitComboAllowed: this.props.isHitComboAllowed,\n      })\n    } else {\n      this.context.unregisterInteractiveComponent(this)\n    }\n  }\n\n  componentDidMount() {\n    this.scrollResponder = this.context.createScrollResponder(this.handleScrollRequest)\n  }\n\n  componentDidUpdate(prevProps: TimeColsProps) {\n    this.scrollResponder.update(prevProps.dateProfile !== this.props.dateProfile)\n  }\n\n  componentWillUnmount() {\n    this.scrollResponder.detach()\n  }\n\n  handleScrollRequest = (request: ScrollRequest) => {\n    let { onScrollTopRequest } = this.props\n    let { slatCoords } = this.state\n\n    if (onScrollTopRequest && slatCoords) {\n      if (request.time) {\n        let top = slatCoords.computeTimeTop(request.time)\n        top = Math.ceil(top) // zoom can give weird floating-point values. rather scroll a little bit further\n        if (top) {\n          top += 1 // to overcome top border that slots beyond the first have. looks better\n        }\n\n        onScrollTopRequest(top)\n      }\n\n      return true\n    }\n\n    return false\n  }\n\n  handleColCoords = (colCoords: PositionCache | null) => {\n    this.colCoords = colCoords\n  }\n\n  handleSlatCoords = (slatCoords: TimeColsSlatsCoords | null) => {\n    this.setState({ slatCoords })\n\n    if (this.props.onSlatCoords) {\n      this.props.onSlatCoords(slatCoords)\n    }\n  }\n\n  queryHit(positionLeft: number, positionTop: number): Hit {\n    let { dateEnv, options } = this.context\n    let { colCoords } = this\n    let { dateProfile } = this.props\n    let { slatCoords } = this.state\n    let { snapDuration, snapsPerSlot } = this.processSlotOptions(this.props.slotDuration, options.snapDuration)\n\n    let colIndex = colCoords.leftToIndex(positionLeft)\n    let slatIndex = slatCoords.positions.topToIndex(positionTop)\n\n    if (colIndex != null && slatIndex != null) {\n      let cell = this.props.cells[colIndex]\n      let slatTop = slatCoords.positions.tops[slatIndex]\n      let slatHeight = slatCoords.positions.getHeight(slatIndex)\n      let partial = (positionTop - slatTop) / slatHeight // floating point number between 0 and 1\n      let localSnapIndex = Math.floor(partial * snapsPerSlot) // the snap # relative to start of slat\n      let snapIndex = slatIndex * snapsPerSlot + localSnapIndex\n\n      let dayDate = this.props.cells[colIndex].date\n      let time = addDurations(\n        dateProfile.slotMinTime,\n        multiplyDuration(snapDuration, snapIndex),\n      )\n\n      let start = dateEnv.add(dayDate, time)\n      let end = dateEnv.add(start, snapDuration)\n\n      return {\n        dateProfile,\n        dateSpan: {\n          range: { start, end },\n          allDay: false,\n          ...cell.extraDateSpan,\n        },\n        dayEl: colCoords.els[colIndex],\n        rect: {\n          left: colCoords.lefts[colIndex],\n          right: colCoords.rights[colIndex],\n          top: slatTop,\n          bottom: slatTop + slatHeight,\n        },\n        layer: 0,\n      }\n    }\n\n    return null\n  }\n}\n\nfunction processSlotOptions(slotDuration: Duration, snapDurationOverride: Duration | null) {\n  let snapDuration = snapDurationOverride || slotDuration\n  let snapsPerSlot = wholeDivideDurations(slotDuration, snapDuration)\n\n  if (snapsPerSlot === null) {\n    snapDuration = slotDuration\n    snapsPerSlot = 1\n    // TODO: say warning?\n  }\n\n  return { snapDuration, snapsPerSlot }\n}\n", "import { intersectRang<PERSON>, DateRang<PERSON>, Slicer } from '@fullcalendar/common'\nimport { TimeColsSeg } from './TimeColsSeg'\n\nexport class DayTimeColsSlicer extends Slicer<TimeColsSeg, [DateRange[]]> {\n  sliceRange(range: DateRange, dayRanges: DateRange[]): TimeColsSeg[] {\n    let segs: TimeColsSeg[] = []\n\n    for (let col = 0; col < dayRanges.length; col += 1) {\n      let segRange = intersectRanges(range, dayRanges[col])\n\n      if (segRange) {\n        segs.push({\n          start: segRange.start,\n          end: segRange.end,\n          isStart: segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: segRange.end.valueOf() === range.end.valueOf(),\n          col,\n        })\n      }\n    }\n\n    return segs\n  }\n}\n", "import {\n  createElement,\n  createRef,\n  VNode,\n  DateComponent,\n  Date<PERSON>ro<PERSON>le,\n  EventStore,\n  EventUiHash,\n  EventInteractionState,\n  DateSpan,\n  memoize,\n  DateRange,\n  DayTableModel,\n  DateEnv,\n  DateMarker,\n  NowTimer,\n  CssDimValue,\n  Duration,\n} from '@fullcalendar/common'\nimport { TimeCols } from './TimeCols'\nimport { TimeSlatMeta } from './time-slat-meta'\nimport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\nimport { DayTimeColsSlicer } from './DayTimeColsSlicer'\n\nexport interface DayTimeColsProps {\n  dateProfile: DateProfile\n  dayTableModel: DayTableModel\n  axis: boolean\n  slotDuration: Duration\n  slatMetas: TimeSlatMeta[]\n  businessHours: EventStore\n  eventStore: EventStore\n  eventUiBases: EventUiHash\n  dateSelection: DateSpan | null\n  eventSelection: string\n  eventDrag: EventInteractionState | null\n  eventResize: EventInteractionState | null\n  tableColGroupNode: VNode\n  tableMinWidth: CssDimValue\n  clientWidth: number | null\n  clientHeight: number | null\n  expandRows: boolean\n  onScrollTopRequest?: (scrollTop: number) => void\n  forPrint: boolean\n  onSlatCoords?: (slatCoords: TimeColsSlatsCoords) => void\n}\n\nexport class DayTimeCols extends DateComponent<DayTimeColsProps> {\n  private buildDayRanges = memoize(buildDayRanges)\n  private slicer = new DayTimeColsSlicer()\n  private timeColsRef = createRef<TimeCols>()\n\n  render() {\n    let { props, context } = this\n    let { dateProfile, dayTableModel } = props\n\n    let isNowIndicator = context.options.nowIndicator\n    let dayRanges = this.buildDayRanges(dayTableModel, dateProfile, context.dateEnv)\n\n    // give it the first row of cells\n    // TODO: would move this further down hierarchy, but sliceNowDate needs it\n    return (\n      <NowTimer unit={isNowIndicator ? 'minute' : 'day'}>\n        {(nowDate: DateMarker, todayRange: DateRange) => (\n          <TimeCols\n            ref={this.timeColsRef}\n            {...this.slicer.sliceProps(props, dateProfile, null, context, dayRanges)}\n            forPrint={props.forPrint}\n            axis={props.axis}\n            dateProfile={dateProfile}\n            slatMetas={props.slatMetas}\n            slotDuration={props.slotDuration}\n            cells={dayTableModel.cells[0]}\n            tableColGroupNode={props.tableColGroupNode}\n            tableMinWidth={props.tableMinWidth}\n            clientWidth={props.clientWidth}\n            clientHeight={props.clientHeight}\n            expandRows={props.expandRows}\n            nowDate={nowDate}\n            nowIndicatorSegs={isNowIndicator && this.slicer.sliceNowDate(nowDate, context, dayRanges)}\n            todayRange={todayRange}\n            onScrollTopRequest={props.onScrollTopRequest}\n            onSlatCoords={props.onSlatCoords}\n          />\n        )}\n      </NowTimer>\n    )\n  }\n}\n\nexport function buildDayRanges(dayTableModel: DayTableModel, dateProfile: DateProfile, dateEnv: DateEnv): DateRange[] {\n  let ranges: DateRange[] = []\n\n  for (let date of dayTableModel.headerDates) {\n    ranges.push({\n      start: dateEnv.add(date, dateProfile.slotMinTime),\n      end: dateEnv.add(date, dateProfile.slotMaxTime),\n    })\n  }\n\n  return ranges\n}\n", "import {\n  createDuration,\n  asRoughMs,\n  formatIsoTimeString,\n  addDurations,\n  wholeDivideDurations,\n  Duration,\n  DateMarker,\n  DateEnv,\n} from '@fullcalendar/common'\n\nexport interface TimeSlatMeta {\n  date: DateMarker\n  time: Duration\n  key: string\n  isoTimeStr: string\n  isLabeled: boolean\n}\n\n// potential nice values for the slot-duration and interval-duration\n// from largest to smallest\nconst STOCK_SUB_DURATIONS = [\n  { hours: 1 },\n  { minutes: 30 },\n  { minutes: 15 },\n  { seconds: 30 },\n  { seconds: 15 },\n]\n\nexport function buildSlatMetas(\n  slotMinTime: Duration,\n  slotMaxTime: Duration,\n  explicitLabelInterval: Duration | null,\n  slotDuration: Duration,\n  dateEnv: DateEnv,\n) {\n  let dayStart = new Date(0)\n  let slatTime = slotMinTime\n  let slatIterator = createDuration(0)\n  let labelInterval = explicitLabelInterval || computeLabelInterval(slotDuration)\n  let metas: TimeSlatMeta[] = []\n\n  while (asRoughMs(slatTime) < asRoughMs(slotMaxTime)) {\n    let date = dateEnv.add(dayStart, slatTime)\n    let isLabeled = wholeDivideDurations(slatIterator, labelInterval) !== null\n\n    metas.push({\n      date,\n      time: slatTime,\n      key: date.toISOString(), // we can't use the isoTimeStr for uniqueness when minTime/maxTime beyone 0h/24h\n      isoTimeStr: formatIsoTimeString(date),\n      isLabeled,\n    })\n\n    slatTime = addDurations(slatTime, slotDuration)\n    slatIterator = addDurations(slatIterator, slotDuration)\n  }\n\n  return metas\n}\n\n// Computes an automatic value for slotLabelInterval\nfunction computeLabelInterval(slotDuration) {\n  let i\n  let labelInterval\n  let slotsPerLabel\n\n  // find the smallest stock label interval that results in more than one slots-per-label\n  for (i = STOCK_SUB_DURATIONS.length - 1; i >= 0; i -= 1) {\n    labelInterval = createDuration(STOCK_SUB_DURATIONS[i])\n    slotsPerLabel = wholeDivideDurations(labelInterval, slotDuration)\n    if (slotsPerLabel !== null && slotsPerLabel > 1) {\n      return labelInterval\n    }\n  }\n\n  return slotDuration // fall back\n}\n", "import {\n  createElement,\n  DateProfileGenerator, DateProfile,\n  DayHeader,\n  DaySeriesModel,\n  DayTableModel,\n  memoize,\n  ChunkContentCallbackArgs,\n} from '@fullcalendar/common'\nimport { DayTable } from '@fullcalendar/daygrid'\nimport { TimeColsView } from './TimeColsView'\nimport { DayTimeCols } from './DayTimeCols'\nimport { buildSlatMetas } from './time-slat-meta'\n\nexport class DayTimeColsView extends TimeColsView {\n  private buildTimeColsModel = memoize(buildTimeColsModel)\n  private buildSlatMetas = memoize(buildSlatMetas)\n\n  render() {\n    let { options, dateEnv, dateProfileGenerator } = this.context\n    let { props } = this\n    let { dateProfile } = props\n    let dayTableModel = this.buildTimeColsModel(dateProfile, dateProfileGenerator)\n    let splitProps = this.allDaySplitter.splitProps(props)\n    let slatMetas = this.buildSlatMetas(\n      dateProfile.slotMinTime,\n      dateProfile.slotMaxTime,\n      options.slotLabelInterval,\n      options.slotDuration,\n      dateEnv,\n    )\n    let { dayMinWidth } = options\n    let hasAttachedAxis = !dayMinWidth\n    let hasDetachedAxis = dayMinWidth\n\n    let headerContent = options.dayHeaders && (\n      <DayHeader\n        dates={dayTableModel.headerDates}\n        dateProfile={dateProfile}\n        datesRepDistinctDays\n        renderIntro={hasAttachedAxis ? this.renderHeadAxis : null}\n      />\n    )\n\n    let allDayContent = (options.allDaySlot !== false) && ((contentArg: ChunkContentCallbackArgs) => (\n      <DayTable\n        {...splitProps.allDay}\n        dateProfile={dateProfile}\n        dayTableModel={dayTableModel}\n        nextDayThreshold={options.nextDayThreshold}\n        tableMinWidth={contentArg.tableMinWidth}\n        colGroupNode={contentArg.tableColGroupNode}\n        renderRowIntro={hasAttachedAxis ? this.renderTableRowAxis : null}\n        showWeekNumbers={false}\n        expandRows={false}\n        headerAlignElRef={this.headerElRef}\n        clientWidth={contentArg.clientWidth}\n        clientHeight={contentArg.clientHeight}\n        forPrint={props.forPrint}\n        {...this.getAllDayMaxEventProps()}\n      />\n    ))\n\n    let timeGridContent = (contentArg: ChunkContentCallbackArgs) => (\n      <DayTimeCols\n        {...splitProps.timed}\n        dayTableModel={dayTableModel}\n        dateProfile={dateProfile}\n        axis={hasAttachedAxis}\n        slotDuration={options.slotDuration}\n        slatMetas={slatMetas}\n        forPrint={props.forPrint}\n        tableColGroupNode={contentArg.tableColGroupNode}\n        tableMinWidth={contentArg.tableMinWidth}\n        clientWidth={contentArg.clientWidth}\n        clientHeight={contentArg.clientHeight}\n        onSlatCoords={this.handleSlatCoords}\n        expandRows={contentArg.expandRows}\n        onScrollTopRequest={this.handleScrollTopRequest}\n      />\n    )\n\n    return hasDetachedAxis\n      ? this.renderHScrollLayout(\n        headerContent,\n        allDayContent,\n        timeGridContent,\n        dayTableModel.colCnt,\n        dayMinWidth,\n        slatMetas,\n        this.state.slatCoords,\n      )\n      : this.renderSimpleLayout(\n        headerContent,\n        allDayContent,\n        timeGridContent,\n      )\n  }\n}\n\nexport function buildTimeColsModel(dateProfile: DateProfile, dateProfileGenerator: DateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator)\n\n  return new DayTableModel(daySeries, false)\n}\n", "export const OPTION_REFINERS = {\n  allDaySlot: Boolean,\n}\n", "import { createPlugin } from '@fullcalendar/common'\nimport { TimeColsView } from './TimeColsView'\nimport { DayTimeColsView, buildTimeColsModel } from './DayTimeColsView'\nimport { TimeColsSeg } from './TimeColsSeg'\nimport { DayTimeCols, buildDayRanges } from './DayTimeCols'\nimport { DayTimeColsSlicer } from './DayTimeColsSlicer'\nimport { OPTION_REFINERS } from './options'\nimport './options-declare'\nimport './main.css'\n\nexport { DayTimeCols, DayTimeColsView, TimeColsView, buildTimeColsModel, buildDayRanges, DayTimeColsSlicer, TimeColsSeg }\nexport { TimeCols } from './TimeCols'\nexport { TimeSlatMeta, buildSlatMetas } from './time-slat-meta'\nexport { TimeColsSlatsCoords } from './TimeColsSlatsCoords'\n\nexport default createPlugin({\n  initialView: 'timeGridWeek',\n  optionRefiners: OPTION_REFINERS,\n  views: {\n\n    timeGrid: {\n      component: DayTimeColsView,\n      usesMinMaxTime: true, // indicates that slotMinTime/slotMaxTime affects rendering\n      allDaySlot: true,\n      slotDuration: '00:30:00',\n      slotEventOverlap: true, // a bad name. confused with overlap/constraint system\n    },\n\n    timeGridDay: {\n      type: 'timeGrid',\n      duration: { days: 1 },\n    },\n\n    timeGridWeek: {\n      type: 'timeGrid',\n      duration: { weeks: 1 },\n    },\n\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;AAOA;IAAoC,kCAAQ;IAA5C;;KA2BC;IA1BC,mCAAU,GAAV;QACE,OAAO;YACL,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,EAAE;SACV,CAAA;KACF;IAED,2CAAkB,GAAlB,UAAmB,QAAkB;QACnC,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,OAAO,CAAC,QAAQ,CAAC,CAAA;SAClB;QAED,OAAO,CAAC,OAAO,CAAC,CAAA;KACjB;IAED,2CAAkB,GAAlB,UAAmB,QAAkB;QACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,OAAO,CAAC,OAAO,CAAC,CAAA;SACjB;QAED,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;SAC3B;QAED,OAAO,CAAC,QAAQ,CAAC,CAAA;KAClB;IACH,qBAAC;AAAD,CA3BA,CAAoC,QAAQ;;ACG5C,IAAM,yBAAyB,GAAG,eAAe,CAAC;IAChD,IAAI,EAAE,SAAS;IACf,MAAM,EAAE,SAAS;IACjB,cAAc,EAAE,IAAI;IACpB,QAAQ,EAAE,OAAO;CAClB,CAAC,CAAA;SAEc,gBAAgB,CAAC,KAAmB;IAClD,IAAI,UAAU,GAAG;QACf,kBAAkB;QAClB,wBAAwB;QACxB,KAAK,CAAC,SAAS,GAAG,sBAAsB,GAAG,wBAAwB;KACpE,CAAA;IAED,QACE,cAAC,eAAe,CAAC,QAAQ,QACtB,UAAC,OAAoB;QACpB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACpB,QACE,sBAAI,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,eAAa,KAAK,CAAC,UAAU,GAAI,EACrE;SACF;QAEK,IAAA,OAAO,GAAuB,OAAO,QAA9B,EAAE,OAAO,GAAc,OAAO,QAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAY;QAC3C,IAAI,WAAW;SACb,OAAO,CAAC,eAAe,IAAI,IAAI,GAAG,yBAAyB;YACzD,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAClF,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QAE9C,IAAI,SAAS,GAAwB;YACnC,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;YAChC,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC;SAC9C,CAAA;QAED,QACE,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,mBAAmB,EACvC,OAAO,EAAE,OAAO,CAAC,gBAAgB,EACjC,cAAc,EAAE,kBAAkB,EAClC,QAAQ,EAAE,OAAO,CAAC,iBAAiB,EACnC,WAAW,EAAE,OAAO,CAAC,oBAAoB,IAExC,UAAC,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,IAAK,QAC1D,sBAAI,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAAa,KAAK,CAAC,UAAU;YACvG,uBAAK,SAAS,EAAC,yDAAyD;gBACtE,uBAAK,SAAS,EAAC,6DAA6D,EAAC,GAAG,EAAE,UAAU,IACzF,YAAY,CACT,CACF,CACH,IACN,CACU,EACd;KACF,CACwB,EAC5B;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAK;IAC/B,OAAO,KAAK,CAAC,IAAI,CAAA;AACnB;;AC/DA;IAAkC,gCAAgC;IAAlE;;KAQC;IAPC,6BAAM,GAAN;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,UAAC,QAAsB,IAAK,QAC1D,sBAAI,GAAG,EAAE,QAAQ,CAAC,GAAG;YACnB,cAAC,gBAAgB,eAAK,QAAQ,EAAI,CAC/B,IACN,CAAC,CAAA;KACH;IACH,mBAAC;AAAD,CARA,CAAkC,aAAa;;ACmB/C,IAAM,uBAAuB,GAAG,eAAe,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;AAClE,IAAM,2BAA2B,GAAG,CAAC,CAAA;;IAWM,gCAA2C;IAAtF;QAAA,qEAwXC;QAvXW,oBAAc,GAAG,IAAI,cAAc,EAAE,CAAA;QAErC,iBAAW,GAAoC,SAAS,EAAwB,CAAA;QAClF,eAAS,GAA8B,SAAS,EAAkB,CAAA;QAClE,mBAAa,GAA8B,SAAS,EAAkB,CAAA;QAE9E,WAAK,GAAG;YACN,UAAU,EAAE,IAAI;SACjB,CAAA;QAuPD,4BAAsB,GAAG,UAAC,SAAiB;YACzC,IAAI,UAAU,GAAG,KAAI,CAAC,aAAa,CAAC,OAAO,CAAA;YAE3C,IAAI,UAAU,EAAE;gBACd,UAAU,CAAC,SAAS,GAAG,SAAS,CAAA;aACjC;SACF,CAAA;;;QAmBD,oBAAc,GAAG,UAAC,MAAsB,EAAE,WAA6B;YAA7B,4BAAA,EAAA,gBAA6B;YAC/D,IAAA,OAAO,GAAK,KAAI,CAAC,OAAO,QAAjB,CAAiB;YACxB,IAAA,WAAW,GAAK,KAAI,CAAC,KAAK,YAAf,CAAe;YAChC,IAAI,KAAK,GAAG,WAAW,CAAC,WAAW,CAAA;YACnC,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;YAE7C,IAAI,YAAY,GAAG,CAAC,MAAM,KAAK,CAAC;kBAC5B,iBAAiB,CAAC,KAAI,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC;kBACpD,EAAE,CAAA;YAEN,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,KAAK,KAAK,EAAE;gBAC3C,QACE,cAAC,cAAc,IAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,uBAAuB,IACtE,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,sBACE,GAAG,EAAE,SAAS,uBAEd,SAAS,EAAE;wBACT,kBAAkB;wBAClB,sBAAsB;qBACvB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;oBAE9B,uBACE,SAAS,EAAC,iFAAiF,EAC3F,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;wBAE9B,8BACE,GAAG,EAAE,UAAU,EACf,SAAS,EAAC,gFAAgF,IACtF,YAAY,GAEf,YAAY,CACX,CACA,CACH,IACN,CACc,EAClB;aACF;YAED,QACE,2CAAgB,SAAS,EAAC,kBAAkB;gBAC1C,uBAAK,SAAS,EAAC,wBAAwB,EAAC,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,GAAI,CACvE,EACN;SACF,CAAA;;;;;QAOD,wBAAkB,GAAG,UAAC,SAAkB;YAClC,IAAA,KAAuB,KAAI,CAAC,OAAO,EAAjC,OAAO,aAAA,EAAE,OAAO,aAAiB,CAAA;YACvC,IAAI,SAAS,GAAqB;gBAChC,IAAI,EAAE,OAAO,CAAC,UAAU;gBACxB,IAAI,EAAE,OAAO;aACd,CAAA;YAED;;YAEE,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,gBAAgB,EACpC,OAAO,EAAE,OAAO,CAAC,aAAa,EAC9B,cAAc,EAAE,iBAAiB,EACjC,QAAQ,EAAE,OAAO,CAAC,cAAc,EAChC,WAAW,EAAE,OAAO,CAAC,iBAAiB,IAErC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,sBACE,GAAG,EAAE,SAAS,uBAEd,SAAS,EAAE;oBACT,kBAAkB;oBAClB,sBAAsB;iBACvB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;gBAE9B,uBACE,SAAS,EAAE,mDAAmD,IAAI,SAAS,IAAI,IAAI,GAAG,gCAAgC,GAAG,EAAE,CAAC,EAC5H,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;oBAE5B,wBAAM,SAAS,EAAC,gFAAgF,EAAC,GAAG,EAAE,UAAU,IAC7G,YAAY,CACR,CACH,CACH,IACN,CACU,EACd;SACF,CAAA;QAED,sBAAgB,GAAG,UAAC,UAA+B;YACjD,KAAI,CAAC,QAAQ,CAAC,EAAE,UAAU,YAAA,EAAE,CAAC,CAAA;SAC9B,CAAA;;KACF;;;IA1WC,yCAAkB,GAAlB,UACE,gBAA8B,EAC9B,aAAuE,EACvE,WAAqE;QAEjE,IAAA,KAAqB,IAAI,EAAvB,OAAO,aAAA,EAAE,KAAK,WAAS,CAAA;QAC7B,IAAI,QAAQ,GAA8B,EAAE,CAAA;QAC5C,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAE7D,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI,CAAC,WAAW;oBACvB,cAAc,EAAE,eAAe;oBAC/B,UAAU,EAAE,gBAAgB;iBAC7B;aACF,CAAC,CAAA;SACH;QAED,IAAI,aAAa,EAAE;YACjB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;aAClC,CAAC,CAAA;YACF,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,iBAAiB;gBACtB,YAAY;gBACV,sBAAI,IAAI,EAAC,cAAc,EAAC,SAAS,EAAC,uBAAuB;oBACvD,sBACE,SAAS,EAAE,sBAAsB,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAC7E,CACC,CACN;aACF,CAAC,CAAA;SACH;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;YAC/C,KAAK,EAAE;gBACL,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,OAAO,EAAE,WAAW;aACrB;SACF,CAAC,CAAA;QAEF,QACE,cAAC,QAAQ,IAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,IACxD,UAAC,SAAS,EAAE,UAAU,IAAK,QAC1B,uBAAK,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,SAAS;YAC1E,cAAC,gBAAgB,IACf,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,EAC9C,gBAAgB,EAAE,KAAK,CAAC,QAAQ,EAChC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAC3B,QAAQ,EAAE,QAAQ,GAClB,CACE,IACP,CACQ,EACZ;KACF;IAED,0CAAmB,GAAnB,UACE,gBAA8B,EAC9B,aAAuE,EACvE,WAAqE,EACrE,MAAc,EACd,WAAmB,EACnB,SAAyB,EACzB,UAAsC;QAPxC,iBA4KC;QAnKC,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAA;QAExD,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;SAChD;QAEG,IAAA,KAAqB,IAAI,EAAvB,OAAO,aAAA,EAAE,KAAK,WAAS,CAAA;QAC7B,IAAI,iBAAiB,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAChF,IAAI,qBAAqB,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,wBAAwB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACxF,IAAI,QAAQ,GAA8B,EAAE,CAAA;QAE5C,IAAI,gBAAgB,EAAE;YACpB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,iBAAiB;gBAC3B,cAAc,EAAE,IAAI;gBACpB,MAAM,EAAE;oBACN;wBACE,GAAG,EAAE,MAAM;wBACX,UAAU,EAAE,UAAC,GAA6B,IAAK,QAC7C,sBAAI,IAAI,EAAC,cAAc,IACpB,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAC/C,IACN;qBACF;oBACD;wBACE,GAAG,EAAE,MAAM;wBACX,KAAK,EAAE,IAAI,CAAC,WAAW;wBACvB,cAAc,EAAE,eAAe;wBAC/B,UAAU,EAAE,gBAAgB;qBAC7B;iBACF;aACF,CAAC,CAAA;SACH;QAED,IAAI,aAAa,EAAE;YACjB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,SAAS;gBACd,cAAc,EAAE,IAAI;gBACpB,MAAM,EAAE;oBACN;wBACE,GAAG,EAAE,MAAM;wBACX,UAAU,EAAE,UAAC,UAAoC,IAAK,QACpD,sBAAI,IAAI,EAAC,cAAc,IACpB,KAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CACnD,IACN;qBACF;oBACD;wBACE,GAAG,EAAE,MAAM;wBACX,OAAO,EAAE,aAAa;qBACvB;iBACF;aACF,CAAC,CAAA;YACF,QAAQ,CAAC,IAAI,CAAC;gBACZ,GAAG,EAAE,iBAAiB;gBACtB,IAAI,EAAE,MAAM;gBACZ,YAAY;gBACV,sBAAI,IAAI,EAAC,cAAc,EAAC,SAAS,EAAC,uBAAuB;oBACvD,sBACE,OAAO,EAAE,CAAC,EACV,SAAS,EAAE,sBAAsB,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAC7E,CACC,CACN;aACF,CAAC,CAAA;SACH;QAED,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAA;QAEjD,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;YAC/C,MAAM,EAAE;gBACN;oBACE,GAAG,EAAE,MAAM;oBACX,OAAO,EAAE,UAAC,GAAG,IAAK;;oBAEhB,uBAAK,SAAS,EAAC,wBAAwB;wBACrC,8CAAmB,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,YAAY,GAAG,EAAE,EAAE;4BACzE,GAAG,CAAC,iBAAiB;4BACtB;gCACE,cAAC,YAAY,IAAC,SAAS,EAAE,SAAS,GAAI,CAChC,CACF;wBACR,uBAAK,SAAS,EAAC,qCAAqC;4BAClD,cAAC,QAAQ,IAAC,IAAI,EAAE,cAAc,GAAG,QAAQ,GAAG,KAAK,gBAC9C,UAAC,OAAmB;gCACnB,IAAI,eAAe,GACjB,cAAc;oCACd,UAAU;oCACV,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;gCAEpC,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;oCACvC,QACE,cAAC,gBAAgB,IAAC,MAAM,QAAC,IAAI,EAAE,OAAO,IACnC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,uBACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,CAAC,iCAAiC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAC3E,KAAK,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE,IAE9B,YAAY,CACT,IACP,CACgB,EACpB;iCACF;gCAED,OAAO,IAAI,CAAA;6BACZ,CACQ,CACP,CACF,IACP;iBACF;gBACD;oBACE,GAAG,EAAE,MAAM;oBACX,aAAa,EAAE,IAAI,CAAC,aAAa;oBACjC,OAAO,EAAE,WAAW;iBACrB;aACF;SACF,CAAC,CAAA;QAEF,IAAI,qBAAqB,EAAE;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,GAAG,EAAE,QAAQ;gBACb,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE;oBACN;wBACE,GAAG,EAAE,MAAM;wBACX,OAAO,EAAE,gBAAgB;qBAC1B;oBACD;wBACE,GAAG,EAAE,MAAM;wBACX,OAAO,EAAE,gBAAgB;qBAC1B;iBACF;aACF,CAAC,CAAA;SACH;QAED,QACE,cAAC,QAAQ,IAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,IACxD,UAAC,SAAS,EAAE,UAAU,IAAK,QAC1B,uBAAK,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,SAAS;YAC1E,cAAC,UAAU,IACT,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,EAC9C,gBAAgB,EAAE,KAAK,EACvB,SAAS,EAAE;oBACT,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE;oBAChD,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE;iBACpD,EACD,QAAQ,EAAE,QAAQ,GAClB,CACE,IACP,CACQ,EACZ;KACF;;;IAaD,6CAAsB,GAAtB;QACM,IAAA,KAAoC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAtD,YAAY,kBAAA,EAAE,eAAe,qBAAyB,CAAA;QAE5D,IAAI,YAAY,KAAK,IAAI,IAAI,eAAe,KAAK,IAAI,EAAE;YACrD,YAAY,GAAG,SAAS,CAAA;YACxB,eAAe,GAAG,2BAA2B,CAAA;SAC9C;QAED,OAAO,EAAE,YAAY,cAAA,EAAE,eAAe,iBAAA,EAAE,CAAA;KACzC;IAoGH,mBAAC;AAAD,CAxXA,CAA2C,aAAa,GAwXvD;AAED,SAAS,iBAAiB,CAAC,SAAS;IAClC,OAAO,SAAS,CAAC,IAAI,CAAA;AACvB;;;IC1ZE,6BACS,SAAwB,EACvB,WAAwB,EACxB,YAAsB;QAFvB,cAAS,GAAT,SAAS,CAAe;QACvB,gBAAW,GAAX,WAAW,CAAa;QACxB,iBAAY,GAAZ,YAAY,CAAU;KAE/B;IAED,4CAAc,GAAd,UAAe,IAAgB;QACvB,IAAA,WAAW,GAAK,IAAI,YAAT,CAAS;QAE1B,IAAI,mBAAmB,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE;YACvD,IAAI,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,CAAA;YACrC,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,OAAO,EAAE,CAAA;YAEtD,IACE,MAAM,IAAI,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC;gBAC5C,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,EAC3C;gBACA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;aACnD;SACF;QAED,OAAO,IAAI,CAAA;KACZ;;;IAID,4CAAc,GAAd,UAAe,IAAgB,EAAE,cAA2B;QAC1D,IAAI,CAAC,cAAc,EAAE;YACnB,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,CAAA;SAClC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;KACtF;;;;IAKD,4CAAc,GAAd,UAAe,QAAkB;QAC3B,IAAA,KAA6B,IAAI,EAA/B,SAAS,eAAA,EAAE,WAAW,iBAAS,CAAA;QACrC,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAA;;QAG9B,IAAI,YAAY,GAAG,CAAC,QAAQ,CAAC,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC9G,IAAI,SAAS,CAAA;QACb,IAAI,aAAa,CAAA;;;;QAKjB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAA;QACxC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;;;QAI1C,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACpC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC,CAAC,CAAA;;;QAIxC,aAAa,GAAG,YAAY,GAAG,SAAS,CAAA;QAExC,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;YAC9B,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,aAAa,CAAA;KACjD;IACH,0BAAC;AAAD,CAAC;;AC5DD;IAAuC,qCAAqC;IAA5E;;KAoDC;IAnDC,kCAAM,GAAN;QACM,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QACvB,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAY;QACnB,IAAA,UAAU,GAAK,KAAK,WAAV,CAAU;QAE1B,QACE,6BACG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,UAAC,QAAQ,EAAE,CAAC;YAC/B,IAAI,SAAS,GAAuB;gBAClC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC3C,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB,CAAA;YAED,IAAI,UAAU,GAAG;gBACf,kBAAkB;gBAClB,uBAAuB;gBACvB,QAAQ,CAAC,SAAS,GAAG,EAAE,GAAG,wBAAwB;aACnD,CAAA;YAED,QACE,sBACE,GAAG,EAAE,QAAQ,CAAC,GAAG,EACjB,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAEtC,KAAK,CAAC,IAAI,KACT,cAAC,gBAAgB,eAAK,QAAQ,EAAI,CACnC;gBACD,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,kBAAkB,EACtC,OAAO,EAAE,OAAO,CAAC,eAAe,EAChC,QAAQ,EAAE,OAAO,CAAC,gBAAgB,EAClC,WAAW,EAAE,OAAO,CAAC,mBAAmB,IAEvC,UAAC,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,IAAK,QAC1D,sBACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAC7C,QAAQ,CAAC,UAAU,IAE7B,YAAY,CACV,IACN,CACU,CACV,EACN;SACF,CAAC,CACI,EACT;KACF;IACH,wBAAC;AAAD,CApDA,CAAuC,aAAa;;ACYpD;;;AAIA;IAAmC,iCAAiC;IAApE;QAAA,qEAoEC;QAnES,eAAS,GAAG,SAAS,EAAkB,CAAA;QACvC,gBAAU,GAAG,IAAI,MAAM,EAAuB,CAAA;;KAkEvD;IAhEC,8BAAM,GAAN;QACM,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAE7B,QACE,uBAAK,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAC,mBAAmB;YACrD,8CAEE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAC1C,KAAK,EAAE;oBACL,QAAQ,EAAE,KAAK,CAAC,aAAa;oBAC7B,KAAK,EAAE,KAAK,CAAC,WAAW;oBACxB,MAAM,EAAE,KAAK,CAAC,SAAS;iBACxB;gBAEA,KAAK,CAAC,iBAAiB;gBACxB,cAAC,iBAAiB,IAChB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,SAAS,EAAE,KAAK,CAAC,SAAS,GAC1B,CACI,CACJ,EACP;KACF;IAED,yCAAiB,GAAjB;QACE,IAAI,CAAC,YAAY,EAAE,CAAA;KACpB;IAED,0CAAkB,GAAlB;QACE,IAAI,CAAC,YAAY,EAAE,CAAA;KACpB;IAED,4CAAoB,GAApB;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;SAC1B;KACF;IAED,oCAAY,GAAZ;QACM,IAAA,KAAqB,IAAI,EAAvB,OAAO,aAAA,EAAE,KAAK,WAAS,CAAA;QAE7B,IACE,KAAK,CAAC,QAAQ;YACd,KAAK,CAAC,WAAW,KAAK,IAAI;UAC1B;YACA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAA;YAEnC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,QAAQ,CACZ,IAAI,mBAAmB,CACrB,IAAI,aAAa,CACf,IAAI,CAAC,SAAS,CAAC,OAAO,EACtB,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,EAC3D,KAAK,EACL,IAAI,CACL,EACD,IAAI,CAAC,KAAK,CAAC,WAAW,EACtB,OAAO,CAAC,OAAO,CAAC,YAAY,CAC7B,CACF,CAAA;aACF;SACF;KACF;IACH,oBAAC;AAAD,CApEA,CAAmC,aAAa,GAoE/C;AAED,SAAS,cAAc,CAAC,KAAqC,EAAE,SAAyB;IACtF,OAAO,SAAS,CAAC,GAAG,CAAC,UAAC,QAAQ,IAAK,OAAA,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAA,CAAC,CAAA;AACzD;;SC9FgB,cAAc,CAAC,IAA0B,EAAE,MAAc;IACvE,IAAI,SAAS,GAAoB,EAAE,CAAA;IACnC,IAAI,CAAC,CAAA;IAEL,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC9B,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACnB;IAED,IAAI,IAAI,EAAE;QACR,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACnC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SACrC;KACF;IAED,OAAO,SAAS,CAAA;AAClB,CAAC;SAEe,qBAAqB,CAAC,EAAqC,EAAE,MAAc;IACzF,IAAI,KAAK,GAAiC,EAAE,CAAA;IAE5C,IAAI,CAAC,EAAE,EAAE;QACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAClC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;SAChB;KACF;SAAM;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAClC,KAAK,CAAC,CAAC,CAAC,GAAG;gBACT,iBAAiB,EAAE,EAAE,CAAC,iBAAiB;gBACvC,OAAO,EAAE,EAAE,CAAC,OAAO;gBACnB,IAAI,EAAE,EAAE;aACT,CAAA;SACF;QAED,KAAgB,UAAO,EAAP,KAAA,EAAE,CAAC,IAAI,EAAP,cAAO,EAAP,IAAO,EAAE;YAApB,IAAI,GAAG,SAAA;YACV,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC9B;KACF;IAED,OAAO,KAAK,CAAA;AACd;;AC7BA;IAAqC,mCAAmC;IAAxE;QAAA,qEAuCC;QAtCC,eAAS,GAAG,SAAS,EAAe,CAAA;;KAsCrC;IApCC,gCAAM,GAAN;QAAA,iBAmCC;QAlCO,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACpB,QACE,cAAC,YAAY,IACX,UAAU,EAAE,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,EAChC,OAAO,EAAE,KAAK,CAAC,UAAU,EACzB,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,IAAI,CAAC,SAAS,EAC9B,cAAc,EAAE,mBAAmB,EACnC,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,cAAM,OAAA,iBAAiB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,GAAA,IAE/D,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,IAAK,QAC/F,qBACE,GAAG,EAAE,UAAC,EAAsB;gBAC1B,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;gBACrB,MAAM,CAAC,KAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;aAC3B,EACD,SAAS,EAAE,CAAC,uBAAuB,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EACjE,KAAK,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,EAC/C,OAAO,EAAE,WAAW,EACpB,KAAK,EAAE,KAAK,mBACG,UAAU,mBACV,SAAS;YAExB,uBAAK,GAAG,EAAE,UAAU,EAAE,SAAS,EAAC,uCAAuC,IACpE,YAAY,CACT,CACJ,IACL,CACY,EAChB;KACF;IACH,sBAAC;AAAD,CAvCA,CAAqC,aAAa,GAuCjD;AAED,SAAS,mBAAmB,CAAC,KAAyB;IACpD,OAAO,KAAK,CAAC,SAAS,CAAA;AACxB;;ACnCA;SACgB,gBAAgB,CAC9B,SAAqB,EACrB,WAAqB,EACrB,WAAoB;IAEpB,IAAI,SAAS,GAAG,IAAI,YAAY,EAAE,CAAA;IAClC,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA;KACpC;IACD,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA;KACpC;IAED,IAAI,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAChD,IAAI,YAAY,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAA;IAE1D,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAA;IAC7B,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IACxB,IAAI,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;IAE9B,OAAO,EAAE,QAAQ,UAAA,EAAE,YAAY,cAAA,EAAE,CAAA;AACnC,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IAC/B,IAAA,cAAc,GAAK,SAAS,eAAd,CAAc;IAEpC,IAAM,SAAS,GAAG,SAAS,CACzB,UAAC,KAAa,EAAE,OAAe,IAAK,OAAA,KAAK,GAAG,GAAG,GAAG,OAAO,GAAA,EACzD,UAAC,KAAa,EAAE,OAAe;QAC7B,IAAI,YAAY,GAAG,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;QAC/D,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QACtD,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAA;QAE1C,OAAO;kCACA,KAAK,KAAE,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC;YAC3C,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC;SAClC,CAAA;KACF,CACF,CAAA;IAED,OAAO,UAAU,CACf,cAAc,CAAC,MAAM;UACjB,EAAE,KAAK,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;UACnE,IAAI,EACR,SAAS,CACV,CAAC,CAAC,CAAC,CAAA;AACN,CAAC;AAED,SAAS,UAAU,CACjB,YAAoC,EACpC,SAAiE;IAEjE,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;KACf;IAEK,IAAA,KAAK,GAA+B,YAAY,MAA3C,EAAE,YAAY,GAAiB,YAAY,aAA7B,EAAE,UAAU,GAAK,YAAY,WAAjB,CAAiB;IACtD,IAAI,OAAO,GAAG,YAAY,CAAA;IAC1B,IAAI,KAAK,GAAyB,EAAE,CAAA;IAEpC,OAAO,OAAO,GAAG,UAAU,EAAE;QAC3B,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;QACrC,OAAO,IAAI,CAAC,CAAA;KACb;IAED,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;IAE5B,OAAO;QACL,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC;QACtB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACZ,CAAA;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,CAAqB,EAAE,CAAqB;IACpE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACpB,CAAC;AAED,SAAS,WAAW,CAAC,CAAqB;IACxC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AACb,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAuB,EAAE,YAAoB,EAAE,cAAsB;IACxF,IAAA,WAAW,GAAqB,SAAS,YAA9B,EAAE,cAAc,GAAK,SAAS,eAAd,CAAc;IAC/C,IAAI,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,CAAA;IAC/D,IAAI,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,SAAS,CAAA;IACrE,IAAI,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAA;IACjC,IAAI,KAAK,GAAG,YAAY,CAAA;;IAGxB,OAAO,KAAK,GAAG,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,YAAY,EAAE,KAAK,IAAI,CAAC;QAAE,CAAC;IAE3E,OAAO,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,CAAC,EAAE;QACnC,IAAI,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,KAAK,SAAU,CAAA;QACnB,IAAI,WAAW,GAAG,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QACjF,IAAI,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAClD,IAAI,UAAU,GAAG,YAAY,CAAA;QAE7B;QACE,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;YAC5B,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EACxC;YAAE,UAAU,IAAI,CAAC,CAAA;SAAE;QAErB,IAAI,YAAY,GAAG,UAAU,EAAE;YAC7B,OAAO,EAAE,KAAK,OAAA,EAAE,YAAY,cAAA,EAAE,UAAU,YAAA,EAAE,CAAA;SAC3C;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,UAAU,CAAC,aAAwB,EAAE,cAAsB;IAClE,IAAM,WAAW,GAAG,SAAS,CAC3B,UAAC,IAAa,EAAE,UAAkB,EAAE,aAAqB,IAAK,OAAA,aAAa,CAAC,IAAI,CAAC,GAAA,EACjF,UAAC,IAAa,EAAE,UAAkB,EAAE,aAAqB;QACjD,IAAA,cAAc,GAAgB,IAAI,eAApB,EAAE,SAAS,GAAK,IAAI,UAAT,CAAS;QACxC,IAAI,YAAY,GAAG,SAAS,GAAG,aAAa,CAAA;QAC5C,IAAI,iBAAiB,GAAG,SAAS,GAAG,YAAY,CAAA;QAChD,IAAI,QAAgB,CAAA;QACpB,IAAI,WAAW,GAAc,EAAE,CAAA;QAE/B,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;YAC1B,QAAQ,GAAG,cAAc,CAAA;SAC1B;aAAM;YACL,KAAsB,UAAc,EAAd,iCAAc,EAAd,4BAAc,EAAd,IAAc,EAAE;gBAAjC,IAAI,SAAS,uBAAA;gBAChB,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,IAAI,GAAG,GAAG,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;oBAC1D,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;oBACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;iBACzB;qBAAM;oBACL,IAAI,GAAG,GAAG,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAA;oBAC7C,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;iBACzB;aACF;SACF;QAED,IAAI,YAAY,GAAG,CAAC,QAAQ,GAAG,UAAU,IAAI,iBAAiB,CAAA;QAC9D,OAAO,CAAC,QAAQ,GAAG,YAAY,wBAC1B,IAAI,KACP,SAAS,EAAE,YAAY,EACvB,cAAc,EAAE,WAAW,IAC3B,CAAA;KACH,CACF,CAAA;IAED,OAAO,aAAa,CAAC,GAAG,CAAC,UAAC,IAAa,IAAK,OAAA,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAA,CAAC,CAAA;AACzE,CAAC;AAED;AACA,SAAS,UAAU,CAAC,aAAwB;IAC1C,IAAI,KAAK,GAAiB,EAAE,CAAA;IAE5B,IAAM,WAAW,GAAG,SAAS,CAC3B,UAAC,IAAa,EAAE,UAAkB,EAAE,UAAkB,IAAK,OAAA,aAAa,CAAC,IAAI,CAAC,GAAA,EAC9E,UAAC,IAAa,EAAE,UAAkB,EAAE,UAAkB;QACpD,IAAI,IAAI,yBACH,IAAI,KACP,UAAU,YAAA;YACV,UAAU,YAAA,EACV,YAAY,EAAE,CAAC,GAChB,CAAA;QACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEhB,QACE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,EACvG;KACF,CACF,CAAA;IAED,SAAS,YAAY,CAAC,KAAgB,EAAE,UAAkB,EAAE,UAAkB;QAC5E,IAAI,YAAY,GAAG,CAAC,CAAA;QACpB,KAAiB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;YAAnB,IAAI,IAAI,cAAA;YACX,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,YAAY,CAAC,CAAA;SACjF;QACD,OAAO,YAAY,CAAA;KACpB;IAED,YAAY,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACjC,OAAO,KAAK,CAAA;AACd,CAAC;AAED;AAEA,SAAS,SAAS,CAChB,OAAkC,EAClC,QAAgC;IAEhC,IAAM,KAAK,GAA2B,EAAE,CAAA;IAExC,OAAO;QAAC,cAAa;aAAb,UAAa,EAAb,qBAAa,EAAb,IAAa;YAAb,yBAAa;;QACnB,IAAI,GAAG,GAAG,OAAO,eAAI,IAAI,CAAC,CAAA;QAC1B,OAAO,CAAC,GAAG,IAAI,KAAK;cAChB,KAAK,CAAC,GAAG,CAAC;eACT,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,eAAI,IAAI,CAAC,CAAC,CAAA;KACrC,CAAA;AACH;;SC9MgB,iBAAiB,CAC/B,IAAmB,EACnB,OAAmB,EACnB,UAAsC,EACtC,cAA0B;IAD1B,2BAAA,EAAA,iBAAsC;IACtC,+BAAA,EAAA,kBAA0B;IAE1B,IAAI,OAAO,GAAc,EAAE,CAAA;IAE3B,IAAI,UAAU,EAAE;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACvC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,IAAI,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;YAC7D,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CACpB,SAAS,IAAI,cAAc,IAAI,CAAC,CAAC;YACjC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAC5C,CAAA;YACD,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC5B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;aACzB,CAAC,CAAA;SACH;KACF;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;SAEe,sBAAsB,CACpC,IAAmB,EACnB,UAAqB;AACrB,gBAA0B,EAC1B,aAAsB;IAEtB,IAAI,SAAS,GAAe,EAAE,CAAA;IAC9B,IAAI,QAAQ,GAAkB,EAAE,CAAA;IAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACvC,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QAC3B,IAAI,OAAO,EAAE;YACX,SAAS,CAAC,IAAI,CAAC;gBACb,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,CAAC;gBACZ,IAAI,EAAE,OAAO;aACd,CAAC,CAAA;SACH;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SACvB;KACF;IAEG,IAAA,KAA6B,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,EAAE,aAAa,CAAC,EAAvF,QAAQ,cAAA,EAAE,YAAY,kBAAiE,CAAA;IAC7F,IAAI,aAAa,GAA4B,EAAE,CAAA;IAE/C,KAAoB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;QAAzB,IAAI,OAAO,iBAAA;QACd,aAAa,CAAC,IAAI,CAAC;YACjB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACxB,IAAI,EAAE,OAAO;SACd,CAAC,CAAA;KACH;IAED,KAAoB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;QAAzB,IAAI,OAAO,iBAAA;QACd,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;KACjD;IAED,OAAO,EAAE,aAAa,eAAA,EAAE,YAAY,cAAA,EAAE,CAAA;AACxC;;AC/EA,IAAM,mBAAmB,GAAG,eAAe,CAAC;IAC1C,IAAI,EAAE,SAAS;IACf,MAAM,EAAE,SAAS;IACjB,QAAQ,EAAE,KAAK;CAChB,CAAC,CAAA;AAMF;IAAkC,gCAAgC;IAAlE;;KAmBC;IAlBC,6BAAM,GAAN;QACE,IAAI,UAAU,GAAG;YACf,mBAAmB;YACnB,YAAY;SACb,CAAA;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAA;SAC3C;QAED,QACE,cAAC,aAAa,eACR,IAAI,CAAC,KAAK,IACd,iBAAiB,EAAE,mBAAmB,EACtC,eAAe,EAAE,UAAU,IAC3B,EACH;KACF;IACH,mBAAC;AAAD,CAnBA,CAAkC,aAAa;;ACA/C;IAAiC,+BAA+B;IAAhE;;KAaC;IAZC,4BAAM,GAAN;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAEpB,QACE,cAAC,cAAc,IAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,cAAc,EAAE,KAAK,CAAC,cAAc,IACjI,UAAC,UAAU,EAAE,YAAY,IAAK,QAC7B,YAAY;YACV,uBAAK,SAAS,EAAC,sBAAsB,EAAC,GAAG,EAAE,UAAU,IAAG,YAAY,CAAO,IAC9E,CACc,EAClB;KACF;IACH,kBAAC;AAAD,CAbA,CAAiC,aAAa;;ACuB9C;IAA6B,2BAA2B;IAAxD;QAAA,qEA8PC;QA7PC,mBAAa,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;;KA6PvC;;IA1PC,wBAAM,GAAN;QAAA,iBAuEC;QAtEK,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAA;QAEjD,IAAI,UAAU,GACZ,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI;aACvC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;aAC5C,cAAc,IAAI,KAAK,CAAC,iBAAiB,CAAC;YAC3C,EAAE,CAAA;QAEJ,IAAI,4BAA4B;SAC9B,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,iBAAiB;aACpD,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC;YAC1D,EAAE,CAAA;QAEJ,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAkB,CAAA;QAErG,QACE,cAAC,WAAW,IACV,KAAK,EAAE,KAAK,CAAC,KAAK,EAClB,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,KAAK,CAAC,cAAc,IAEnC,UAAC,SAAS,EAAE,UAAU,EAAE,SAAS,IAAK,QACrC,+BACE,GAAG,EAAE,SAAS,EACd,IAAI,EAAC,UAAU,EACf,SAAS,EAAE,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IACpF,SAAS,EACT,KAAK,CAAC,cAAc;YAExB,uBAAK,SAAS,EAAC,uBAAuB;gBACpC,uBAAK,SAAS,EAAC,oBAAoB;oBAChC,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,gBAAgB,EAAE,cAAc,CAAC;oBAC3D,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC;oBAClD,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC,CACtD;gBACN,uBAAK,SAAS,EAAC,wBAAwB,IACpC,KAAI,CAAC,YAAY,CAChB,YAAY,EACZ,4BAA4B,EAC5B,KAAK,EACL,KAAK,EACL,KAAK,CACN,CACG;gBACN,uBAAK,SAAS,EAAC,wBAAwB,IACpC,KAAI,CAAC,YAAY,CAChB,UAA2B,EAC3B,EAAE,EACF,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EACxB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAC1B,OAAO,CAAC,cAAc,CAAC,CACxB,CACG;gBACN,uBAAK,SAAS,EAAC,qCAAqC,IACjD,KAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAC5C;gBACN,cAAC,WAAW,IACV,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,KAAK,CAAC,cAAc,GACpC,CACE,CACH,IACN,CACW,EACf;KACF;IAED,8BAAY,GAAZ,UACE,YAA2B,EAC3B,cAA6C,EAC7C,UAAmB,EACnB,UAAmB,EACnB,eAAwB;QAElB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACpB,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClB,OAAO,iBAAiB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;SAC9C;QACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;KAC1G;IAED,wCAAsB,GAAtB,UACE,IAAmB;IACnB,cAA6C,EAC7C,UAAmB,EACnB,UAAmB,EACnB,eAAwB;QAL1B,iBAoDC;QA7CK,IAAA,KAAwE,IAAI,CAAC,OAAO,CAAC,OAAO,EAA1F,aAAa,mBAAA,EAAE,gBAAgB,sBAAA,EAAE,gBAAgB,sBAAA,EAAE,cAAc,oBAAyB,CAAA;QAC5F,IAAA,KAA4D,IAAI,CAAC,KAAK,EAApE,IAAI,UAAA,EAAE,UAAU,gBAAA,EAAE,cAAc,oBAAA,EAAE,UAAU,gBAAA,EAAE,OAAO,aAAe,CAAA;QAC1E,IAAI,QAAQ,GAAG,UAAU,IAAI,UAAU,IAAI,eAAe,CAAA;QAC1D,IAAI,UAAU,GAAG,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAA;QACtE,IAAA,KAAkC,sBAAsB,CAAC,IAAI,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,CAAC,EAAzG,aAAa,mBAAA,EAAE,YAAY,kBAA8E,CAAA;QAE/G,QACE,cAAC,QAAQ;YACN,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC;YAC3C,aAAa,CAAC,GAAG,CAAC,UAAC,YAAY;gBACxB,IAAA,GAAG,GAAW,YAAY,IAAvB,EAAE,IAAI,GAAK,YAAY,KAAjB,CAAiB;gBAChC,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAA;gBACnD,IAAI,SAAS,GAAG,QAAQ,IAAI,OAAO,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,CAAA;gBACxE,IAAI,MAAM,GAAG,gBAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;gBAChD,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,IAAI,IAAI,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;gBACtF,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;gBACpD,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,gBAAgB,CAAA;gBAEnF,QACE,uBACE,SAAS,EACP,2BAA2B;yBAC1B,OAAO,GAAG,kCAAkC,GAAG,EAAE,CAAC,EAErD,GAAG,EAAE,UAAU,EACf,KAAK,sBACH,UAAU,EAAE,SAAS,GAAI,EAAU,GAAG,QAAQ,IAC3C,MAAM,GACN,MAAM;oBAGX,cAAC,YAAY,aACX,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,UAAU,EACtB,eAAe,EAAE,eAAe,EAChC,UAAU,EAAE,UAAU,KAAK,cAAc,EACzC,OAAO,EAAE,OAAO,IACZ,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EACxC,CACE,EACP;aACF,CAAC,CACO,EACZ;KACF;;IAGD,oCAAkB,GAAlB,UAAmB,YAA6B,EAAE,IAAmB;QAC/D,IAAA,KAA8F,IAAI,CAAC,KAAK,EAAtG,aAAa,mBAAA,EAAE,WAAW,iBAAA,EAAE,UAAU,gBAAA,EAAE,OAAO,aAAA,EAAE,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,WAAW,iBAAe,CAAA;QAC5G,QACE,cAAC,QAAQ,QACN,YAAY,CAAC,GAAG,CAAC,UAAC,WAAW;YAC5B,IAAI,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACpD,IAAI,UAAU,GAAG,sBAAsB,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YAClE,QACE,cAAC,eAAe,IACd,GAAG,EAAE,cAAc,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC,EACxD,UAAU,EAAE,UAAU,EACtB,GAAG,EAAE,WAAW,CAAC,GAAG,EACpB,MAAM,EAAE,WAAW,CAAC,MAAM,EAC1B,aAAa,EAAE,aAAa,EAC5B,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,UAAU,EACtB,OAAO,EAAE,OAAO,EAChB,cAAc,EAAE,cAAc,EAC9B,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,WAAW,GACxB,EACH;SACF,CAAC,CACO,EACZ;KACF;IAED,gCAAc,GAAd,UAAe,IAAmB,EAAE,QAAgB;QAC9C,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,UAAU,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;QAEtG,IAAI,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,UAAC,OAAO,EAAE,CAAC;YACvC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACjB,QACE,uBACE,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EACvC,SAAS,EAAC,wBAAwB,EAClC,KAAK,EAAE,gBAAgB,CAAC,OAAO,CAAC,IAE/B,QAAQ,KAAK,UAAU;gBACtB,cAAC,OAAO,aAAC,GAAG,EAAE,GAAG,IAAM,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,EAAI;gBAC3E,UAAU,CAAC,QAAQ,CAAC,CAClB,EACP;SACF,CAAC,CAAA;QAEF,OAAO,cAAC,QAAQ,QAAE,QAAQ,CAAY,CAAA;KACvC;IAED,oCAAkB,GAAlB,UAAmB,IAAmB;QAChC,IAAA,KAAuB,IAAI,CAAC,KAAK,EAA/B,UAAU,gBAAA,EAAE,IAAI,UAAe,CAAA;QAErC,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,IAAI,CAAA;SAAE;QAEhC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,QAC1B,cAAC,gBAAgB,IACf,MAAM,EAAE,KAAK,EACb,IAAI,EAAE,IAAI;;YAEV,GAAG,EAAE,CAAC,IAEL,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,uBACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1E,KAAK,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,IAEzD,YAAY,CACT,IACP,CACgB,IACpB,CAAC,CAAA;KACH;IAED,kCAAgB,GAAhB,UAAiB,UAAsB;QACjC,IAAA,KAAqB,IAAI,CAAC,OAAO,EAA/B,KAAK,WAAA,EAAE,OAAO,aAAiB,CAAA;QACrC,IAAI,aAAa,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAC5C,IAAI,SAAS,GAAG,UAAU,CAAC,UAAU,CAAA;QACrC,IAAI,QAAQ,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAA;QAC3D,IAAI,IAAI,CAAA;QACR,IAAI,KAAK,CAAA;QAET,IAAI,aAAa,EAAE;;YAEjB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,CAAC,CAAA;SAC/D;QAED,IAAI,KAAK,EAAE;YACT,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAA;YACnB,KAAK,GAAG,SAAS,CAAA;SAClB;aAAM;YACL,IAAI,GAAG,SAAS,CAAA;YAChB,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAA;SACrB;QAED,IAAI,KAAK,GAAG;YACV,MAAM,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC;YACjC,IAAI,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG;YACtB,KAAK,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG;SACzB,CAAA;QAED,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;;YAE7C,KAAK,CAAC,KAAK,GAAG,YAAY,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;SACrD;QAED,OAAO,KAAK,CAAA;KACb;IACH,cAAC;AAAD,CA9PA,CAA6B,aAAa,GA8PzC;SAEe,iBAAiB,CAC/B,YAA2B,EAC3B,EAMC;QANC,UAAU,gBAAA,EAAE,OAAO,aAAA,EAAE,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,WAAW,iBAAA;IAQ7D,IAAI,eAAe,GACjB,CAAC,SAAS,GAAG,SAAS,CAAC,iBAAiB,GAAG,IAAI;SAC9C,WAAW,GAAG,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACpD,EAAE,CAAA;IACJ,QACE,cAAC,QAAQ,QACN,YAAY,CAAC,GAAG,CAAC,UAAC,GAAG;QACpB,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAA;QACnD,QACE,uBACE,GAAG,EAAE,UAAU,EACf,KAAK,EAAE,EAAE,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAI,EAAU,EAAE;YAE3E,cAAC,YAAY,aACX,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,KAAK,EACjB,eAAe,EAAE,KAAK,EACtB,UAAU,EAAE,UAAU,KAAK,cAAc,EACzC,OAAO,EAAE,KAAK,IACV,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EACxC,CACE,EACP;KACF,CAAC,CACO,EACZ;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,UAA0B;IAClD,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;KAC/B;IACD,OAAO;QACL,GAAG,EAAE,UAAU,CAAC,KAAK;QACrB,MAAM,EAAE,CAAC,UAAU,CAAC,GAAG;KACxB,CAAA;AACH,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,OAAsB;IAEtB,OAAO,UAAU,CAAC,GAAG,CAAC,UAAC,QAAQ,IAAK,OAAA,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAA,CAAC,CAAA;AAC9D;;AChTA;IAAqC,mCAAmC;IAAxE;QAAA,qEAoHC;QAnHS,sBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC1C,sBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC1C,2BAAqB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC/C,2BAAqB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAC/C,4BAAsB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QAChD,oBAAc,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;QAC/C,sBAAgB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;QACjD,eAAS,GAAG,SAAS,EAAkB,CAAA;QACvC,gBAAU,GAAG,IAAI,MAAM,EAAwB,CAAA;;KA2GxD;IAzGC,gCAAM,GAAN;QAAA,iBA8EC;QA7EK,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAC7B,IAAI,eAAe,GACjB,OAAO,CAAC,OAAO,CAAC,YAAY;YAC5B,KAAK,CAAC,UAAU;YAChB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAEhD,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAA;QAC/B,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvE,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QACtF,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QACtF,IAAI,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;QACzF,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QACjE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAEvE,QACE,uBAAK,SAAS,EAAC,kBAAkB,EAAC,GAAG,EAAE,IAAI,CAAC,SAAS;YACnD,yBACE,IAAI,EAAC,cAAc,EACnB,KAAK,EAAE;oBACL,QAAQ,EAAE,KAAK,CAAC,aAAa;oBAC7B,KAAK,EAAE,KAAK,CAAC,WAAW;iBACzB;gBAEA,KAAK,CAAC,iBAAiB;gBACxB,yBAAO,IAAI,EAAC,cAAc;oBACxB,sBAAI,IAAI,EAAC,KAAK;wBACX,KAAK,CAAC,IAAI,KACT,2CAAgB,SAAS,EAAC,kCAAkC;4BAC1D,uBAAK,SAAS,EAAC,uBAAuB;gCACpC,uBAAK,SAAS,EAAC,qCAAqC,IACjD,OAAO,eAAe,KAAK,QAAQ,KAClC,cAAC,gBAAgB,IAAC,MAAM,QAAC,IAAI,EAAE,KAAK,CAAC,OAAO,IACzC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,uBACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,CAAC,iCAAiC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAC3E,KAAK,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE,IAE9B,YAAY,CACT,IACP,CACgB,CACpB,CACG,CACF,CACH,CACN;wBACA,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,CAAC,IAAK,QAC5B,cAAC,OAAO,IACN,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,KAAK,EAAE,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1C,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,eAAe,EAAE,IAAI,CAAC,eAAe,EACrC,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAChC,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAChC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAC1C,gBAAgB,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAC1C,iBAAiB,EAAE,sBAAsB,CAAC,CAAC,CAAC,EAC5C,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAChC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACH,CAAC,CACC,CACC,CACF,CACJ,EACP;KACF;IAED,2CAAiB,GAAjB;QACE,IAAI,CAAC,YAAY,EAAE,CAAA;KACpB;IAED,4CAAkB,GAAlB;QACE,IAAI,CAAC,YAAY,EAAE,CAAA;KACpB;IAED,sCAAY,GAAZ;QACQ,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QAEpB,IACE,KAAK,CAAC,WAAW;YACjB,KAAK,CAAC,WAAW,KAAK,IAAI;UAC1B;YACA,KAAK,CAAC,WAAW,CACf,IAAI,aAAa,CACf,IAAI,CAAC,SAAS,CAAC,OAAO,EACtB,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,EACvD,IAAI;YACJ,KAAK,CACN,CACF,CAAA;SACF;KACF;IACH,sBAAC;AAAD,CApHA,CAAqC,aAAa,GAoHjD;AAED,SAAS,cAAc,CAAC,KAAqC,EAAE,KAAqB;IAClF,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAA,CAAC,CAAA;AAC7C;;ACzGA;;;IAG8B,4BAA2C;IAAzE;QAAA,qEAiKC;QAhKS,wBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAIxD,WAAK,GAAG;YACN,UAAU,EAAE,IAAI;SACjB,CAAA;QAmDD,kBAAY,GAAG,UAAC,EAAsB;YACpC,IAAI,EAAE,EAAE;gBACN,KAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,KAAI,EAAE;oBAC9C,EAAE,IAAA;oBACF,iBAAiB,EAAE,KAAI,CAAC,KAAK,CAAC,iBAAiB;iBAChD,CAAC,CAAA;aACH;iBAAM;gBACL,KAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,KAAI,CAAC,CAAA;aAClD;SACF,CAAA;QAcD,yBAAmB,GAAG,UAAC,OAAsB;YACrC,IAAA,kBAAkB,GAAK,KAAI,CAAC,KAAK,mBAAf,CAAe;YACjC,IAAA,UAAU,GAAK,KAAI,CAAC,KAAK,WAAf,CAAe;YAE/B,IAAI,kBAAkB,IAAI,UAAU,EAAE;gBACpC,IAAI,OAAO,CAAC,IAAI,EAAE;oBAChB,IAAI,KAAG,GAAG,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;oBACjD,KAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAG,CAAC,CAAA;oBACpB,IAAI,KAAG,EAAE;wBACP,KAAG,IAAI,CAAC,CAAA;qBACT;oBAED,kBAAkB,CAAC,KAAG,CAAC,CAAA;iBACxB;gBAED,OAAO,IAAI,CAAA;aACZ;YAED,OAAO,KAAK,CAAA;SACb,CAAA;QAED,qBAAe,GAAG,UAAC,SAA+B;YAChD,KAAI,CAAC,SAAS,GAAG,SAAS,CAAA;SAC3B,CAAA;QAED,sBAAgB,GAAG,UAAC,UAAsC;YACxD,KAAI,CAAC,QAAQ,CAAC,EAAE,UAAU,YAAA,EAAE,CAAC,CAAA;YAE7B,IAAI,KAAI,CAAC,KAAK,CAAC,YAAY,EAAE;gBAC3B,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;aACpC;SACF,CAAA;;KAiDF;IAxJC,yBAAM,GAAN;QACM,IAAA,KAAmB,IAAI,EAArB,KAAK,WAAA,EAAE,KAAK,WAAS,CAAA;QAE3B,QACE,uBACE,SAAS,EAAC,kBAAkB,EAC5B,GAAG,EAAE,IAAI,CAAC,YAAY,EACtB,KAAK,EAAE;;;gBAGL,KAAK,EAAE,KAAK,CAAC,WAAW;gBACxB,QAAQ,EAAE,KAAK,CAAC,aAAa;aAC9B;YAED,cAAC,aAAa,IACZ,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,SAAS,EAAE,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY,GAAG,EAAE,EACrD,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,iBAAiB,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,iDAC9D,QAAQ,EAAE,IAAI,CAAC,gBAAgB,GAC/B;YACF,cAAC,eAAe,IACd,KAAK,EAAE,KAAK,CAAC,KAAK,EAClB,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EACxC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,EAC1C,cAAc,EAAE,KAAK,CAAC,cAAc,EACpC,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EACxC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,EAC1C,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,WAAW,EAAE,IAAI,CAAC,eAAe,EACjC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,CACE,EACP;KACF;IAaD,oCAAiB,GAAjB;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;KACpF;IAED,qCAAkB,GAAlB,UAAmB,SAAwB;QACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;KAC9E;IAED,uCAAoB,GAApB;QACE,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAA;KAC9B;IAmCD,2BAAQ,GAAR,UAAS,YAAoB,EAAE,WAAmB;QAC5C,IAAA,KAAuB,IAAI,CAAC,OAAO,EAAjC,OAAO,aAAA,EAAE,OAAO,aAAiB,CAAA;QACjC,IAAA,SAAS,GAAK,IAAI,UAAT,CAAS;QAClB,IAAA,WAAW,GAAK,IAAI,CAAC,KAAK,YAAf,CAAe;QAC1B,IAAA,UAAU,GAAK,IAAI,CAAC,KAAK,WAAf,CAAe;QAC3B,IAAA,KAAiC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,EAArG,YAAY,kBAAA,EAAE,YAAY,kBAA2E,CAAA;QAE3G,IAAI,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;QAClD,IAAI,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;QAE5D,IAAI,QAAQ,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,EAAE;YACzC,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACrC,IAAI,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAClD,IAAI,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;YAC1D,IAAI,OAAO,GAAG,CAAC,WAAW,GAAG,OAAO,IAAI,UAAU,CAAA;YAClD,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,CAAA;YACvD,IAAI,SAAS,GAAG,SAAS,GAAG,YAAY,GAAG,cAAc,CAAA;YAEzD,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAA;YAC7C,IAAI,IAAI,GAAG,YAAY,CACrB,WAAW,CAAC,WAAW,EACvB,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAC1C,CAAA;YAED,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YACtC,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;YAE1C,OAAO;gBACL,WAAW,aAAA;gBACX,QAAQ,aACN,KAAK,EAAE,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,EACrB,MAAM,EAAE,KAAK,IACV,IAAI,CAAC,aAAa,CACtB;gBACD,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAC/B,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACjC,GAAG,EAAE,OAAO;oBACZ,MAAM,EAAE,OAAO,GAAG,UAAU;iBAC7B;gBACD,KAAK,EAAE,CAAC;aACT,CAAA;SACF;QAED,OAAO,IAAI,CAAA;KACZ;IACH,eAAC;AAAD,CAjKA,CAA8B,aAAa,GAiK1C;AAED,SAAS,kBAAkB,CAAC,YAAsB,EAAE,oBAAqC;IACvF,IAAI,YAAY,GAAG,oBAAoB,IAAI,YAAY,CAAA;IACvD,IAAI,YAAY,GAAG,oBAAoB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;IAEnE,IAAI,YAAY,KAAK,IAAI,EAAE;QACzB,YAAY,GAAG,YAAY,CAAA;QAC3B,YAAY,GAAG,CAAC,CAAA;;KAEjB;IAED,OAAO,EAAE,YAAY,cAAA,EAAE,YAAY,cAAA,EAAE,CAAA;AACvC;;;ICtOuC,qCAAkC;IAAzE;;KAoBC;IAnBC,sCAAU,GAAV,UAAW,KAAgB,EAAE,SAAsB;QACjD,IAAI,IAAI,GAAkB,EAAE,CAAA;QAE5B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;YAClD,IAAI,QAAQ,GAAG,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;YAErD,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC;oBACR,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,GAAG,EAAE,QAAQ,CAAC,GAAG;oBACjB,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;oBAC3D,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;oBACrD,GAAG,KAAA;iBACJ,CAAC,CAAA;aACH;SACF;QAED,OAAO,IAAI,CAAA;KACZ;IACH,wBAAC;AAAD,CApBA,CAAuC,MAAM;;;IC4CZ,+BAA+B;IAAhE;QAAA,qEAyCC;QAxCS,oBAAc,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;QACxC,YAAM,GAAG,IAAI,iBAAiB,EAAE,CAAA;QAChC,iBAAW,GAAG,SAAS,EAAY,CAAA;;KAsC5C;IApCC,4BAAM,GAAN;QAAA,iBAmCC;QAlCK,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QACvB,IAAA,WAAW,GAAoB,KAAK,YAAzB,EAAE,aAAa,GAAK,KAAK,cAAV,CAAU;QAE1C,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAA;QACjD,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;;;QAIhF,QACE,cAAC,QAAQ,IAAC,IAAI,EAAE,cAAc,GAAG,QAAQ,GAAG,KAAK,IAC9C,UAAC,OAAmB,EAAE,UAAqB,IAAK,QAC/C,cAAC,QAAQ,aACP,GAAG,EAAE,KAAI,CAAC,WAAW,IACjB,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,IACxE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EACxB,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,WAAW,EAAE,WAAW,EACxB,SAAS,EAAE,KAAK,CAAC,SAAS,EAC1B,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAC7B,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,EAC1C,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,YAAY,EAAE,KAAK,CAAC,YAAY,EAChC,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,OAAO,EAAE,OAAO,EAChB,gBAAgB,EAAE,cAAc,IAAI,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,EACzF,UAAU,EAAE,UAAU,EACtB,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,EAC5C,YAAY,EAAE,KAAK,CAAC,YAAY,IAChC,IACH,CACQ,EACZ;KACF;IACH,kBAAC;AAAD,CAzCA,CAAiC,aAAa,GAyC7C;SAEe,cAAc,CAAC,aAA4B,EAAE,WAAwB,EAAE,OAAgB;IACrG,IAAI,MAAM,GAAgB,EAAE,CAAA;IAE5B,KAAiB,UAAyB,EAAzB,KAAA,aAAa,CAAC,WAAW,EAAzB,cAAyB,EAAzB,IAAyB,EAAE;QAAvC,IAAI,IAAI,SAAA;QACX,MAAM,CAAC,IAAI,CAAC;YACV,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,WAAW,CAAC;YACjD,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,WAAW,CAAC;SAChD,CAAC,CAAA;KACH;IAED,OAAO,MAAM,CAAA;AACf;;AClFA;AACA;AACA,IAAM,mBAAmB,GAAG;IAC1B,EAAE,KAAK,EAAE,CAAC,EAAE;IACZ,EAAE,OAAO,EAAE,EAAE,EAAE;IACf,EAAE,OAAO,EAAE,EAAE,EAAE;IACf,EAAE,OAAO,EAAE,EAAE,EAAE;IACf,EAAE,OAAO,EAAE,EAAE,EAAE;CAChB,CAAA;SAEe,cAAc,CAC5B,WAAqB,EACrB,WAAqB,EACrB,qBAAsC,EACtC,YAAsB,EACtB,OAAgB;IAEhB,IAAI,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,QAAQ,GAAG,WAAW,CAAA;IAC1B,IAAI,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;IACpC,IAAI,aAAa,GAAG,qBAAqB,IAAI,oBAAoB,CAAC,YAAY,CAAC,CAAA;IAC/E,IAAI,KAAK,GAAmB,EAAE,CAAA;IAE9B,OAAO,SAAS,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE;QACnD,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAC1C,IAAI,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,aAAa,CAAC,KAAK,IAAI,CAAA;QAE1E,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,MAAA;YACJ,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE;YACvB,UAAU,EAAE,mBAAmB,CAAC,IAAI,CAAC;YACrC,SAAS,WAAA;SACV,CAAC,CAAA;QAEF,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;QAC/C,YAAY,GAAG,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;KACxD;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,YAAY;IACxC,IAAI,CAAC,CAAA;IACL,IAAI,aAAa,CAAA;IACjB,IAAI,aAAa,CAAA;;IAGjB,KAAK,CAAC,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QACvD,aAAa,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAA;QACtD,aAAa,GAAG,oBAAoB,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;QACjE,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC,EAAE;YAC/C,OAAO,aAAa,CAAA;SACrB;KACF;IAED,OAAO,YAAY,CAAA;AACrB;;;IC/DqC,mCAAY;IAAjD;QAAA,qEAoFC;QAnFS,wBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAChD,oBAAc,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;;KAkFjD;IAhFC,gCAAM,GAAN;QAAA,iBA+EC;QA9EK,IAAA,KAA6C,IAAI,CAAC,OAAO,EAAvD,OAAO,aAAA,EAAE,OAAO,aAAA,EAAE,oBAAoB,0BAAiB,CAAA;QACvD,IAAA,KAAK,GAAK,IAAI,MAAT,CAAS;QACd,IAAA,WAAW,GAAK,KAAK,YAAV,CAAU;QAC3B,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;QAC9E,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QACtD,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CACjC,WAAW,CAAC,WAAW,EACvB,WAAW,CAAC,WAAW,EACvB,OAAO,CAAC,iBAAiB,EACzB,OAAO,CAAC,YAAY,EACpB,OAAO,CACR,CAAA;QACK,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAY;QAC7B,IAAI,eAAe,GAAG,CAAC,WAAW,CAAA;QAClC,IAAI,eAAe,GAAG,WAAW,CAAA;QAEjC,IAAI,aAAa,GAAG,OAAO,CAAC,UAAU,KACpC,cAAC,SAAS,IACR,KAAK,EAAE,aAAa,CAAC,WAAW,EAChC,WAAW,EAAE,WAAW,EACxB,oBAAoB,QACpB,WAAW,EAAE,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,GACzD,CACH,CAAA;QAED,IAAI,aAAa,GAAG,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,MAAM,UAAC,UAAoC,IAAK,QAC/F,cAAC,QAAQ,eACH,UAAU,CAAC,MAAM,IACrB,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,aAAa,EAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAC1C,aAAa,EAAE,UAAU,CAAC,aAAa,EACvC,YAAY,EAAE,UAAU,CAAC,iBAAiB,EAC1C,cAAc,EAAE,eAAe,GAAG,KAAI,CAAC,kBAAkB,GAAG,IAAI,EAChE,eAAe,EAAE,KAAK,EACtB,UAAU,EAAE,KAAK,EACjB,gBAAgB,EAAE,KAAI,CAAC,WAAW,EAClC,WAAW,EAAE,UAAU,CAAC,WAAW,EACnC,YAAY,EAAE,UAAU,CAAC,YAAY,EACrC,QAAQ,EAAE,KAAK,CAAC,QAAQ,IACpB,KAAI,CAAC,sBAAsB,EAAE,EACjC,IACH,CAAC,CAAA;QAEF,IAAI,eAAe,GAAG,UAAC,UAAoC,IAAK,QAC9D,cAAC,WAAW,eACN,UAAU,CAAC,KAAK,IACpB,aAAa,EAAE,aAAa,EAC5B,WAAW,EAAE,WAAW,EACxB,IAAI,EAAE,eAAe,EACrB,YAAY,EAAE,OAAO,CAAC,YAAY,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EACxB,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,EAC/C,aAAa,EAAE,UAAU,CAAC,aAAa,EACvC,WAAW,EAAE,UAAU,CAAC,WAAW,EACnC,YAAY,EAAE,UAAU,CAAC,YAAY,EACrC,YAAY,EAAE,KAAI,CAAC,gBAAgB,EACnC,UAAU,EAAE,UAAU,CAAC,UAAU,EACjC,kBAAkB,EAAE,KAAI,CAAC,sBAAsB,IAC/C,IACH,CAAA;QAED,OAAO,eAAe;cAClB,IAAI,CAAC,mBAAmB,CACxB,aAAa,EACb,aAAa,EACb,eAAe,EACf,aAAa,CAAC,MAAM,EACpB,WAAW,EACX,SAAS,EACT,IAAI,CAAC,KAAK,CAAC,UAAU,CACtB;cACC,IAAI,CAAC,kBAAkB,CACvB,aAAa,EACb,aAAa,EACb,eAAe,CAChB,CAAA;KACJ;IACH,sBAAC;AAAD,CApFA,CAAqC,YAAY,GAoFhD;SAEe,kBAAkB,CAAC,WAAwB,EAAE,oBAA0C;IACrG,IAAI,SAAS,GAAG,IAAI,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;IAEjF,OAAO,IAAI,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;AAC5C;;ACxGO,IAAM,eAAe,GAAG;IAC7B,UAAU,EAAE,OAAO;CACpB;;ACaD,WAAe,YAAY,CAAC;IAC1B,WAAW,EAAE,cAAc;IAC3B,cAAc,EAAE,eAAe;IAC/B,KAAK,EAAE;QAEL,QAAQ,EAAE;YACR,SAAS,EAAE,eAAe;YAC1B,cAAc,EAAE,IAAI;YACpB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,UAAU;YACxB,gBAAgB,EAAE,IAAI;SACvB;QAED,WAAW,EAAE;YACX,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;SACtB;QAED,YAAY,EAAE;YACZ,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;SACvB;KAEF;CACF,CAAC;;;;;"}