// src/components/ServiceCard.jsx

import React from "react";
import { Card, Button } from "react-bootstrap";

const AdminServiceCard = ({ service, onEdit, onDelete }) => {
  const cardStyle = {
    borderLeft: "4px solid #228B22", // var(--primary-green)
    borderRadius: "8px",
    boxShadow: "0 2px 10px rgba(0,0,0,0.05)",
  };

  return (
    <Card style={cardStyle} className="h-100">
      <Card.Body className="d-flex flex-column">
        <Card.Title>{service.name}</Card.Title>
        <Card.Subtitle className="mb-2 text-muted">
          Price: ${service.price} | Duration: {service.duration_minutes} mins
        </Card.Subtitle>
        <Card.Text className="flex-grow-1">{service.description}</Card.Text>
        <div className="mt-auto">
          <Button variant="outline-secondary" size="sm" className="me-2" onClick={() => onEdit(service)}>
            <i className="bi bi-pencil-fill"></i> Edit
          </Button>
          <Button variant="outline-danger" size="sm" onClick={() => onDelete(service.id)}>
            <i className="bi bi-trash-fill"></i> Delete
          </Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default AdminServiceCard;



