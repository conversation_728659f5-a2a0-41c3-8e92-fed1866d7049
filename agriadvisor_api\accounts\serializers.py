# accounts/serializers.py

from rest_framework import serializers
from django.db.models import Avg
from .models import <PERSON><PERSON><PERSON>, Tenant, FarmerProfile, ExpertProfile

# --- Child Serializers (for nesting) ---
class TenantSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tenant
        fields = ['id', 'name']

class ExpertProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpertProfile
        fields = ['specialty', 'bio']

class FarmerProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = FarmerProfile
        fields = ['location_country', 'interests']

# --- Main Serializer for Generic User Display ---
class CustomUserSerializer(serializers.ModelSerializer):
    tenant = TenantSerializer(read_only=True)
    class Meta:
        model = CustomUser
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'role', 'tenant']

# --- Serializer for Farmer Registration ---
class FarmerRegistrationSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(write_only=True)
    class Meta:
        model = CustomUser
        fields = ['username', 'password', 'email', 'first_name', 'last_name', 'tenant_name']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        tenant_name = validated_data.pop('tenant_name')
        tenant = Tenant.objects.create(name=tenant_name)
        user = CustomUser.objects.create_user(role='farmer', tenant=tenant, **validated_data)
        FarmerProfile.objects.create(user=user)
        return user

# --- Serializers for the Expert Workflow ---
class ExpertReadSerializer(serializers.ModelSerializer):
    """FOR FARMERS TO READ: Displays expert info including ratings."""
    expert_profile = ExpertProfileSerializer(read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = ['id', 'first_name', 'last_name', 'expert_profile', 'average_rating', 'review_count']
    
    def get_average_rating(self, obj):
        avg = obj.received_reviews.aggregate(Avg('rating')).get('rating__avg')
        return round(avg, 1) if avg else 0

    def get_review_count(self, obj):
        return obj.received_reviews.count()

class ExpertAdminSerializer(serializers.ModelSerializer):
    """FOR ADMINS TO WRITE: Used in the admin panel to create/update experts."""
    expert_profile = ExpertProfileSerializer()
    full_name = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = CustomUser
        fields = ['id', 'email', 'first_name', 'last_name', 'expert_profile', 'full_name']
        extra_kwargs = {
            'first_name': {'read_only': True},
            'last_name': {'read_only': True},
        }

    def create(self, validated_data):
        profile_data = validated_data.pop('expert_profile')
        full_name = validated_data.pop('full_name')

        # Split full name into first and last name
        first_name, last_name = (full_name.split(' ', 1) + [''])[:2]

        # Generate username from email
        base_username = validated_data.get('email').split('@')[0]
        tenant = self.context['request'].user.tenant

        # Ensure username is unique globally (not just within tenant)
        username = base_username
        counter = 1
        while CustomUser.objects.filter(username=username).exists():
            username = f"{base_username}{counter}"
            counter += 1

        # Generate random password
        from django.utils.crypto import get_random_string
        password = get_random_string(12)

        # Create user
        user = CustomUser.objects.create_user(
            username=username,
            password=password,
            email=validated_data.get('email'),
            first_name=first_name,
            last_name=last_name,
            role='expert',
            tenant=tenant
        )

        # Create expert profile
        ExpertProfile.objects.create(user=user, **profile_data)
        return user

    def update(self, instance, validated_data):
        profile_data = validated_data.pop('expert_profile', None)
        full_name = validated_data.pop('full_name', None)

        if full_name:
            instance.first_name, instance.last_name = (full_name.split(' ', 1) + [''])[:2]

        instance.email = validated_data.get('email', instance.email)
        instance.save()

        if profile_data:
            profile = instance.expert_profile
            profile.specialty = profile_data.get('specialty', profile.specialty)
            profile.bio = profile_data.get('bio', profile.bio)
            profile.save()

        return instance

    def update(self, instance, validated_data):
        profile_data = validated_data.pop('expert_profile', None)
        password = validated_data.pop('password', None)
        instance = super().update(instance, validated_data)
        if password:
            instance.set_password(password)
            instance.save()
        if profile_data:
            profile, created = ExpertProfile.objects.get_or_create(user=instance)
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()
        return instance
    
    


