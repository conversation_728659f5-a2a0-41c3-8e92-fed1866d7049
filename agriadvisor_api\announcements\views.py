from rest_framework import viewsets, permissions
from rest_framework.response import Response
from .models import Announcement
from .serializers import AnnouncementSerializer
from core.views import BaseTenantViewSet # For the Admin
from rest_framework.views import APIView

class AnnouncementAdminViewSet(BaseTenantViewSet):
    """
    Endpoint for an Admin to manage announcements for their tenant.
    """
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer

class CurrentAnnouncementView(APIView):
    """
    Endpoint for logged-in users to get the current active announcement
    for their role and tenant.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        # Find an active announcement for the user's specific role OR for 'all' users
        announcement = Announcement.objects.filter(
            tenant=user.tenant,
            target_role__in=[user.role, 'all'],
            is_active=True
        ).order_by('-created_at').first() # Get the newest one if multiple match
        
        if announcement:
            serializer = AnnouncementSerializer(announcement)
            return Response(serializer.data)
        else:
            return Response({}) # Return an empty object if no active announcement



