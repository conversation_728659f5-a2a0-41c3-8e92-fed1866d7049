// src/components/ExpertCard.jsx

import React from "react";
import { Card, Button, Badge } from "react-bootstrap";

// A simple component for the user avatar with initials
const UserAvatar = ({ user }) => {
  const getInitials = () => {
    const firstName = user?.first_name || "";
    const lastName = user?.last_name || "";
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const avatarStyle = {
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    backgroundColor: "#006400", // dark-green
    color: "white",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "1.5rem",
    fontWeight: "bold",
  };

  return <div style={avatarStyle}>{getInitials()}</div>;
};

// Star rating display component
const StarDisplay = ({ rating, reviewCount }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="d-flex align-items-center justify-content-center mb-2">
      <div className="me-2">
        {/* Full stars */}
        {[...Array(fullStars)].map((_, i) => (
          <span key={`full-${i}`} style={{ color: "#ffc107" }}>★</span>
        ))}
        {/* Half star */}
        {hasHalfStar && <span style={{ color: "#ffc107" }}>☆</span>}
        {/* Empty stars */}
        {[...Array(emptyStars)].map((_, i) => (
          <span key={`empty-${i}`} style={{ color: "#e4e5e9" }}>★</span>
        ))}
      </div>
      <small className="text-muted">
        {rating > 0 ? `${rating} (${reviewCount} review${reviewCount !== 1 ? 's' : ''})` : 'No reviews yet'}
      </small>
    </div>
  );
};

const ExpertCard = ({ expert, onEdit, onDelete }) => {
  return (
    <Card className="h-100 shadow-sm text-center">
      <Card.Body>
        <div className="d-flex justify-content-center mb-3">
          <UserAvatar user={expert} />
        </div>
        <Card.Title>
          {expert.first_name} {expert.last_name}
        </Card.Title>
        <Card.Subtitle className="mb-2 text-muted">{expert.expert_profile?.specialty || "No specialty"}</Card.Subtitle>

        {/* Star Rating Display */}
        <StarDisplay
          rating={expert.average_rating || 0}
          reviewCount={expert.review_count || 0}
        />

        <Card.Text className="text-muted small">{expert.expert_profile?.bio || "No bio provided."}</Card.Text>

        {/* Payment Status Badge */}
        {expert.expert_profile?.payments_enabled && (
          <Badge bg="success" className="mb-2">
            <i className="bi bi-check-circle me-1"></i>
            Payments Enabled
          </Badge>
        )}
      </Card.Body>
      <Card.Footer className="bg-white border-0">
        <Button variant="outline-secondary" size="sm" className="me-2" onClick={() => onEdit(expert)}>
          <i className="bi bi-pencil-fill"></i> Edit
        </Button>
        <Button variant="outline-danger" size="sm" onClick={() => onDelete(expert.id)}>
          <i className="bi bi-trash-fill"></i> Delete
        </Button>
      </Card.Footer>
    </Card>
  );
};

export default ExpertCard;



