# core/permissions.py

from rest_framework.permissions import BasePermission

class IsAdminUser(BasePermission):
    """
    Allows access only to users with the 'admin' role.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'admin'

class IsExpertUser(BasePermission):
    """
    Allows access only to users with the 'expert' role.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'expert'

class IsFarmerUser(BasePermission):
    """
    Allows access only to users with the 'farmer' role.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'farmer'
    
    
    