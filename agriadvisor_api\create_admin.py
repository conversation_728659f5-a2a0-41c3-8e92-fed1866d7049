#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agriadvisor.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import CustomUser

User = get_user_model()

# Create admin user
if not User.objects.filter(username='admin').exists():
    admin_user = User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        first_name='Admin',
        last_name='User',
        role='admin',
        is_staff=True,
        is_superuser=True
    )
    print("Admin user created successfully!")
    print("Username: admin")
    print("Password: admin123")
    print("Email: <EMAIL>")
else:
    print("Admin user already exists!")

# Create a test farmer
if not User.objects.filter(username='farmer1').exists():
    farmer_user = User.objects.create_user(
        username='farmer1',
        email='<EMAIL>',
        password='farmer123',
        first_name='<PERSON>',
        last_name='Farmer',
        role='farmer'
    )
    print("Test farmer created successfully!")
    print("Username: farmer1")
    print("Password: farmer123")
else:
    print("Test farmer already exists!")

# Create a test expert
if not User.objects.filter(username='expert1').exists():
    expert_user = User.objects.create_user(
        username='expert1',
        email='<EMAIL>',
        password='expert123',
        first_name='Jane',
        last_name='Expert',
        role='expert'
    )
    print("Test expert created successfully!")
    print("Username: expert1")
    print("Password: expert123")
else:
    print("Test expert already exists!")
