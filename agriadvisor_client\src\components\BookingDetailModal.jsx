// src/components/BookingDetailModal.jsx

import React from "react";
import { Modal, Button, ListGroup } from "react-bootstrap";

const BookingDetailModal = ({ show, onHide, booking, onMarkComplete, userRole }) => {
  if (!booking) return null;

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Booking Details</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <ListGroup variant="flush">
          <ListGroup.Item>
            <strong>Service:</strong> {booking.extendedProps.service}
          </ListGroup.Item>
          <ListGroup.Item>
            <strong>{userRole === "expert" ? "Farmer:" : "Expert:"}</strong>
            {userRole === "expert" ? booking.extendedProps.farmer : booking.extendedProps.expert}
          </ListGroup.Item>
          <ListGroup.Item>
            <strong>Date & Time:</strong> {new Date(booking.start).toLocaleString()}
          </ListGroup.Item>
          <ListGroup.Item>
            <strong>Status:</strong>{" "}
            <span
              className={`text-capitalize badge bg-${
                booking.extendedProps.status === "confirmed" ? "primary" : "success"
              }`}
            >
              {booking.extendedProps.status}
            </span>
          </ListGroup.Item>
        </ListGroup>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
        {/* Only show the "Mark as Complete" button to Experts for Confirmed bookings */}
        {userRole === "expert" && booking.extendedProps.status === "confirmed" && (
          <Button variant="success" onClick={() => onMarkComplete(booking.id)}>
            Mark as Complete
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default BookingDetailModal;

