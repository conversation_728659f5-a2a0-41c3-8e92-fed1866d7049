// src/pages/NewBookingPage.jsx

import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Container, Form, <PERSON><PERSON>, Card, Spinner, Alert } from "react-bootstrap";

// Import all the services we need
import { getFarmerExperts } from "../services/expertService";
import { checkAvailability } from "../services/availabilityService";
import { createPaymentIntent } from "../services/paymentService";


// Import the payment modal
import PaymentModal from "../components/PaymentModal";

const NewBookingPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { service } = location.state || {};

  // State for data fetching
  const [experts, setExperts] = useState([]);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [loadingExperts, setLoadingExperts] = useState(true);
  const [loadingSlots, setLoadingSlots] = useState(false);
  const [error, setError] = useState("");

  // State for form selections
  const [selectedExpert, setSelectedExpert] = useState("");
  const [selectedSlot, setSelectedSlot] = useState("");

  // State for the payment flow
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [clientSecret, setClientSecret] = useState(null);
  const [bookingDetails, setBookingDetails] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Effect 1: Fetch the list of experts when the page loads.
  useEffect(() => {
    const fetchExperts = async () => {
      try {
        const data = await getFarmerExperts();
        setExperts(data);
      } catch (err) {
        setError("Could not load available experts.");
      } finally {
        setLoadingExperts(false);
      }
    };
    fetchExperts();
  }, []);

  // Effect 2: Fetch available slots whenever an expert is selected.
  useEffect(() => {
    if (selectedExpert && service) {
      const fetchSlots = async () => {
        setLoadingSlots(true);
        setAvailableSlots([]); // Clear old slots
        setSelectedSlot(""); // Reset selection
        try {
          const params = {
            expert_id: selectedExpert,
            service_id: service.id,
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1, // Fetch for the current month
          };
          const response = await checkAvailability(params);
          setAvailableSlots(response.data);
        } catch (err) {
          setError("Could not fetch this expert's availability.");
        } finally {
          setLoadingSlots(false);
        }
      };
      fetchSlots();
    }
  }, [selectedExpert, service]);

  // This function runs when the user clicks the final "Proceed" button.
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsProcessing(true);
    setError("");

    const details = {
      serviceName: service.name,
      date: new Date(selectedSlot).toLocaleDateString(),
      time: new Date(selectedSlot).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      amount: parseFloat(service.price),
    };
    setBookingDetails(details);

    try {
      const intentData = await createPaymentIntent({
        service_id: service.id,
        expert_id: selectedExpert,
        booking_time: new Date(selectedSlot).toISOString(),
      });
      setClientSecret(intentData.clientSecret);
      setShowPaymentModal(true);
    } catch (err) {
      setError("Could not initiate payment. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    alert("Payment successful! Your booking is confirmed.");
    navigate("/my-bookings");
  };

  // Helper to group slots by day for display
  const groupSlotsByDay = () => {
    return availableSlots.reduce((acc, slot) => {
      const day = new Date(slot).toDateString();
      if (!acc[day]) acc[day] = [];
      acc[day].push(slot);
      return acc;
    }, {});
  };
  const groupedSlots = groupSlotsByDay();

  if (!service) {
    return (
      <Container className="mt-4">
        <Alert variant="warning">
          No service selected. Please <a href="/browse-services">go back</a>.
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header as="h4">Book: {service.name}</Card.Header>
        <Card.Body>
          <p>{service.description}</p>
          <hr />

          {loadingExperts ? (
            <div className="text-center">
              <Spinner animation="border" />
            </div>
          ) : (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>
                  <strong>1. Select an Expert</strong>
                </Form.Label>
                <Form.Select value={selectedExpert} onChange={(e) => setSelectedExpert(e.target.value)} required>
                  <option value="">Choose an expert...</option>
                  {experts.map((expert) => (
                    <option key={expert.id} value={expert.id}>
                      {expert.first_name} {expert.last_name} ({expert.expert_profile?.specialty})
                      {expert.average_rating > 0 ? ` ⭐${expert.average_rating}` : ""} ✓
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              {selectedExpert && (
                <Form.Group className="mb-3">
                  <Form.Label>
                    <strong>2. Select an Available Slot</strong>
                  </Form.Label>
                  {loadingSlots ? (
                    <div className="mt-2">
                      <Spinner size="sm" /> Fetching schedule...
                    </div>
                  ) : Object.keys(groupedSlots).length > 0 ? (
                    Object.entries(groupedSlots).map(([day, slots]) => (
                      <div key={day} className="mb-3 p-2 border rounded">
                        <strong>{day}</strong>
                        <div className="d-flex flex-wrap mt-2">
                          {slots.map((slot) => (
                            <Button
                              key={slot}
                              variant={selectedSlot === slot ? "success" : "outline-primary"}
                              className="m-1"
                              onClick={() => setSelectedSlot(slot)}
                            >
                              {new Date(slot).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                            </Button>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <Alert variant="info" className="mt-2">
                      No available slots for this expert in the current month.
                    </Alert>
                  )}
                </Form.Group>
              )}

              {error && (
                <Alert variant="danger" className="mt-3">
                  {error}
                </Alert>
              )}

              <div className="d-grid mt-4">
                <Button variant="primary" type="submit" disabled={!selectedSlot || isProcessing}>
                  {isProcessing ? "Initiating..." : `Proceed to Payment ($${parseFloat(service.price).toFixed(2)})`}
                </Button>
              </div>
            </Form>
          )}
        </Card.Body>
      </Card>

      {clientSecret && bookingDetails && (
        <PaymentModal
          show={showPaymentModal}
          onHide={() => setShowPaymentModal(false)}
          clientSecret={clientSecret}
          bookingDetails={bookingDetails}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}
    </Container>
  );
};

export default NewBookingPage;



