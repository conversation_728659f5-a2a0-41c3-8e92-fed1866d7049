// src/pages/ForgotPasswordPage.jsx

import React, { useState } from "react";
import { Link } from "react-router-dom";
import { Container, Form, <PERSON><PERSON>, Card, Alert, Row, Col } from "react-bootstrap";
import { forgotPassword } from "../services/authService"; // We need to add this function

// Premium Forgot Password CSS
const premiumForgotCSS = `
  .premium-forgot-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
  }

  .premium-forgot-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .premium-forgot-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: 25px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    animation: loginSlideIn 0.8s ease-out;
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .premium-forgot-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #fa709a 0%, #fee140 50%, #43e97b 100%);
  }

  .forgot-title {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
  }

  .premium-forgot-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    padding: 15px 30px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .premium-forgot-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumForgotCSS;
  document.head.appendChild(styleSheet);
}

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await forgotPassword({ email });
      setMessage(response.detail);
      setError("");
    } catch (err) {
      setError("An error occurred. Please try again.");
    }
  };

  return (
    <div className="premium-forgot-container">
      <Container>
        <Row className="justify-content-center">
          <Col xs={12} sm={10} md={8} lg={6} xl={5}>
            <Card className="premium-forgot-card">
              <Card.Body className="p-5">
                {/* Brand Logo */}
                <div className="brand-logo">
                  🔑
                </div>

                {/* Forgot Password Header */}
                <div className="login-header">
                  <h1 className="forgot-title">Reset Password</h1>
                  <p className="login-subtitle">
                    {!message ? "We'll help you get back to your account" : "Check your email for reset instructions"}
                  </p>
                </div>

                {/* Success Message */}
                {message && (
                  <Alert variant="success" className="premium-alert">
                    <i className="bi bi-check-circle-fill me-2"></i>
                    {message}
                  </Alert>
                )}

                {/* Error Alert */}
                {error && (
                  <Alert variant="danger" className="premium-alert">
                    <i className="bi bi-exclamation-triangle-fill me-2"></i>
                    {error}
                  </Alert>
                )}

                {/* Reset Form */}
                {!message && (
                  <Form onSubmit={handleSubmit}>
                    <div className="mb-4 p-3 rounded-3" style={{
                      background: 'linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%)',
                      border: '1px solid rgba(250, 112, 154, 0.2)'
                    }}>
                      <small className="text-muted">
                        <i className="bi bi-info-circle me-2"></i>
                        Enter your email address and we'll send you a secure link to reset your password.
                      </small>
                    </div>

                    <Form.Group className="mb-4">
                      <Form.Label className="premium-form-label">
                        <i className="bi bi-envelope me-2"></i>
                        Email Address
                      </Form.Label>
                      <Form.Control
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="premium-form-control"
                        placeholder="Enter your registered email address"
                      />
                    </Form.Group>

                    <Button type="submit" className="w-100 premium-forgot-btn">
                      <i className="bi bi-send me-2"></i>
                      Send Reset Link
                    </Button>
                  </Form>
                )}

                {/* Navigation Links */}
                <div className="login-links">
                  <div>
                    <Link to="/login" className="login-link">
                      <i className="bi bi-arrow-left me-1"></i>
                      Back to Login
                    </Link>
                  </div>
                  {!message && (
                    <div className="mt-2">
                      <span className="text-muted">Don't have an account? </span>
                      <Link to="/register" className="login-link">
                        <i className="bi bi-person-plus me-1"></i>
                        Create Account
                      </Link>
                    </div>
                  )}
                </div>

                {/* Additional Info */}
                <div className="text-center mt-4 pt-3" style={{ borderTop: '1px solid #e9ecef' }}>
                  <small className="text-muted">
                    <i className="bi bi-shield-check me-1"></i>
                    Secure password recovery for AgriAdvisor
                  </small>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default ForgotPasswordPage;
