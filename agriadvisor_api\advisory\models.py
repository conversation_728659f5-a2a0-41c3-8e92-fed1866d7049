# advisory/models.py
from django.db import models
# Make sure to get CustomUser and Tenant from the accounts app
from accounts.models import CustomUser, Tenant

class Service(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD') # e.g., 'USD', 'EUR'
    duration_minutes = models.PositiveIntegerField(default=60)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="services")

    def __str__(self):
        return f"{self.name} ({self.tenant.name})"

class Booking(models.Model):
    STATUS_CHOICES = (
        ('pending_payment', 'Pending Payment'),
        ('confirmed', 'Confirmed'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    )
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    expert = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="expert_bookings", limit_choices_to={'role': 'expert'})
    farmer = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="farmer_bookings", limit_choices_to={'role': 'farmer'})
    booking_time = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending_payment')
    stripe_payment_intent_id = models.CharField(max_length=255, blank=True, null=True)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name="bookings")

    def __str__(self):
        return f"Booking for {self.service.name} with {self.expert.username} at {self.booking_time}"
    
    
    
    