# Generated by Django 5.2.5 on 2025-08-25 20:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_customuser_phone_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='farminginterest',
            name='description',
        ),
        migrations.AlterField(
            model_name='customuser',
            name='tenant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='users', to='accounts.tenant'),
        ),
        migrations.AlterField(
            model_name='expertprofile',
            name='specialty',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='tenant',
            name='name',
            field=models.CharField(max_length=255, unique=True),
        ),
    ]
