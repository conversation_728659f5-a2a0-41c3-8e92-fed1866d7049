# accounts/management/commands/setup_expert_payments.py

from django.core.management.base import BaseCommand
from accounts.models import CustomUser, ExpertProfile


class Command(BaseCommand):
    help = 'Set up payment capabilities for experts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--expert-id',
            type=int,
            help='ID of the expert to set up payments for',
        )
        parser.add_argument(
            '--stripe-account-id',
            type=str,
            help='Stripe Connect account ID for the expert',
        )
        parser.add_argument(
            '--list',
            action='store_true',
            help='List all experts and their payment status',
        )

    def handle(self, *args, **options):
        if options['list']:
            self.list_experts()
        elif options['expert_id'] and options['stripe_account_id']:
            self.setup_expert_payment(options['expert_id'], options['stripe_account_id'])
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please provide either --list or both --expert-id and --stripe-account-id'
                )
            )

    def list_experts(self):
        """List all experts and their payment status"""
        experts = CustomUser.objects.filter(role='expert').select_related('expert_profile')
        
        self.stdout.write(self.style.SUCCESS('\nExpert Payment Status:'))
        self.stdout.write('-' * 80)
        
        for expert in experts:
            profile = getattr(expert, 'expert_profile', None)
            if profile:
                status = "✓ Enabled" if profile.stripe_account_id else "✗ Not Set Up"
                stripe_id = profile.stripe_account_id or "None"
                self.stdout.write(
                    f"ID: {expert.id:3} | {expert.first_name} {expert.last_name:20} | "
                    f"Status: {status:12} | Stripe ID: {stripe_id}"
                )
            else:
                self.stdout.write(
                    f"ID: {expert.id:3} | {expert.first_name} {expert.last_name:20} | "
                    f"Status: ✗ No Profile | Stripe ID: None"
                )

    def setup_expert_payment(self, expert_id, stripe_account_id):
        """Set up payment for a specific expert"""
        try:
            expert = CustomUser.objects.get(id=expert_id, role='expert')
            profile, created = ExpertProfile.objects.get_or_create(user=expert)
            
            profile.stripe_account_id = stripe_account_id
            profile.payouts_enabled = True
            profile.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully set up payments for {expert.first_name} {expert.last_name} '
                    f'with Stripe account: {stripe_account_id}'
                )
            )
            
        except CustomUser.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Expert with ID {expert_id} not found')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error setting up payments: {str(e)}')
            )
