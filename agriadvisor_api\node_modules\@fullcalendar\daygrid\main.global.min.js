/*!
FullCalendar v5.11.5
Docs & License: https://fullcalendar.io/
(c) 2022 <PERSON>
*/
var FullCalendarDayGrid=function(e,t){"use strict";var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};var i=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.headerElRef=t.createRef(),n}return r(n,e),n.prototype.renderSimpleLayout=function(e,n){var r=this.props,a=this.context,i=[],s=t.getStickyHeaderDates(a.options);return e&&i.push({type:"header",key:"header",isSticky:s,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),i.push({type:"body",key:"body",liquid:!0,chunk:{content:n}}),t.createElement(t.ViewRoot,{viewSpec:a.viewSpec},(function(e,n){return t.createElement("div",{ref:e,className:["fc-daygrid"].concat(n).join(" ")},t.createElement(t.SimpleScrollGrid,{liquid:!r.isHeightAuto&&!r.forPrint,collapsibleWidth:r.forPrint,cols:[],sections:i}))}))},n.prototype.renderHScrollLayout=function(e,n,r,a){var i=this.context.pluginHooks.scrollGridImpl;if(!i)throw new Error("No ScrollGrid implementation");var s=this.props,o=this.context,l=!s.forPrint&&t.getStickyHeaderDates(o.options),c=!s.forPrint&&t.getStickyFooterScrollbar(o.options),d=[];return e&&d.push({type:"header",key:"header",isSticky:l,chunks:[{key:"main",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),d.push({type:"body",key:"body",liquid:!0,chunks:[{key:"main",content:n}]}),c&&d.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"main",content:t.renderScrollShim}]}),t.createElement(t.ViewRoot,{viewSpec:o.viewSpec},(function(e,n){return t.createElement("div",{ref:e,className:["fc-daygrid"].concat(n).join(" ")},t.createElement(i,{liquid:!s.isHeightAuto&&!s.forPrint,collapsibleWidth:s.forPrint,colGroups:[{cols:[{span:r,minWidth:a}]}],sections:d}))}))},n}(t.DateComponent);function s(e,t){for(var n=[],r=0;r<t;r+=1)n[r]=[];for(var a=0,i=e;a<i.length;a++){var s=i[a];n[s.row].push(s)}return n}function o(e,t){for(var n=[],r=0;r<t;r+=1)n[r]=[];for(var a=0,i=e;a<i.length;a++){var s=i[a];n[s.firstCol].push(s)}return n}function l(e,t){var n=[];if(e){for(s=0;s<t;s+=1)n[s]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(var r=0,a=e.segs;r<a.length;r++){var i=a[r];n[i.row].segs.push(i)}}else for(var s=0;s<t;s+=1)n[s]=null;return n}var c=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.render=function(){var e=this.props,n=t.buildNavLinkAttrs(this.context,e.date);return t.createElement(t.DayCellContent,{date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraHookProps:e.extraHookProps,defaultContent:d},(function(r,i){return(i||e.forceDayTop)&&t.createElement("div",{className:"fc-daygrid-day-top",ref:r},t.createElement("a",a({id:e.dayNumberId,className:"fc-daygrid-day-number"},n),i||t.createElement(t.Fragment,null," ")))}))},n}(t.BaseComponent);function d(e){return e.dayNumberText}var u=t.createFormatter({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function f(e){var t=e.eventRange.ui.display;return"list-item"===t||"auto"===t&&!e.eventRange.def.allDay&&e.firstCol===e.lastCol&&e.isStart&&e.isEnd}var g=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.render=function(){var e=this.props;return t.createElement(t.StandardEvent,a({},e,{extraClassNames:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:u,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))},n}(t.BaseComponent),p=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.render=function(){var e=this.props,n=this.context,r=n.options.eventTimeFormat||u,i=t.buildSegTimeText(e.seg,r,n,!0,e.defaultDisplayEventEnd);return t.createElement(t.EventRoot,{seg:e.seg,timeText:i,defaultContent:h,isDragging:e.isDragging,isResizing:!1,isDateSelecting:!1,isSelected:e.isSelected,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},(function(r,i,s,o){return t.createElement("a",a({className:["fc-daygrid-event","fc-daygrid-dot-event"].concat(i).join(" "),ref:r},t.getSegAnchorAttrs(e.seg,n)),o)}))},n}(t.BaseComponent);function h(e){return t.createElement(t.Fragment,null,t.createElement("div",{className:"fc-daygrid-event-dot",style:{borderColor:e.borderColor||e.backgroundColor}}),e.timeText&&t.createElement("div",{className:"fc-event-time"},e.timeText),t.createElement("div",{className:"fc-event-title"},e.event.title||t.createElement(t.Fragment,null," ")))}var v=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.compileSegs=t.memoize(y),n}return r(n,e),n.prototype.render=function(){var e=this.props,n=this.compileSegs(e.singlePlacements),r=n.allSegs,i=n.invisibleSegs;return t.createElement(t.MoreLinkRoot,{dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:r,hiddenSegs:i,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:function(){var n=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return t.createElement(t.Fragment,null,r.map((function(r){var i=r.eventRange.instance.instanceId;return t.createElement("div",{className:"fc-daygrid-event-harness",key:i,style:{visibility:n[i]?"hidden":""}},f(r)?t.createElement(p,a({seg:r,isDragging:!1,isSelected:i===e.eventSelection,defaultDisplayEventEnd:!1},t.getSegMeta(r,e.todayRange))):t.createElement(g,a({seg:r,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:i===e.eventSelection,defaultDisplayEventEnd:!1},t.getSegMeta(r,e.todayRange))))})))}},(function(e,n,r,i,s,o,l,c){return t.createElement("a",a({ref:e,className:["fc-daygrid-more-link"].concat(n).join(" "),title:o,"aria-expanded":l,"aria-controls":c},t.createAriaClickAttrs(s)),i)}))},n}(t.BaseComponent);function y(e){for(var t=[],n=[],r=0,a=e;r<a.length;r++){var i=a[r];t.push(i.seg),i.isVisible||n.push(i.seg)}return{allSegs:t,invisibleSegs:n}}var m=t.createFormatter({week:"narrow"}),R=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.rootElRef=t.createRef(),n.state={dayNumberId:t.getUniqueDomId()},n.handleRootEl=function(e){t.setRef(n.rootElRef,e),t.setRef(n.props.elRef,e)},n}return r(n,e),n.prototype.render=function(){var e=this,n=e.context,r=e.props,i=e.state,s=e.rootElRef,o=r.date,l=r.dateProfile,d=t.buildNavLinkAttrs(n,o,"week");return t.createElement(t.DayCellRoot,{date:o,dateProfile:l,todayRange:r.todayRange,showDayNumber:r.showDayNumber,extraHookProps:r.extraHookProps,elRef:this.handleRootEl},(function(e,n,u,f){return t.createElement("td",a({ref:e,role:"gridcell",className:["fc-daygrid-day"].concat(n,r.extraClassNames||[]).join(" ")},u,r.extraDataAttrs,r.showDayNumber?{"aria-labelledby":i.dayNumberId}:{}),t.createElement("div",{className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",ref:r.innerElRef},r.showWeekNumber&&t.createElement(t.WeekNumberRoot,{date:o,defaultFormat:m},(function(e,n,r,i){return t.createElement("a",a({ref:e,className:["fc-daygrid-week-number"].concat(n).join(" ")},d),i)})),!f&&t.createElement(c,{date:o,dateProfile:l,showDayNumber:r.showDayNumber,dayNumberId:i.dayNumberId,forceDayTop:r.forceDayTop,todayRange:r.todayRange,extraHookProps:r.extraHookProps}),t.createElement("div",{className:"fc-daygrid-day-events",ref:r.fgContentElRef},r.fgContent,t.createElement("div",{className:"fc-daygrid-day-bottom",style:{marginTop:r.moreMarginTop}},t.createElement(v,{allDayDate:o,singlePlacements:r.singlePlacements,moreCnt:r.moreCnt,alignmentElRef:s,alignGridTop:!r.showDayNumber,extraDateSpan:r.extraDateSpan,dateProfile:r.dateProfile,eventSelection:r.eventSelection,eventDrag:r.eventDrag,eventResize:r.eventResize,todayRange:r.todayRange}))),t.createElement("div",{className:"fc-daygrid-day-bg"},r.bgContent)))}))},n}(t.DateComponent);function E(e,t,n,r,a,i,s){var o=new S;o.allowReslicing=!0,o.strictOrder=r,!0===t||!0===n?(o.maxCoord=i,o.hiddenConsumes=!0):"number"==typeof t?o.maxStackCnt=t:"number"==typeof n&&(o.maxStackCnt=n,o.hiddenConsumes=!0);for(var l=[],c=[],d=0;d<e.length;d+=1){var u=a[(P=e[d]).eventRange.instance.instanceId];null!=u?l.push({index:d,thickness:u,span:{start:P.firstCol,end:P.lastCol+1}}):c.push(P)}for(var f=o.addSegs(l),g=function(e,t,n){for(var r=function(e,t){for(var n=[],r=0;r<t;r+=1)n.push([]);for(var a=0,i=e;a<i.length;a++){var s=i[a];for(r=s.span.start;r<s.span.end;r+=1)n[r].push(s)}return n}(e,n.length),a=[],i=[],s=[],o=0;o<n.length;o+=1){for(var l=r[o],c=[],d=0,u=0,f=0,g=l;f<g.length;f++){var p=t[(m=g[f]).index];c.push({seg:b(p,o,o+1,n),isVisible:!0,isAbsolute:!1,absoluteTop:m.levelCoord,marginTop:m.levelCoord-d}),d=m.levelCoord+m.thickness}var h=[];d=0,u=0;for(var v=0,y=l;v<y.length;v++){p=t[(m=y[v]).index];var m,R=m.span.end-m.span.start>1,E=m.span.start===o;u+=m.levelCoord-d,d=m.levelCoord+m.thickness,R?(u+=m.thickness,E&&h.push({seg:b(p,m.span.start,m.span.end,n),isVisible:!0,isAbsolute:!0,absoluteTop:m.levelCoord,marginTop:0})):E&&(h.push({seg:b(p,m.span.start,m.span.end,n),isVisible:!0,isAbsolute:!1,absoluteTop:m.levelCoord,marginTop:u}),u=0)}a.push(c),i.push(h),s.push(u)}return{singleColPlacements:a,multiColPlacements:i,leftoverMargins:s}}(o.toRects(),e,s),p=g.singleColPlacements,h=g.multiColPlacements,v=g.leftoverMargins,y=[],m=[],R=0,E=c;R<E.length;R++){h[(P=E[R]).firstCol].push({seg:P,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(var C=P.firstCol;C<=P.lastCol;C+=1)p[C].push({seg:b(P,C,C+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(C=0;C<s.length;C+=1)y.push(0);for(var D=0,w=f;D<w.length;D++){var x=w[D],P=e[x.index],k=x.span;h[k.start].push({seg:b(P,k.start,k.end,s),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(C=k.start;C<k.end;C+=1)y[C]+=1,p[C].push({seg:b(P,C,C+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(C=0;C<s.length;C+=1)m.push(v[C]);return{singleColPlacements:p,multiColPlacements:h,moreCnts:y,moreMarginTops:m}}function b(e,n,r,i){if(e.firstCol===n&&e.lastCol===r-1)return e;var s=e.eventRange,o=s.range,l=t.intersectRanges(o,{start:i[n].date,end:t.addDays(i[r-1].date,1)});return a(a({},e),{firstCol:n,lastCol:r-1,eventRange:{def:s.def,ui:a(a({},s.ui),{durationEditable:!1}),instance:s.instance,range:l},isStart:e.isStart&&l.start.valueOf()===o.start.valueOf(),isEnd:e.isEnd&&l.end.valueOf()===o.end.valueOf()})}var S=function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t.hiddenConsumes=!1,t.forceHidden={},t}return r(n,e),n.prototype.addSegs=function(n){for(var r=this,a=e.prototype.addSegs.call(this,n),i=this.entriesByLevel,s=function(e){return!r.forceHidden[t.buildEntryKey(e)]},o=0;o<i.length;o+=1)i[o]=i[o].filter(s);return a},n.prototype.handleInvalidInsertion=function(n,r,i){var s=this.entriesByLevel,o=this.forceHidden,l=n.touchingEntry,c=n.touchingLevel,d=n.touchingLateral;if(this.hiddenConsumes&&l){var u=t.buildEntryKey(l);if(!o[u])if(this.allowReslicing){var f=a(a({},l),{span:t.intersectSpans(l.span,r.span)});o[t.buildEntryKey(f)]=!0,s[c][d]=f,this.splitEntry(l,r,i)}else o[u]=!0,i.push(l)}return e.prototype.handleInvalidInsertion.call(this,n,r,i)},n}(t.SegHierarchy),C=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.cellElRefs=new t.RefMap,n.frameElRefs=new t.RefMap,n.fgElRefs=new t.RefMap,n.segHarnessRefs=new t.RefMap,n.rootElRef=t.createRef(),n.state={framePositions:null,maxContentHeight:null,eventInstanceHeights:{}},n}return r(n,e),n.prototype.render=function(){var e=this,n=this,r=n.props,a=n.state,i=n.context.options,s=r.cells.length,l=o(r.businessHourSegs,s),c=o(r.bgEventSegs,s),d=o(this.getHighlightSegs(),s),u=o(this.getMirrorSegs(),s),f=E(t.sortEventSegs(r.fgEventSegs,i.eventOrder),r.dayMaxEvents,r.dayMaxEventRows,i.eventOrderStrict,a.eventInstanceHeights,a.maxContentHeight,r.cells),g=f.singleColPlacements,p=f.multiColPlacements,h=f.moreCnts,v=f.moreMarginTops,y=r.eventDrag&&r.eventDrag.affectedInstances||r.eventResize&&r.eventResize.affectedInstances||{};return t.createElement("tr",{ref:this.rootElRef,role:"row"},r.renderIntro&&r.renderIntro(),r.cells.map((function(n,a){var i=e.renderFgSegs(a,r.forPrint?g[a]:p[a],r.todayRange,y),s=e.renderFgSegs(a,function(e,t){if(!e.length)return[];var n=function(e){for(var t={},n=0,r=e;n<r.length;n++)for(var a=0,i=r[n];a<i.length;a++){var s=i[a];t[s.seg.eventRange.instance.instanceId]=s.absoluteTop}return t}(t);return e.map((function(e){return{seg:e,isVisible:!0,isAbsolute:!0,absoluteTop:n[e.eventRange.instance.instanceId],marginTop:0}}))}(u[a],p),r.todayRange,{},Boolean(r.eventDrag),Boolean(r.eventResize),!1);return t.createElement(R,{key:n.key,elRef:e.cellElRefs.createRef(n.key),innerElRef:e.frameElRefs.createRef(n.key),dateProfile:r.dateProfile,date:n.date,showDayNumber:r.showDayNumbers,showWeekNumber:r.showWeekNumbers&&0===a,forceDayTop:r.showWeekNumbers,todayRange:r.todayRange,eventSelection:r.eventSelection,eventDrag:r.eventDrag,eventResize:r.eventResize,extraHookProps:n.extraHookProps,extraDataAttrs:n.extraDataAttrs,extraClassNames:n.extraClassNames,extraDateSpan:n.extraDateSpan,moreCnt:h[a],moreMarginTop:v[a],singlePlacements:g[a],fgContentElRef:e.fgElRefs.createRef(n.key),fgContent:t.createElement(t.Fragment,null,t.createElement(t.Fragment,null,i),t.createElement(t.Fragment,null,s)),bgContent:t.createElement(t.Fragment,null,e.renderFillSegs(d[a],"highlight"),e.renderFillSegs(l[a],"non-business"),e.renderFillSegs(c[a],"bg-event"))})})))},n.prototype.componentDidMount=function(){this.updateSizing(!0)},n.prototype.componentDidUpdate=function(e,n){var r=this.props;this.updateSizing(!t.isPropsEqual(e,r))},n.prototype.getHighlightSegs=function(){var e=this.props;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs},n.prototype.getMirrorSegs=function(){var e=this.props;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]},n.prototype.renderFgSegs=function(e,n,r,i,s,o,l){var c=this.context,d=this.props.eventSelection,u=this.state.framePositions,h=1===this.props.cells.length,v=s||o||l,y=[];if(u)for(var m=0,R=n;m<R.length;m++){var E=R[m],b=E.seg,S=b.eventRange.instance.instanceId,C=S+":"+e,D=E.isVisible&&!i[S],w=E.isAbsolute,x="",P="";w&&(c.isRtl?(P=0,x=u.lefts[b.lastCol]-u.lefts[b.firstCol]):(x=0,P=u.rights[b.firstCol]-u.rights[b.lastCol])),y.push(t.createElement("div",{className:"fc-daygrid-event-harness"+(w?" fc-daygrid-event-harness-abs":""),key:C,ref:v?null:this.segHarnessRefs.createRef(C),style:{visibility:D?"":"hidden",marginTop:w?"":E.marginTop,top:w?E.absoluteTop:"",left:x,right:P}},f(b)?t.createElement(p,a({seg:b,isDragging:s,isSelected:S===d,defaultDisplayEventEnd:h},t.getSegMeta(b,r))):t.createElement(g,a({seg:b,isDragging:s,isResizing:o,isDateSelecting:l,isSelected:S===d,defaultDisplayEventEnd:h},t.getSegMeta(b,r)))))}return y},n.prototype.renderFillSegs=function(e,n){var r=this.context.isRtl,i=this.props.todayRange,s=this.state.framePositions,o=[];if(s)for(var l=0,c=e;l<c.length;l++){var d=c[l],u=r?{right:0,left:s.lefts[d.lastCol]-s.lefts[d.firstCol]}:{left:0,right:s.rights[d.firstCol]-s.rights[d.lastCol]};o.push(t.createElement("div",{key:t.buildEventRangeKey(d.eventRange),className:"fc-daygrid-bg-harness",style:u},"bg-event"===n?t.createElement(t.BgEvent,a({seg:d},t.getSegMeta(d,i))):t.renderFill(n)))}return t.createElement.apply(void 0,function(e,t,n){if(n||2===arguments.length)for(var r,a=0,i=t.length;a<i;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||t)}([t.Fragment,{}],o))},n.prototype.updateSizing=function(e){var n=this.props,r=this.frameElRefs;if(!n.forPrint&&null!==n.clientWidth){if(e){var i=n.cells.map((function(e){return r.currentMap[e.key]}));if(i.length){var s=this.rootElRef.current;this.setState({framePositions:new t.PositionCache(s,i,!0,!1)})}}var o=this.state.eventInstanceHeights,l=this.queryEventInstanceHeights(),c=!0===n.dayMaxEvents||!0===n.dayMaxEventRows;this.safeSetState({eventInstanceHeights:a(a({},o),l),maxContentHeight:c?this.computeMaxContentHeight():null})}},n.prototype.queryEventInstanceHeights=function(){var e=this.segHarnessRefs.currentMap,t={};for(var n in e){var r=Math.round(e[n].getBoundingClientRect().height),a=n.split(":")[0];t[a]=Math.max(t[a]||0,r)}return t},n.prototype.computeMaxContentHeight=function(){var e=this.props.cells[0].key,t=this.cellElRefs.currentMap[e],n=this.fgElRefs.currentMap[e];return t.getBoundingClientRect().bottom-n.getBoundingClientRect().top},n.prototype.getCellEls=function(){var e=this.cellElRefs.currentMap;return this.props.cells.map((function(t){return e[t.key]}))},n}(t.DateComponent);C.addStateEquality({eventInstanceHeights:t.isPropsEqual});var D=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.splitBusinessHourSegs=t.memoize(s),n.splitBgEventSegs=t.memoize(s),n.splitFgEventSegs=t.memoize(s),n.splitDateSelectionSegs=t.memoize(s),n.splitEventDrag=t.memoize(l),n.splitEventResize=t.memoize(l),n.rowRefs=new t.RefMap,n.handleRootEl=function(e){n.rootEl=e,e?n.context.registerInteractiveComponent(n,{el:e,isHitComboAllowed:n.props.isHitComboAllowed}):n.context.unregisterInteractiveComponent(n)},n}return r(n,e),n.prototype.render=function(){var e=this,n=this.props,r=n.dateProfile,a=n.dayMaxEventRows,i=n.dayMaxEvents,s=n.expandRows,o=n.cells.length,l=this.splitBusinessHourSegs(n.businessHourSegs,o),c=this.splitBgEventSegs(n.bgEventSegs,o),d=this.splitFgEventSegs(n.fgEventSegs,o),u=this.splitDateSelectionSegs(n.dateSelectionSegs,o),f=this.splitEventDrag(n.eventDrag,o),g=this.splitEventResize(n.eventResize,o),p=!0===i||!0===a;p&&!s&&(p=!1,a=null,i=null);var h=["fc-daygrid-body",p?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",s?"":"fc-daygrid-body-natural"];return t.createElement("div",{className:h.join(" "),ref:this.handleRootEl,style:{width:n.clientWidth,minWidth:n.tableMinWidth}},t.createElement(t.NowTimer,{unit:"day"},(function(p,h){return t.createElement(t.Fragment,null,t.createElement("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:n.clientWidth,minWidth:n.tableMinWidth,height:s?n.clientHeight:""}},n.colGroupNode,t.createElement("tbody",{role:"presentation"},n.cells.map((function(s,p){return t.createElement(C,{ref:e.rowRefs.createRef(p),key:s.length?s[0].date.toISOString():p,showDayNumbers:o>1,showWeekNumbers:n.showWeekNumbers,todayRange:h,dateProfile:r,cells:s,renderIntro:n.renderRowIntro,businessHourSegs:l[p],eventSelection:n.eventSelection,bgEventSegs:c[p].filter(w),fgEventSegs:d[p],dateSelectionSegs:u[p],eventDrag:f[p],eventResize:g[p],dayMaxEvents:i,dayMaxEventRows:a,clientWidth:n.clientWidth,clientHeight:n.clientHeight,forPrint:n.forPrint})})))))})))},n.prototype.prepareHits=function(){this.rowPositions=new t.PositionCache(this.rootEl,this.rowRefs.collect().map((function(e){return e.getCellEls()[0]})),!1,!0),this.colPositions=new t.PositionCache(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)},n.prototype.queryHit=function(e,t){var n=this.colPositions,r=this.rowPositions,i=n.leftToIndex(e),s=r.topToIndex(t);if(null!=s&&null!=i){var o=this.props.cells[s][i];return{dateProfile:this.props.dateProfile,dateSpan:a({range:this.getCellRange(s,i),allDay:!0},o.extraDateSpan),dayEl:this.getCellEl(s,i),rect:{left:n.lefts[i],right:n.rights[i],top:r.tops[s],bottom:r.bottoms[s]},layer:0}}return null},n.prototype.getCellEl=function(e,t){return this.rowRefs.currentMap[e].getCellEls()[t]},n.prototype.getCellRange=function(e,n){var r=this.props.cells[e][n].date;return{start:r,end:t.addDays(r,1)}},n}(t.DateComponent);function w(e){return e.eventRange.def.allDay}var x=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.forceDayIfListItem=!0,t}return r(t,e),t.prototype.sliceRange=function(e,t){return t.sliceRange(e)},t}(t.Slicer),P=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.slicer=new x,n.tableRef=t.createRef(),n}return r(n,e),n.prototype.render=function(){var e=this.props,n=this.context;return t.createElement(D,a({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,n,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))},n}(t.DateComponent),k=function(e){function n(){var n=null!==e&&e.apply(this,arguments)||this;return n.buildDayTableModel=t.memoize(M),n.headerRef=t.createRef(),n.tableRef=t.createRef(),n}return r(n,e),n.prototype.render=function(){var e=this,n=this.context,r=n.options,a=n.dateProfileGenerator,i=this.props,s=this.buildDayTableModel(i.dateProfile,a),o=r.dayHeaders&&t.createElement(t.DayHeader,{ref:this.headerRef,dateProfile:i.dateProfile,dates:s.headerDates,datesRepDistinctDays:1===s.rowCnt}),l=function(n){return t.createElement(P,{ref:e.tableRef,dateProfile:i.dateProfile,dayTableModel:s,businessHours:i.businessHours,dateSelection:i.dateSelection,eventStore:i.eventStore,eventUiBases:i.eventUiBases,eventSelection:i.eventSelection,eventDrag:i.eventDrag,eventResize:i.eventResize,nextDayThreshold:r.nextDayThreshold,colGroupNode:n.tableColGroupNode,tableMinWidth:n.tableMinWidth,dayMaxEvents:r.dayMaxEvents,dayMaxEventRows:r.dayMaxEventRows,showWeekNumbers:r.weekNumbers,expandRows:!i.isHeightAuto,headerAlignElRef:e.headerElRef,clientWidth:n.clientWidth,clientHeight:n.clientHeight,forPrint:i.forPrint})};return r.dayMinWidth?this.renderHScrollLayout(o,l,s.colCnt,r.dayMinWidth):this.renderSimpleLayout(o,l)},n}(i);function M(e,n){var r=new t.DaySeriesModel(e.renderRange,n);return new t.DayTableModel(r,/year|month|week/.test(e.currentRangeUnit))}var N=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.buildRenderRange=function(n,r,a){var i,s=this.props.dateEnv,o=e.prototype.buildRenderRange.call(this,n,r,a),l=o.start,c=o.end;if(/^(year|month)$/.test(r)&&(l=s.startOfWeek(l),(i=s.startOfWeek(c)).valueOf()!==c.valueOf()&&(c=t.addWeeks(i,1))),this.props.monthMode&&this.props.fixedWeekCount){var d=Math.ceil(t.diffWeeks(l,c));c=t.addWeeks(c,6-d)}return{start:l,end:c}},n}(t.DateProfileGenerator),T=t.createPlugin({initialView:"dayGridMonth",views:{dayGrid:{component:k,dateProfileGeneratorClass:N},dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},monthMode:!0,fixedWeekCount:!0}}});return t.globalPlugins.push(T),e.DayGridView=k,e.DayTable=P,e.DayTableSlicer=x,e.Table=D,e.TableView=i,e.buildDayTableModel=M,e.default=T,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar);