// src/components/PaymentModal.jsx

import React from "react";
import { Modal, Row, Col } from "react-bootstrap";
import CheckoutForm from "./CheckoutForm"; // We are importing the real Stripe form

/**
 * A modal component that acts as a container for the Stripe payment process.
 * It displays a summary of the booking and renders the secure CheckoutForm.
 *
 * @param {object} props
 * @param {boolean} props.show - Controls if the modal is visible.
 * @param {function} props.onHide - Function to call when the modal should be closed.
 * @param {string} props.clientSecret - The secret from Stripe's PaymentIntent, required for payment.
 * @param {object} props.bookingDetails - The details of the booking to be paid for.
 * @param {function} props.onPaymentSuccess - Callback function to run after a successful payment.
 */
const PaymentModal = ({ show, onHide, clientSecret, bookingDetails, onPaymentSuccess }) => {
  // If the modal is asked to show but we don't have the necessary details, render nothing.
  if (!bookingDetails || !clientSecret) {
    return null;
  }

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Payment for {bookingDetails.serviceName}</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {/* Section 1: A summary of what the user is paying for */}
        <div className="mb-4">
          <Row>
            <Col>
              <strong>Service:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.serviceName}</Col>
          </Row>
          <Row>
            <Col>
              <strong>Date:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.date}</Col>
          </Row>
          <Row>
            <Col>
              <strong>Time:</strong>
            </Col>
            <Col className="text-end">{bookingDetails.time}</Col>
          </Row>
          <hr />
          <Row className="fw-bold fs-5">
            <Col>Total Amount:</Col>
            <Col className="text-end text-success">${bookingDetails.amount.toFixed(2)}</Col>
          </Row>
        </div>

        {/* Section 2: The actual, secure Stripe payment form */}
        {/* We pass the necessary props down to the CheckoutForm component */}
        <CheckoutForm clientSecret={clientSecret} onPaymentSuccess={onPaymentSuccess} amount={bookingDetails.amount} />
      </Modal.Body>
    </Modal>
  );
};

export default PaymentModal;



