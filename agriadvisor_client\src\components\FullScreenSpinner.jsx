// src/components/FullScreenSpinner.jsx

import React from "react";
import { Spinner, Container } from "react-bootstrap";

const FullScreenSpinner = () => {
  return (
    <Container
      fluid
      className="d-flex justify-content-center align-items-center"
      style={{
        height: "100vh",
        backgroundColor: "#f8f9fa",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        transition: "opacity 0.2s ease-in-out"
      }}
    >
      <div className="text-center">
        <Spinner animation="border" role="status" variant="primary" style={{ width: "3rem", height: "3rem" }}>
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <div className="mt-3 text-muted">Loading...</div>
      </div>
    </Container>
  );
};

export default FullScreenSpinner;


