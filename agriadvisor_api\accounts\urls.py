# accounts/urls.py

from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    RegisterUserView, MyTokenObtainPairView, ForgotPasswordView, ResetPasswordView, MeView,
    ExpertViewSet, FarmerViewSet, FarmerExpertViewSet
)

router = DefaultRouter()
router.register(r'experts', ExpertViewSet, basename='expert')
router.register(r'farmers', FarmerViewSet, basename='farmer')
router.register(r'farmer/experts', FarmerExpertViewSet, basename='farmer-expert')

urlpatterns = [
    # Auth URLs
    path('register/', RegisterUserView.as_view(), name='register'),
    path('token/', MyTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('password-reset/', ForgotPasswordView.as_view(), name='password-reset'),
    path('password-reset/confirm/', ResetPasswordView.as_view(), name='password-reset-confirm'),
    path('me/', MeView.as_view(), name='me'),

    # Router URLs for ViewSets
    path('', include(router.urls)),
]
