# payments/views.py

import stripe
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsFarmerUser
from advisory.models import Service
from accounts.models import CustomUser

stripe.api_key = settings.STRIPE_SECRET_KEY

class CreatePaymentIntentView(APIView):
    permission_classes = [IsAuthenticated, IsFarmerUser]

    def post(self, request, *args, **kwargs):
        user = request.user
        service_id = request.data.get('service_id')
        expert_id = request.data.get('expert_id')

        try:
            service = Service.objects.get(id=service_id, tenant=user.tenant)
            expert = CustomUser.objects.get(id=expert_id, tenant=user.tenant, role='expert')
            
            # Ensure the expert has a Stripe account connected
            if not expert.expert_profile or not expert.expert_profile.stripe_account_id:
                return Response(
                    {"error": "This expert is not set up to receive payments."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Convert price from dollars to cents for Stripe
            amount_in_cents = int(service.price * 100)
            
            # Calculate the application fee (e.g., 20% platform fee)
            application_fee = int(amount_in_cents * 0.20)

            payment_intent = stripe.PaymentIntent.create(
                amount=amount_in_cents,
                currency=service.currency.lower(),
                application_fee_amount=application_fee,
                transfer_data={
                    'destination': expert.expert_profile.stripe_account_id,
                },
                # Add metadata to link the payment to our app's data
                metadata={
                    'service_id': service.id,
                    'expert_id': expert.id,
                    'farmer_id': user.id,
                    'tenant_id': user.tenant.id,
                }
            )

            return Response({
                'clientSecret': payment_intent.client_secret,
            })

        except Service.DoesNotExist:
            return Response({"error": "Service not found."}, status=status.HTTP_404_NOT_FOUND)
        except CustomUser.DoesNotExist:
            return Response({"error": "Expert not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



