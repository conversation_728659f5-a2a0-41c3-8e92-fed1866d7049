// src/components/ReviewModal.jsx

import React, { useState } from "react";
import { Modal, Button, Form } from "react-bootstrap";

// A simple star rating component
const StarRating = ({ rating, setRating }) => {
  return (
    <div>
      {[1, 2, 3, 4, 5].map((star) => (
        <span
          key={star}
          className="fs-2"
          style={{ cursor: "pointer", color: star <= rating ? "gold" : "gray" }}
          onClick={() => setRating(star)}
        >
          ★
        </span>
      ))}
    </div>
  );
};

const ReviewModal = ({ show, onHide, onSubmit }) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");

  const handleSubmit = () => {
    if (rating > 0) {
      onSubmit({ rating, comment });
      setRating(0); // Reset for next time
      setComment("");
    } else {
      alert("Please select a star rating.");
    }
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Leave a Review</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group className="mb-3 text-center">
            <Form.Label>Your Rating</Form.Label>
            <StarRating rating={rating} setRating={setRating} />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Comments (Optional)</Form.Label>
            <Form.Control as="textarea" rows={3} value={comment} onChange={(e) => setComment(e.target.value)} />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSubmit}>
          Submit Review
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ReviewModal;


