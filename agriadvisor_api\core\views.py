# core/views.py

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .permissions import IsAdminUser

class BaseTenantViewSet(viewsets.ModelViewSet):
    """
    This is the cornerstone of our multi-tenant security model.
    
    - It automatically filters all querysets to the user's tenant.
    - It automatically assigns the user's tenant to new objects on creation.
    - It enforces that only authenticated admins of a tenant can access the data.
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """
        This method is overridden to filter the queryset for the logged-in user's tenant.
        The actual base queryset must be defined in the subclass that inherits from this.
        """
        user = self.request.user
        # THIS IS THE FIX: We use self.queryset, which is defined in the subclass
        # (e.g., Service.objects.all()), and filter it.
        return self.queryset.filter(tenant=user.tenant)

    def perform_create(self, serializer):
        """
        This method is overridden to automatically assign the user's tenant
        when a new object is created via the API.
        """
        serializer.save(tenant=self.request.user.tenant)
        
        
        