// src/pages/FarmerDashboardPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, ListGroup, Badge, ProgressBar } from "react-bootstrap";
import { Link } from "react-router-dom";
import useAuth from "../hooks/useAuth";
import { getFarmerDashboardData } from "../services/dashboardService";

// Premium Farmer Dashboard CSS and Animations
const premiumCSS = `
  @keyframes farmerSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes appointmentPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 10px 30px rgba(67, 233, 123, 0.3);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 15px 40px rgba(67, 233, 123, 0.5);
    }
  }

  @keyframes activityFlow {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes welcomeGlow {
    0%, 100% {
      text-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
      text-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
  }

  .premium-farmer-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: farmerSlideIn 0.6s ease-out;
  }

  .premium-farmer-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .appointment-card {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    animation: appointmentPulse 3s infinite;
  }

  .activity-item {
    border: none;
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.05) 0%, rgba(56, 249, 215, 0.05) 100%);
    transition: all 0.3s ease;
    animation: activityFlow 0.6s ease-out;
  }

  .activity-item:hover {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
    transform: translateX(10px);
  }

  .quick-action-btn {
    border-radius: 15px;
    padding: 15px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    margin-bottom: 10px;
  }

  .quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  }

  .welcome-text {
    animation: welcomeGlow 3s infinite;
  }

  .slide-in-up {
    animation: farmerSlideIn 0.6s ease-out;
  }

  .farmer-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }

  .service-progress {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
  }

  .upcoming-highlight {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(168, 237, 234, 0.3);
  }

  .no-appointment-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    text-align: center;
    padding: 40px;
    border-radius: 20px;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  appointmentCard: {
    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    border: 'none'
  },
  activityCard: {
    background: 'linear-gradient(135deg, #e3ffe7 0%, #d9e7ff 100%)',
    border: 'none'
  },
  quickActionsCard: {
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    border: 'none'
  },
  gradientHeader: {
    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  },
  activityHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  },
  quickActionsHeader: {
    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  }
};

const FarmerDashboardPage = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Temporary: Return a simple component to test if routing works
  return (
    <Container className="mt-4">
      <h1>Farmer Dashboard</h1>
      <p>Welcome, {user?.username || 'Farmer'}!</p>
      <p>Dashboard is loading...</p>
    </Container>
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await getFarmerDashboardData();
        setDashboardData(data);
      } catch (err) {
        console.error("Farmer dashboard error:", err);
        // Set default data to prevent blank screen
        setDashboardData({
          upcoming_bookings: [],
          recent_bookings: [],
          total_bookings: 0,
          pending_bookings: 0
        });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) {
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          ...premiumStyles.appointmentCard,
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(67, 233, 123, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h3 className="mt-3 mb-2">Loading Your Dashboard</h3>
          <p className="mb-0 opacity-75">Preparing your farming insights and appointments...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <div className="premium-farmer-card p-4">
          <Alert variant="danger" className="border-0 rounded-4">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Dashboard Unavailable
            </Alert.Heading>
            {error}
            <hr />
            <div className="d-flex justify-content-end">
              <Button variant="outline-danger" onClick={() => window.location.reload()}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                Try Again
              </Button>
            </div>
          </Alert>
        </div>
      </Container>
    );
  }

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2 welcome-text">
                <i className="bi bi-house-heart me-3"></i>
                Welcome back, {user?.first_name}!
              </h1>
              <p className="lead mb-0 opacity-90">
                Your farming journey continues here. Manage appointments and grow your knowledge.
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    <i className="bi bi-person-check me-2"></i>
                    Farmer
                  </Badge>
                </div>
                <small className="opacity-75">Account Type</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Dashboard Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="farmer-stats">
              <Row className="text-center">
                <Col md={3}>
                  <div className="h2 mb-1">
                    <i className="bi bi-calendar-check-fill"></i>
                  </div>
                  <h5 className="mb-1">
                    {dashboardData?.recent_activity?.length || 0}
                  </h5>
                  <small className="opacity-75">Completed Sessions</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1">
                    <i className="bi bi-clock-fill"></i>
                  </div>
                  <h5 className="mb-1">
                    {dashboardData?.next_booking ? 'Scheduled' : 'Available'}
                  </h5>
                  <small className="opacity-75">Next Appointment</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1">
                    <i className="bi bi-graph-up-arrow"></i>
                  </div>
                  <h5 className="mb-1">Growing</h5>
                  <small className="opacity-75">Farm Progress</small>
                </Col>
                <Col md={3}>
                  <div className="h2 mb-1">
                    <i className="bi bi-star-fill"></i>
                  </div>
                  <h5 className="mb-1">Premium</h5>
                  <small className="opacity-75">Member Status</small>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        <Row>
          {/* Premium Main Content */}
          <Col lg={8} className="mb-4">
            <PremiumUpcomingBooking booking={dashboardData?.next_booking} />
            <PremiumRecentActivity activities={dashboardData?.recent_activity} />
          </Col>

          {/* Premium Sidebar */}
          <Col lg={4} className="mb-4">
            <PremiumQuickActions />

            {/* Premium Progress Section */}
            <Card className="premium-farmer-card slide-in-up mt-4" style={premiumStyles.activityCard}>
              <div style={premiumStyles.activityHeader}>
                <h5 className="mb-0">
                  <i className="bi bi-trophy me-2"></i>
                  Your Progress
                </h5>
              </div>
              <Card.Body className="p-4">
                <div className="service-progress">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span className="fw-semibold" style={{ color: '#333' }}>Learning Journey</span>
                    <Badge bg="success" className="px-2 py-1" style={{ borderRadius: '10px' }}>
                      75%
                    </Badge>
                  </div>
                  <ProgressBar
                    now={75}
                    style={{ height: '8px', borderRadius: '10px' }}
                    className="mb-3"
                  />
                  <small className="text-muted">
                    <i className="bi bi-lightbulb me-1"></i>
                    Keep booking sessions to unlock new farming techniques!
                  </small>
                </div>

                <div className="text-center mt-3">
                  <Button
                    variant="outline-primary"
                    size="sm"
                    as={Link}
                    to="/browse-services"
                    className="rounded-pill px-3"
                  >
                    <i className="bi bi-plus-circle me-2"></i>
                    Continue Learning
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Action Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Quick book a service"
          as={Link}
          to="/browse-services"
        >
          <i className="bi bi-plus-lg"></i>
        </Button>
      </div>
    </div>
  );
};

// --- Premium Child Components ---

const PremiumUpcomingBooking = ({ booking }) => (
  <Card className="premium-farmer-card mb-4 slide-in-up" style={premiumStyles.appointmentCard}>
    <div style={premiumStyles.gradientHeader}>
      <h4 className="mb-0">
        <i className="bi bi-calendar-event me-2"></i>
        Your Next Appointment
      </h4>
    </div>
    <Card.Body className="p-4">
      {booking ? (
        <div className="upcoming-highlight">
          <Row className="align-items-center">
            <Col md={8}>
              <h3 className="mb-2" style={{ color: '#333' }}>
                <i className="bi bi-briefcase me-2 text-primary"></i>
                {booking.service.name}
              </h3>
              <div className="mb-3">
                <div className="d-flex align-items-center mb-2">
                  <div
                    className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3"
                    style={{ width: '40px', height: '40px', fontSize: '1rem' }}
                  >
                    {booking.expert.first_name[0]}{booking.expert.last_name[0]}
                  </div>
                  <div>
                    <div className="fw-semibold" style={{ color: '#333' }}>
                      {booking.expert.first_name} {booking.expert.last_name}
                    </div>
                    <small className="text-muted">Agricultural Expert</small>
                  </div>
                </div>
                <div className="d-flex align-items-center text-muted">
                  <i className="bi bi-clock me-2"></i>
                  <span>{new Date(booking.booking_time).toLocaleString()}</span>
                </div>
              </div>
              <div className="d-flex gap-2">
                <Button
                  variant="primary"
                  as={Link}
                  to={`/live-session/${booking.id}`}
                  className="quick-action-btn"
                  style={{
                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    border: 'none'
                  }}
                >
                  <i className="bi bi-camera-video me-2"></i>
                  Join Live Session
                </Button>
                <Button
                  variant="outline-info"
                  className="quick-action-btn"
                  style={{ borderWidth: '2px' }}
                >
                  <i className="bi bi-info-circle me-2"></i>
                  View Details
                </Button>
              </div>
            </Col>
            <Col md={4} className="text-center">
              <div className="h1 mb-2">
                <i className="bi bi-calendar-check-fill text-success"></i>
              </div>
              <Badge bg="success" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                <i className="bi bi-check-circle me-2"></i>
                Confirmed
              </Badge>
            </Col>
          </Row>
        </div>
      ) : (
        <div className="no-appointment-card">
          <div className="mb-3">
            <i className="bi bi-calendar-x" style={{ fontSize: '3rem' }}></i>
          </div>
          <h4 className="mb-3">No Upcoming Appointments</h4>
          <p className="mb-4 opacity-90">
            Ready to learn something new? Book a session with our agricultural experts.
          </p>
          <Button
            variant="light"
            size="lg"
            as={Link}
            to="/browse-services"
            className="quick-action-btn"
            style={{ color: '#333', fontWeight: 'bold' }}
          >
            <i className="bi bi-plus-circle me-2"></i>
            Book Your First Service
          </Button>
        </div>
      )}
    </Card.Body>
  </Card>
);

const PremiumRecentActivity = ({ activities }) => (
  <Card className="premium-farmer-card slide-in-up" style={premiumStyles.activityCard}>
    <div style={premiumStyles.activityHeader}>
      <h4 className="mb-0">
        <i className="bi bi-activity me-2"></i>
        Recent Activity
      </h4>
    </div>
    <Card.Body className="p-4">
      {activities && activities.length > 0 ? (
        <div className="activity-list">
          {activities.map((booking) => (
            <div key={booking.id} className="activity-item">
              <Row className="align-items-center">
                <Col xs={2}>
                  <div
                    className="rounded-circle bg-success text-white d-flex align-items-center justify-content-center"
                    style={{ width: '40px', height: '40px' }}
                  >
                    <i className="bi bi-check-lg"></i>
                  </div>
                </Col>
                <Col xs={8}>
                  <div className="fw-semibold text-dark mb-1">
                    Session Completed
                  </div>
                  <div className="text-muted small mb-1">
                    <strong>{booking.service.name}</strong>
                  </div>
                  <div className="text-muted small">
                    <i className="bi bi-calendar me-1"></i>
                    {new Date(booking.booking_time).toLocaleDateString()}
                  </div>
                </Col>
                <Col xs={2} className="text-end">
                  {!booking.review ? (
                    <Button
                      variant="outline-warning"
                      size="sm"
                      as={Link}
                      to="/my-bookings"
                      className="rounded-pill"
                      title="Leave a review"
                    >
                      <i className="bi bi-star"></i>
                    </Button>
                  ) : (
                    <Badge bg="success" className="px-2 py-1" style={{ borderRadius: '10px' }}>
                      <i className="bi bi-check-circle"></i>
                    </Badge>
                  )}
                </Col>
              </Row>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-5">
          <div className="mb-3">
            <i className="bi bi-calendar-x text-muted" style={{ fontSize: '3rem' }}></i>
          </div>
          <h5 className="text-muted">No recent activity</h5>
          <p className="text-muted mb-4">
            Your completed sessions will appear here
          </p>
          <Button
            variant="outline-primary"
            as={Link}
            to="/browse-services"
            className="rounded-pill px-4"
          >
            <i className="bi bi-plus-circle me-2"></i>
            Start Your Journey
          </Button>
        </div>
      )}
    </Card.Body>
  </Card>
);

const PremiumQuickActions = () => (
  <Card className="premium-farmer-card slide-in-up" style={premiumStyles.quickActionsCard}>
    <div style={premiumStyles.quickActionsHeader}>
      <h5 className="mb-0">
        <i className="bi bi-lightning-charge-fill me-2"></i>
        Quick Actions
      </h5>
    </div>
    <Card.Body className="p-4">
      <div className="d-grid gap-3">
        <Button
          variant="primary"
          as={Link}
          to="/browse-services"
          className="quick-action-btn"
          style={{
            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            border: 'none'
          }}
        >
          <i className="bi bi-plus-circle me-2"></i>
          Book New Service
        </Button>
        <Button
          variant="outline-primary"
          as={Link}
          to="/my-bookings"
          className="quick-action-btn"
          style={{ borderWidth: '2px' }}
        >
          <i className="bi bi-calendar-check me-2"></i>
          My Bookings
        </Button>
        <Button
          variant="outline-info"
          as={Link}
          to="/farmer/profile"
          className="quick-action-btn"
          style={{ borderWidth: '2px' }}
        >
          <i className="bi bi-person-gear me-2"></i>
          Manage Profile
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="mt-4 p-3 rounded-4" style={{
        background: 'linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%)'
      }}>
        <h6 className="mb-2" style={{ color: '#333' }}>
          <i className="bi bi-info-circle me-2"></i>
          Quick Tip
        </h6>
        <p className="small text-muted mb-0">
          Regular consultations help improve crop yields by up to 30%. Book your next session today!
        </p>
      </div>
    </Card.Body>
  </Card>
);

export default FarmerDashboardPage;