# reviews/views.py

from rest_framework import viewsets, permissions, serializers
from .models import Review
from .serializers import ReviewSerializer
from advisory.models import Booking

class ReviewViewSet(viewsets.ModelViewSet):
    """
    An endpoint for managing reviews.
    - Farmers can CREATE reviews for their own completed bookings.
    - Anyone can LIST reviews for a specific expert.
    """
    queryset = Review.objects.all().order_by('-created_at')
    serializer_class = ReviewSerializer
    # Allow anyone to read reviews, but only authenticated users to create them.
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        # Allow filtering reviews by expert, e.g., /api/reviews/?expert_id=1
        queryset = super().get_queryset()
        expert_id = self.request.query_params.get('expert_id')
        if expert_id:
            queryset = queryset.filter(expert_id=expert_id)
        return queryset

    def perform_create(self, serializer):
        farmer = self.request.user
        booking_id = self.request.data.get('booking')
        
        try:
            # Security Check: Ensure the booking exists, belongs to this farmer, and is completed.
            booking = Booking.objects.get(id=booking_id, farmer=farmer, status='completed')
        except Booking.DoesNotExist:
            # This will raise a DRF validation error, which is what we want.
            raise serializers.ValidationError({
                "booking": "You can only review your own completed bookings."
            })
        
        # Check if a review already exists for this booking
        if Review.objects.filter(booking=booking).exists():
            raise serializers.ValidationError({
                "booking": "A review for this booking already exists."
            })

        # Save the review, setting the farmer and expert from the verified booking
        serializer.save(farmer=farmer, expert=booking.expert)
        
        
        