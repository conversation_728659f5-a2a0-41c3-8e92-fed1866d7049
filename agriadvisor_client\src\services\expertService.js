// src/services/expertService.js

import api from "./api";

/**
 * Fetches all experts for the current tenant.
 */
export const getExperts = async () => {
  try {
    // ---->  FIX THIS LINE <----
    const response = await api.get("/accounts/experts/");
    return response.data;
  } catch (error) {
    console.error("Error fetching experts:", error);
    throw error;
  }
};

/**
 * Creates a new expert.
 */
export const createExpert = async (expertData) => {
  try {
    // ---->  FIX THIS LINE <----
    const response = await api.post("/accounts/experts/", expertData);
    return response.data;
  } catch (error) {
    console.error("Error creating expert:", error);
    throw error;
  }
};

/**
 * Updates an existing expert.
 */
export const updateExpert = async (id, expertData) => {
  try {
    // ---->  FIX THIS LINE <----
    const response = await api.put(`/accounts/experts/${id}/`, expertData);
    return response.data;
  } catch (error) {
    console.error(`Error updating expert ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes an expert.
 */
export const deleteExpert = async (id) => {
  try {
    // ---->  FIX THIS LINE <----
    await api.delete(`/accounts/experts/${id}/`);
  } catch (error) {
    console.error(`Error deleting expert ${id}:`, error);
    throw error;
  }
};


/**
 * Fetches all available experts for a logged-in farmer.
 */
export const getFarmerExperts = async () => {
  try {
    const response = await api.get('/accounts/farmer/experts/');
    return response.data;
  } catch (error) {
    console.error("Error fetching farmer experts:", error);
    throw error;
  }
};



