# accounts/admin.py

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, Tenant, ExpertProfile, FarmerProfile, FarmingInterest

# This is the standard and recommended way to extend the UserAdmin
class CustomUserAdmin(UserAdmin):
    # This adds the custom fields to the EDIT page of a user
    fieldsets = UserAdmin.fieldsets + (
        ('Custom Company Info', {'fields': ('role', 'tenant', 'phone_number')}),
        ('Onboarding', {'fields': ('has_completed_onboarding',)}),
    )
    
    # This adds the custom fields to the ADD page for a new user
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Custom Company Info', {'fields': ('role', 'tenant', 'phone_number')}),
    )
    
    # This adds 'role' and 'tenant' to the main user list display for clarity
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'role', 'tenant')
    list_filter = UserAdmin.list_filter + ('role', 'tenant')

# This class configures the admin for the ExpertProfile model
class ExpertProfileAdmin(admin.ModelAdmin):
    # This is the key to making the Stripe field editable
    fields = ('user', 'specialty', 'bio', 'stripe_account_id', 'payouts_enabled')
    list_display = ('user', 'specialty', 'payment_status', 'stripe_account_id', 'payouts_enabled')
    list_filter = ('payouts_enabled', 'specialty')
    search_fields = ('user__first_name', 'user__last_name', 'user__email', 'specialty')

    def payment_status(self, obj):
        """Display payment status in a user-friendly way"""
        if obj.stripe_account_id and obj.stripe_account_id.strip():
            return "✓ Enabled"
        return "✗ Not Set Up"
    payment_status.short_description = 'Payment Status'

# Now, we register our models cleanly.
# Django is smart enough to use our CustomUserAdmin for the CustomUser model.
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(Tenant)
admin.site.register(ExpertProfile, ExpertProfileAdmin)
admin.site.register(FarmerProfile)
admin.site.register(FarmingInterest)


