// src/pages/ProfilePage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, But<PERSON>, Alert, <PERSON>, <PERSON>, <PERSON>, <PERSON>ge, InputGroup } from "react-bootstrap";
import { getMyProfile, changePassword } from "../services/authService";
import useAuth from "../hooks/useAuth";

// Premium Profile CSS and Animations
const premiumCSS = `
  @keyframes profileSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes avatarPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
  }

  @keyframes formFocus {
    from {
      box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    to {
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }
  }

  .premium-profile-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: profileSlideIn 0.6s ease-out;
  }

  .premium-profile-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
    animation: avatarPulse 3s infinite;
    border: 5px solid white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  }

  .premium-form-control {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 12px 20px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.9);
  }

  .premium-form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
    animation: formFocus 0.3s ease;
  }

  .premium-btn {
    border-radius: 15px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
  }

  .premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  }

  .premium-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .premium-btn:hover::before {
    left: 100%;
  }

  .slide-in-up {
    animation: profileSlideIn 0.6s ease-out;
  }

  .info-item {
    padding: 15px 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color 0.2s ease;
  }

  .info-item:hover {
    background-color: rgba(102, 126, 234, 0.02);
    border-radius: 10px;
    margin: 0 -15px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .info-item:last-child {
    border-bottom: none;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  },
  profileCard: {
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    border: 'none'
  },
  passwordCard: {
    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    border: 'none'
  },
  gradientHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  },
  passwordHeader: {
    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  }
};

const ProfilePage = () => {
  const { user } = useAuth(); // Get basic user info from context
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  // State for the change password form
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [passwordMessage, setPasswordMessage] = useState("");
  const [passwordError, setPasswordError] = useState("");

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data = await getMyProfile();
        setProfile(data);
      } catch (error) {
        console.error("Failed to fetch profile", error);
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  const handleChangePassword = async (e) => {
    e.preventDefault();
    setPasswordMessage("");
    setPasswordError("");

    try {
      await changePassword({ old_password: oldPassword, new_password: newPassword });
      setPasswordMessage("Password updated successfully!");
      setOldPassword("");
      setNewPassword("");
    } catch (err) {
      setPasswordError(err.response?.data?.old_password || "Failed to update password.");
    }
  };

  if (loading)
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          ...premiumStyles.profileCard,
          maxWidth: '400px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(252, 182, 159, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: '#667eea' }} />
          <h4 className="mt-3 mb-2" style={{ color: '#333' }}>Loading Your Profile</h4>
          <p className="mb-0" style={{ color: '#666' }}>Preparing your personal information...</p>
        </div>
      </Container>
    );

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-person-circle me-3"></i>
                My Profile
              </h1>
              <p className="lead mb-0 opacity-90">
                Manage your personal information and account settings
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h4 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {profile?.role?.charAt(0).toUpperCase() + profile?.role?.slice(1)}
                  </Badge>
                </div>
                <small className="opacity-75">Account Type</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        <Row>
          {/* Premium Profile Details Card */}
          <Col lg={6} className="mb-4">
            <Card className="premium-profile-card h-100" style={premiumStyles.profileCard}>
              <div style={premiumStyles.gradientHeader}>
                <h4 className="mb-0">
                  <i className="bi bi-person-badge me-2"></i>
                  Profile Information
                </h4>
              </div>
              <Card.Body className="p-4">
                {/* Profile Avatar */}
                <div className="text-center mb-4">
                  <div className="profile-avatar">
                    {profile?.first_name?.[0]}{profile?.last_name?.[0]}
                  </div>
                  <h3 className="mb-1" style={{ color: '#333' }}>
                    {profile?.first_name} {profile?.last_name}
                  </h3>
                  <p className="text-muted mb-0">@{profile?.username}</p>
                </div>

                {/* Profile Information */}
                <div className="profile-info">
                  <div className="info-item">
                    <Row className="align-items-center">
                      <Col xs={2}>
                        <i className="bi bi-envelope-fill text-primary" style={{ fontSize: '1.2rem' }}></i>
                      </Col>
                      <Col xs={3}>
                        <strong style={{ color: '#333' }}>Email</strong>
                      </Col>
                      <Col xs={7}>
                        <span style={{ color: '#666' }}>{profile?.email}</span>
                      </Col>
                    </Row>
                  </div>

                  <div className="info-item">
                    <Row className="align-items-center">
                      <Col xs={2}>
                        <i className="bi bi-person-fill text-success" style={{ fontSize: '1.2rem' }}></i>
                      </Col>
                      <Col xs={3}>
                        <strong style={{ color: '#333' }}>Username</strong>
                      </Col>
                      <Col xs={7}>
                        <span style={{ color: '#666' }}>{profile?.username}</span>
                      </Col>
                    </Row>
                  </div>

                  <div className="info-item">
                    <Row className="align-items-center">
                      <Col xs={2}>
                        <i className="bi bi-shield-fill text-warning" style={{ fontSize: '1.2rem' }}></i>
                      </Col>
                      <Col xs={3}>
                        <strong style={{ color: '#333' }}>Role</strong>
                      </Col>
                      <Col xs={7}>
                        <Badge
                          bg="primary"
                          className="px-3 py-1"
                          style={{ borderRadius: '15px', fontSize: '0.9rem' }}
                        >
                          {profile?.role?.charAt(0).toUpperCase() + profile?.role?.slice(1)}
                        </Badge>
                      </Col>
                    </Row>
                  </div>

                  {user.role === "expert" && (
                    <div className="info-item">
                      <Row className="align-items-center">
                        <Col xs={2}>
                          <i className="bi bi-star-fill text-info" style={{ fontSize: '1.2rem' }}></i>
                        </Col>
                        <Col xs={3}>
                          <strong style={{ color: '#333' }}>Specialty</strong>
                        </Col>
                        <Col xs={7}>
                          <Badge
                            bg="info"
                            className="px-3 py-1"
                            style={{ borderRadius: '15px', fontSize: '0.9rem' }}
                          >
                            {profile?.profile?.specialty || 'Not specified'}
                          </Badge>
                        </Col>
                      </Row>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="text-center mt-4">
                  <Button
                    variant="outline-primary"
                    className="premium-btn me-2"
                    style={{ borderWidth: '2px' }}
                  >
                    <i className="bi bi-pencil-square me-2"></i>
                    Edit Profile
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
          {/* Premium Password Change Card */}
          <Col lg={6} className="mb-4">
            <Card className="premium-profile-card h-100" style={premiumStyles.passwordCard}>
              <div style={premiumStyles.passwordHeader}>
                <h4 className="mb-0">
                  <i className="bi bi-shield-lock me-2"></i>
                  Security Settings
                </h4>
              </div>
              <Card.Body className="p-4">
                <div className="text-center mb-4">
                  <div
                    className="mx-auto mb-3"
                    style={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '2rem',
                      boxShadow: '0 10px 25px rgba(250, 112, 154, 0.3)'
                    }}
                  >
                    <i className="bi bi-key-fill"></i>
                  </div>
                  <h5 style={{ color: '#333' }}>Change Your Password</h5>
                  <p className="text-muted small mb-0">
                    Keep your account secure with a strong password
                  </p>
                </div>

                {/* Success/Error Messages */}
                {passwordMessage && (
                  <Alert
                    variant="success"
                    className="border-0 rounded-4 slide-in-up"
                    style={{
                      background: 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)',
                      color: '#155724'
                    }}
                  >
                    <i className="bi bi-check-circle-fill me-2"></i>
                    {passwordMessage}
                  </Alert>
                )}

                {passwordError && (
                  <Alert
                    variant="danger"
                    className="border-0 rounded-4 slide-in-up"
                    style={{
                      background: 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)',
                      color: '#721c24'
                    }}
                  >
                    <i className="bi bi-exclamation-triangle-fill me-2"></i>
                    {passwordError}
                  </Alert>
                )}

                {/* Premium Password Form */}
                <Form onSubmit={handleChangePassword}>
                  <Form.Group className="mb-4">
                    <Form.Label className="fw-semibold" style={{ color: '#333' }}>
                      <i className="bi bi-lock me-2"></i>
                      Current Password
                    </Form.Label>
                    <InputGroup>
                      <InputGroup.Text style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        border: 'none',
                        color: 'white',
                        borderRadius: '15px 0 0 15px'
                      }}>
                        <i className="bi bi-shield-check"></i>
                      </InputGroup.Text>
                      <Form.Control
                        type="password"
                        value={oldPassword}
                        onChange={(e) => setOldPassword(e.target.value)}
                        required
                        className="premium-form-control"
                        style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                        placeholder="Enter your current password"
                      />
                    </InputGroup>
                  </Form.Group>

                  <Form.Group className="mb-4">
                    <Form.Label className="fw-semibold" style={{ color: '#333' }}>
                      <i className="bi bi-key me-2"></i>
                      New Password
                    </Form.Label>
                    <InputGroup>
                      <InputGroup.Text style={{
                        background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                        border: 'none',
                        color: 'white',
                        borderRadius: '15px 0 0 15px'
                      }}>
                        <i className="bi bi-lock-fill"></i>
                      </InputGroup.Text>
                      <Form.Control
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                        className="premium-form-control"
                        style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                        placeholder="Enter your new password"
                      />
                    </InputGroup>
                    <Form.Text className="text-muted small mt-2">
                      <i className="bi bi-info-circle me-1"></i>
                      Use at least 8 characters with a mix of letters, numbers, and symbols
                    </Form.Text>
                  </Form.Group>

                  <div className="d-grid">
                    <Button
                      type="submit"
                      className="premium-btn"
                      style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        fontSize: '1.1rem',
                        padding: '15px'
                      }}
                    >
                      <i className="bi bi-shield-check me-2"></i>
                      Update Password
                    </Button>
                  </div>
                </Form>

                {/* Security Tips */}
                <div className="mt-4 p-3 rounded-4" style={{
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
                }}>
                  <h6 className="mb-2" style={{ color: '#333' }}>
                    <i className="bi bi-lightbulb me-2"></i>
                    Security Tips
                  </h6>
                  <ul className="small text-muted mb-0" style={{ paddingLeft: '1.2rem' }}>
                    <li>Use a unique password for this account</li>
                    <li>Include uppercase, lowercase, numbers, and symbols</li>
                    <li>Avoid using personal information</li>
                    <li>Consider using a password manager</li>
                  </ul>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Premium Account Statistics */}
        <Row className="mt-4">
          <Col>
            <Card className="premium-profile-card slide-in-up">
              <div style={{
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                padding: '1.5rem',
                borderRadius: '25px 25px 0 0'
              }}>
                <h4 className="mb-0">
                  <i className="bi bi-graph-up-arrow me-2"></i>
                  Account Overview
                </h4>
              </div>
              <Card.Body className="p-4">
                <Row className="text-center">
                  <Col md={3} className="mb-3 mb-md-0">
                    <div className="h2 mb-1" style={{ color: '#667eea' }}>
                      <i className="bi bi-calendar-check-fill"></i>
                    </div>
                    <h5 className="mb-1">Active</h5>
                    <small className="text-muted">Account Status</small>
                  </Col>
                  <Col md={3} className="mb-3 mb-md-0">
                    <div className="h2 mb-1" style={{ color: '#fa709a' }}>
                      <i className="bi bi-shield-fill-check"></i>
                    </div>
                    <h5 className="mb-1">Verified</h5>
                    <small className="text-muted">Email Status</small>
                  </Col>
                  <Col md={3} className="mb-3 mb-md-0">
                    <div className="h2 mb-1" style={{ color: '#4facfe' }}>
                      <i className="bi bi-clock-fill"></i>
                    </div>
                    <h5 className="mb-1">
                      {new Date(profile?.date_joined || Date.now()).toLocaleDateString()}
                    </h5>
                    <small className="text-muted">Member Since</small>
                  </Col>
                  <Col md={3}>
                    <div className="h2 mb-1" style={{ color: '#fee140' }}>
                      <i className="bi bi-star-fill"></i>
                    </div>
                    <h5 className="mb-1">Premium</h5>
                    <small className="text-muted">Account Type</small>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help with your profile?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>
    </div>
  );
};

export default ProfilePage;
