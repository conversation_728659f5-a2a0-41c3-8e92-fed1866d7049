import api from "./api";

export const getMyAvailabilityRules = () => api.get("/availability/rules/");
export const createAvailabilityRule = (data) => api.post("/availability/rules/", data);
export const deleteAvailabilityRule = (id) => api.delete(`/availability/rules/${id}/`);

// We can add exception functions later

export const checkAvailability = (params) => {
  // params = { expert_id, service_id, year, month }
  return api.get("/availability/check/", { params });
};


