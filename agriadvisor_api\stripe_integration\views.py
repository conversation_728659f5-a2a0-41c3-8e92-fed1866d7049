# stripe_integration/views.py

import stripe
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from core.permissions import IsExpertUser

from rest_framework.permissions import AllowAny

from advisory.models import Booking, Service
from accounts.models import CustomUser, Tenant

stripe.api_key = settings.STRIPE_SECRET_KEY

class CreateStripeConnectAccountView(APIView):
    """
    An endpoint for an Expert user to create a Stripe Express account.
    """
    permission_classes = [IsAuthenticated, IsExpertUser]

    def post(self, request, *args, **kwargs):
        expert_user = request.user
        
        # Check if the user already has a Stripe account ID
        if expert_user.expert_profile and expert_user.expert_profile.stripe_account_id:
            # Optional: Handle this case, maybe by creating a new link for an existing account
            # For now, we'll assume we only create one.
            return Response(
                {"error": "A Stripe Connect account already exists for this user."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Step 1: Create a new Express account on Stripe
            account = stripe.Account.create(
                type="express",
                email=expert_user.email,
                capabilities={
                    "card_payments": {"requested": True},
                    "transfers": {"requested": True},
                },
            )

            # Step 2: Save the new account ID to the expert's profile
            expert_profile = expert_user.expert_profile
            expert_profile.stripe_account_id = account.id
            expert_profile.save()

            # Step 3: Create an account link for the expert to complete onboarding
            # The refresh_url and return_url are where Stripe sends the user
            # after they complete or abandon the onboarding process.
            account_link = stripe.AccountLink.create(
                account=account.id,
                refresh_url="http://localhost:5173/expert/payouts?reauth=true",
                return_url="http://localhost:5173/expert/payouts",
                type="account_onboarding",
            )

            # Step 4: Return the account link URL to the frontend
            return Response({'onboarding_url': account_link.url})

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class StripeWebhookView(APIView):
    """
    Listens for webhook events from Stripe.
    This endpoint must be public, as it's called by Stripe's servers,
    but it is secured by verifying the webhook signature.
    """
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        # Get the signature from the headers
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        # Get the raw request body
        payload = request.body
        # Get the webhook secret from your settings
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET

        try:
            # Step 1: Verify the event is genuinely from Stripe
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError as e:
            # Invalid payload
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature
            return Response(status=status.HTTP_400_BAD_REQUEST)

        # Step 2: Handle the specific event type
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            
            # Extract our metadata
            metadata = payment_intent.get('metadata', {})
            service_id = metadata.get('service_id')
            expert_id = metadata.get('expert_id')
            farmer_id = metadata.get('farmer_id')
            tenant_id = metadata.get('tenant_id')
            
            # --- IMPORTANT: We need booking_time from the payment intent ---
            # For simplicity, we'll assume the payment is the booking time.
            # A better approach would be to pass it in metadata too.
            booking_time = datetime.fromtimestamp(payment_intent['created'])
            
            try:
                # Step 3: Create the Booking in our database
                Booking.objects.create(
                    service_id=service_id,
                    expert_id=expert_id,
                    farmer_id=farmer_id,
                    tenant_id=tenant_id,
                    booking_time=booking_time,
                    status='confirmed', # The booking is now officially confirmed
                    stripe_payment_intent_id=payment_intent['id']
                )
                print("✅ Successfully created booking from webhook.")
            except Exception as e:
                # Handle potential errors, e.g., if one of the IDs is invalid
                print(f"❌ Error creating booking from webhook: {e}")
                # You might want to log this error for manual review
                pass

        # You can add handlers for other events here, e.g., 'payment_intent.payment_failed'
        # or 'account.updated' for Connect onboarding status.

        # Step 4: Acknowledge receipt of the event
        return Response(status=status.HTTP_200_OK)

# We need datetime for the booking_time
from datetime import datetime




