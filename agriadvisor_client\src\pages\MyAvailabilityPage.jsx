// src/pages/MyAvailabilityPage.jsx

import React, { useState, useEffect } from "react";
import { Con<PERSON>er, Card, Form, <PERSON><PERSON>, Row, <PERSON>, Spin<PERSON>, <PERSON><PERSON>, ListGroup, Badge, InputGroup } from "react-bootstrap";
import {
  getMyAvailabilityRules,
  createAvailabilityRule,
  deleteAvailabilityRule,
} from "../services/availabilityService";

// Premium Availability CSS and Animations
const premiumCSS = `
  @keyframes scheduleSlideIn {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes timeSlotPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
    }
  }

  @keyframes formGlow {
    from {
      box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
    to {
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }
  }

  .premium-availability-card {
    border: none;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    animation: scheduleSlideIn 0.6s ease-out;
  }

  .premium-availability-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  }

  .time-slot-item {
    border: none;
    border-radius: 15px;
    margin-bottom: 10px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transition: all 0.3s ease;
    animation: scheduleSlideIn 0.6s ease-out;
  }

  .time-slot-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateX(10px);
    animation: timeSlotPulse 2s infinite;
  }

  .day-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-block;
    margin-right: 15px;
    min-width: 100px;
    text-align: center;
  }

  .time-display {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-block;
    margin-right: 10px;
  }

  .premium-form-control {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 12px 20px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.9);
  }

  .premium-form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
    animation: formGlow 0.3s ease;
  }

  .premium-btn {
    border-radius: 15px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
  }

  .premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  }

  .premium-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .premium-btn:hover::before {
    left: 100%;
  }

  .delete-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    border-radius: 10px;
    color: white;
    padding: 8px 16px;
    transition: all 0.3s ease;
  }

  .delete-btn:hover {
    background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
  }

  .slide-in-left {
    animation: scheduleSlideIn 0.6s ease-out;
  }

  .weekly-overview {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(252, 182, 159, 0.3);
  }

  .schedule-stats {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
  }
`;

// Inject premium CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 30px 30px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.15)'
  },
  stickyContent: {
    paddingTop: '20px'
  },
  scheduleCard: {
    background: 'linear-gradient(135deg, #e3ffe7 0%, #d9e7ff 100%)',
    border: 'none'
  },
  addSlotCard: {
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    border: 'none'
  },
  gradientHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  },
  addSlotHeader: {
    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    color: 'white',
    borderRadius: '25px 25px 0 0',
    padding: '1.5rem'
  }
};

const MyAvailabilityPage = () => {
  const [rules, setRules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Form state for adding a new rule
  const [day, setDay] = useState("0"); // 0 for Monday
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("17:00");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  // Helper function to get day color
  const getDayColor = (dayIndex) => {
    const colors = [
      '#667eea', // Monday - Purple
      '#4facfe', // Tuesday - Blue
      '#43e97b', // Wednesday - Green
      '#fa709a', // Thursday - Pink
      '#fee140', // Friday - Yellow
      '#ff6b6b', // Saturday - Red
      '#764ba2'  // Sunday - Dark Purple
    ];
    return colors[dayIndex] || '#667eea';
  };

  // Helper function to calculate total hours
  const calculateTotalHours = () => {
    return rules.reduce((total, rule) => {
      const start = new Date(`2000-01-01T${rule.start_time}`);
      const end = new Date(`2000-01-01T${rule.end_time}`);
      const hours = (end - start) / (1000 * 60 * 60);
      return total + hours;
    }, 0);
  };

  // Helper function to get schedule statistics
  const getScheduleStats = () => {
    const totalHours = calculateTotalHours();
    const daysWithRules = new Set(rules.map(rule => rule.day_of_week)).size;
    const averageHoursPerDay = daysWithRules > 0 ? (totalHours / daysWithRules).toFixed(1) : 0;

    return {
      totalHours: totalHours.toFixed(1),
      activeDays: daysWithRules,
      averageHours: averageHoursPerDay
    };
  };

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await getMyAvailabilityRules();
      setRules(response.data);
      setError("");
    } catch (err) {
      setError("Failed to load availability rules. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, []);

  const handleAddRule = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");
    setSuccess("");

    try {
      // Validate times
      if (startTime >= endTime) {
        setError("End time must be after start time.");
        return;
      }

      await createAvailabilityRule({
        day_of_week: parseInt(day),
        start_time: startTime,
        end_time: endTime
      });

      setSuccess("Time slot added successfully!");
      setDay("0");
      setStartTime("09:00");
      setEndTime("17:00");
      fetchRules(); // Refresh list

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000);
    } catch (err) {
      setError(err.response?.data?.detail || "Could not add rule. It may overlap with an existing rule.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteRule = async (id) => {
    const rule = rules.find(r => r.id === id);
    const dayName = daysOfWeek[rule.day_of_week];

    if (window.confirm(`Are you sure you want to delete the ${dayName} time slot (${rule.start_time} - ${rule.end_time})?`)) {
      try {
        await deleteAvailabilityRule(id);
        setSuccess("Time slot deleted successfully!");
        fetchRules(); // Refresh list

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(""), 3000);
      } catch (err) {
        setError("Failed to delete time slot. Please try again.");
      }
    }
  };

  if (loading) {
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{
          ...premiumStyles.scheduleCard,
          maxWidth: '500px',
          padding: '3rem',
          borderRadius: '25px',
          boxShadow: '0 20px 40px rgba(227, 255, 231, 0.3)'
        }}>
          <Spinner animation="border" size="lg" style={{ color: '#667eea' }} />
          <h4 className="mt-3 mb-2" style={{ color: '#333' }}>Loading Your Schedule</h4>
          <p className="mb-0" style={{ color: '#666' }}>Preparing your availability settings...</p>
        </div>
      </Container>
    );
  }

  const stats = getScheduleStats();

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-calendar-week me-3"></i>
                My Availability
              </h1>
              <p className="lead mb-0 opacity-90">
                Manage your weekly schedule and working hours
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h3 mb-1">
                  <Badge bg="light" text="dark" className="px-3 py-2" style={{ borderRadius: '15px' }}>
                    {stats.activeDays} Days
                  </Badge>
                </div>
                <small className="opacity-75">Active Schedule</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Schedule Statistics */}
        <Row className="mb-4">
          <Col>
            <div className="weekly-overview">
              <Row className="text-center">
                <Col md={4}>
                  <div className="schedule-stats">
                    <div className="h2 mb-1" style={{ color: '#667eea' }}>
                      <i className="bi bi-clock-fill"></i>
                    </div>
                    <h4 className="mb-1">{stats.totalHours}h</h4>
                    <small className="text-muted">Total Weekly Hours</small>
                  </div>
                </Col>
                <Col md={4}>
                  <div className="schedule-stats">
                    <div className="h2 mb-1" style={{ color: '#4facfe' }}>
                      <i className="bi bi-calendar-check-fill"></i>
                    </div>
                    <h4 className="mb-1">{stats.activeDays}</h4>
                    <small className="text-muted">Active Days</small>
                  </div>
                </Col>
                <Col md={4}>
                  <div className="schedule-stats">
                    <div className="h2 mb-1" style={{ color: '#43e97b' }}>
                      <i className="bi bi-graph-up-arrow"></i>
                    </div>
                    <h4 className="mb-1">{stats.averageHours}h</h4>
                    <small className="text-muted">Average Per Day</small>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        <Row>
          {/* Premium Schedule Display */}
          <Col lg={7} className="mb-4">
            <Card className="premium-availability-card h-100" style={premiumStyles.scheduleCard}>
              <div style={premiumStyles.gradientHeader}>
                <h4 className="mb-0">
                  <i className="bi bi-calendar3 me-2"></i>
                  Weekly Schedule
                </h4>
              </div>
              <Card.Body className="p-4">
                {/* Success/Error Messages */}
                {success && (
                  <Alert
                    variant="success"
                    className="border-0 rounded-4 slide-in-left mb-4"
                    style={{
                      background: 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)',
                      color: '#155724'
                    }}
                  >
                    <i className="bi bi-check-circle-fill me-2"></i>
                    {success}
                  </Alert>
                )}

                {error && (
                  <Alert
                    variant="danger"
                    className="border-0 rounded-4 slide-in-left mb-4"
                    style={{
                      background: 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)',
                      color: '#721c24'
                    }}
                  >
                    <i className="bi bi-exclamation-triangle-fill me-2"></i>
                    {error}
                  </Alert>
                )}

                {/* Schedule List */}
                <div className="schedule-list">
                  {rules.length > 0 ? (
                    rules
                      .sort((a, b) => a.day_of_week - b.day_of_week)
                      .map((rule) => (
                        <div key={rule.id} className="time-slot-item">
                          <Row className="align-items-center">
                            <Col md={3}>
                              <div
                                className="day-badge"
                                style={{ backgroundColor: getDayColor(rule.day_of_week) }}
                              >
                                {daysOfWeek[rule.day_of_week]}
                              </div>
                            </Col>
                            <Col md={6}>
                              <div className="d-flex align-items-center">
                                <span className="time-display">
                                  <i className="bi bi-clock me-2"></i>
                                  {rule.start_time}
                                </span>
                                <span className="mx-2" style={{ color: '#666' }}>to</span>
                                <span className="time-display">
                                  <i className="bi bi-clock-fill me-2"></i>
                                  {rule.end_time}
                                </span>
                              </div>
                              <small className="text-muted mt-1 d-block">
                                Duration: {((new Date(`2000-01-01T${rule.end_time}`) - new Date(`2000-01-01T${rule.start_time}`)) / (1000 * 60 * 60)).toFixed(1)} hours
                              </small>
                            </Col>
                            <Col md={3} className="text-end">
                              <Button
                                variant="outline-danger"
                                size="sm"
                                className="delete-btn"
                                onClick={() => handleDeleteRule(rule.id)}
                              >
                                <i className="bi bi-trash me-1"></i>
                                Remove
                              </Button>
                            </Col>
                          </Row>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-5">
                      <div className="mb-3">
                        <i className="bi bi-calendar-x text-muted" style={{ fontSize: '3rem' }}></i>
                      </div>
                      <h5 className="text-muted">No schedule set</h5>
                      <p className="text-muted mb-0">
                        Add your first time slot to start accepting bookings
                      </p>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          </Col>
          {/* Premium Add Time Slot Form */}
          <Col lg={5} className="mb-4">
            <Card className="premium-availability-card h-100" style={premiumStyles.addSlotCard}>
              <div style={premiumStyles.addSlotHeader}>
                <h4 className="mb-0">
                  <i className="bi bi-plus-circle me-2"></i>
                  Add Time Slot
                </h4>
              </div>
              <Card.Body className="p-4">
                <div className="text-center mb-4">
                  <div
                    className="mx-auto mb-3"
                    style={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '2rem',
                      boxShadow: '0 10px 25px rgba(250, 112, 154, 0.3)'
                    }}
                  >
                    <i className="bi bi-calendar-plus"></i>
                  </div>
                  <h5 style={{ color: '#333' }}>Set Your Hours</h5>
                  <p className="text-muted small mb-0">
                    Add available time slots for client bookings
                  </p>
                </div>

                <Form onSubmit={handleAddRule}>
                  <Form.Group className="mb-4">
                    <Form.Label className="fw-semibold" style={{ color: '#333' }}>
                      <i className="bi bi-calendar-week me-2"></i>
                      Day of the Week
                    </Form.Label>
                    <InputGroup>
                      <InputGroup.Text style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        border: 'none',
                        color: 'white',
                        borderRadius: '15px 0 0 15px'
                      }}>
                        <i className="bi bi-calendar3"></i>
                      </InputGroup.Text>
                      <Form.Select
                        value={day}
                        onChange={(e) => setDay(e.target.value)}
                        className="premium-form-control"
                        style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                      >
                        {daysOfWeek.map((dayName, index) => (
                          <option key={index} value={index}>
                            {dayName}
                          </option>
                        ))}
                      </Form.Select>
                    </InputGroup>
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold" style={{ color: '#333' }}>
                          <i className="bi bi-clock me-2"></i>
                          Start Time
                        </Form.Label>
                        <InputGroup>
                          <InputGroup.Text style={{
                            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                            border: 'none',
                            color: 'white',
                            borderRadius: '15px 0 0 15px'
                          }}>
                            <i className="bi bi-play-fill"></i>
                          </InputGroup.Text>
                          <Form.Control
                            type="time"
                            value={startTime}
                            onChange={(e) => setStartTime(e.target.value)}
                            className="premium-form-control"
                            style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold" style={{ color: '#333' }}>
                          <i className="bi bi-clock-fill me-2"></i>
                          End Time
                        </Form.Label>
                        <InputGroup>
                          <InputGroup.Text style={{
                            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                            border: 'none',
                            color: 'white',
                            borderRadius: '15px 0 0 15px'
                          }}>
                            <i className="bi bi-stop-fill"></i>
                          </InputGroup.Text>
                          <Form.Control
                            type="time"
                            value={endTime}
                            onChange={(e) => setEndTime(e.target.value)}
                            className="premium-form-control"
                            style={{ borderLeft: 'none', borderRadius: '0 15px 15px 0' }}
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* Duration Preview */}
                  {startTime && endTime && startTime < endTime && (
                    <div className="mb-4 p-3 rounded-4" style={{
                      background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
                    }}>
                      <div className="text-center">
                        <i className="bi bi-hourglass-split me-2" style={{ color: '#667eea' }}></i>
                        <strong style={{ color: '#333' }}>
                          Duration: {((new Date(`2000-01-01T${endTime}`) - new Date(`2000-01-01T${startTime}`)) / (1000 * 60 * 60)).toFixed(1)} hours
                        </strong>
                      </div>
                    </div>
                  )}

                  <div className="d-grid">
                    <Button
                      type="submit"
                      className="premium-btn"
                      disabled={isSubmitting}
                      style={{
                        background: isSubmitting
                          ? 'linear-gradient(135deg, #ccc 0%, #999 100%)'
                          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        fontSize: '1.1rem',
                        padding: '15px'
                      }}
                    >
                      {isSubmitting ? (
                        <>
                          <Spinner animation="border" size="sm" className="me-2" />
                          Adding...
                        </>
                      ) : (
                        <>
                          <i className="bi bi-plus-circle me-2"></i>
                          Add to Schedule
                        </>
                      )}
                    </Button>
                  </div>
                </Form>

                {/* Quick Time Presets */}
                <div className="mt-4">
                  <h6 className="mb-3" style={{ color: '#333' }}>
                    <i className="bi bi-lightning-charge me-2"></i>
                    Quick Presets
                  </h6>
                  <Row>
                    <Col xs={6} className="mb-2">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="w-100"
                        onClick={() => { setStartTime("09:00"); setEndTime("17:00"); }}
                        style={{ borderRadius: '10px' }}
                      >
                        9 AM - 5 PM
                      </Button>
                    </Col>
                    <Col xs={6} className="mb-2">
                      <Button
                        variant="outline-success"
                        size="sm"
                        className="w-100"
                        onClick={() => { setStartTime("08:00"); setEndTime("16:00"); }}
                        style={{ borderRadius: '10px' }}
                      >
                        8 AM - 4 PM
                      </Button>
                    </Col>
                    <Col xs={6}>
                      <Button
                        variant="outline-info"
                        size="sm"
                        className="w-100"
                        onClick={() => { setStartTime("10:00"); setEndTime("18:00"); }}
                        style={{ borderRadius: '10px' }}
                      >
                        10 AM - 6 PM
                      </Button>
                    </Col>
                    <Col xs={6}>
                      <Button
                        variant="outline-warning"
                        size="sm"
                        className="w-100"
                        onClick={() => { setStartTime("13:00"); setEndTime("21:00"); }}
                        style={{ borderRadius: '10px' }}
                      >
                        1 PM - 9 PM
                      </Button>
                    </Col>
                  </Row>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '65px',
            height: '65px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.3rem'
          }}
          title="Need help with your schedule?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>
    </div>
  );
};

export default MyAvailabilityPage;


