# advisory/views.py

from django.utils import timezone
from django.db.models import Su<PERSON>, F, Dec<PERSON><PERSON><PERSON><PERSON>
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from django.db.models import Avg

from rest_framework import viewsets
from .models import Service, Booking

from .serializers import ServiceSerializer
from core.views import BaseTenantViewSet 

from .serializers import BookingSerializer

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from accounts.models import CustomUser
from core.permissions import IsAdminUser
from django.db.models import Sum, F, DecimalField

from .serializers import BookingSerializer

from core.permissions import <PERSON><PERSON><PERSON>erUser, IsExpertUser

from django.utils import timezone 
import datetime



class ServiceViewSet(BaseTenantViewSet):
    """
    API endpoint that allows services to be viewed or edited.
    Inherits all security and multi-tenancy logic from BaseTenantViewSet.
    """
    # queryset defines the complete set of objects this view can access.
    # The BaseTenantViewSet will automatically filter this down.
    queryset = Service.objects.all().order_by('name')
    
    # serializer_class tells the view which serializer to use for data conversion.
    serializer_class = ServiceSerializer



class BookingViewSet(BaseTenantViewSet):
    """
    API endpoint that allows bookings to be viewed or edited.
    Inherits all security and multi-tenancy logic from BaseTenantViewSet.
    """
    queryset = Booking.objects.all().select_related('service', 'expert', 'farmer')
    serializer_class = BookingSerializer

    def get_serializer_context(self):
        """
        Pass the request object to the serializer.
        This is crucial for our dynamic filtering logic in the serializer's __init__ method.
        """
        return {'request': self.request}



class DashboardStatsView(APIView):
    """
    An endpoint to provide aggregated statistics AND recent data for the admin dashboard.
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request, *args, **kwargs):
        tenant = request.user.tenant

        # Handle admin users without tenant (for development/testing)
        if not tenant and request.user.is_superuser:
            # For superusers without tenant, show all data
            expert_count = CustomUser.objects.filter(role='expert').count()
            service_count = Service.objects.count()
            booking_count = Booking.objects.filter(status='confirmed').count()
            total_revenue = Booking.objects.filter(
                status='confirmed'
            ).aggregate(
                total=Sum(F('service__price'), output_field=DecimalField())
            )['total'] or 0.00

            # Recent bookings without tenant filter
            recent_bookings = Booking.objects.select_related(
                'service', 'expert', 'farmer'
            ).order_by('-booking_time')[:5]
        else:
            # Normal tenant-based filtering
            expert_count = CustomUser.objects.filter(tenant=tenant, role='expert').count()
            service_count = Service.objects.filter(tenant=tenant).count()
            booking_count = Booking.objects.filter(tenant=tenant, status='confirmed').count()
            total_revenue = Booking.objects.filter(
                tenant=tenant,
                status='confirmed'
            ).aggregate(
                total=Sum(F('service__price'), output_field=DecimalField())
            )['total'] or 0.00

            # Recent bookings with tenant filter
            recent_bookings = Booking.objects.filter(
                tenant=tenant
            ).select_related(
                'service', 'expert', 'farmer'
            ).order_by('-booking_time')[:5]

        # We need to serialize this data to convert it to JSON
        recent_bookings_data = BookingSerializer(recent_bookings, many=True).data

        # --- COMBINE ALL DATA ---
        data = {
            'total_experts': expert_count,
            'total_services': service_count,
            'total_bookings': booking_count,
            'total_revenue': total_revenue,
            'recent_bookings': recent_bookings_data, # <--- ADD TO RESPONSE
        }

        return Response(data)
    


class FarmerServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    An API endpoint for Farmers to view available services in their tenant.
    """
    serializer_class = ServiceSerializer
    permission_classes = [IsAuthenticated, IsFarmerUser] # Lock down to farmers only

    def get_queryset(self):
        """
        This view should return a list of all the services
        for the currently authenticated user's tenant.
        """
        user = self.request.user
        if not user.tenant:
            # If for some reason a farmer has no tenant, return nothing.
            return Service.objects.none()
        return Service.objects.filter(tenant=user.tenant)



class ExpertBookingViewSet(viewsets.ReadOnlyModelViewSet):
    """
    An API endpoint for Experts to view bookings assigned to them.
    This is a read-only view.
    """
    serializer_class = BookingSerializer
    permission_classes = [IsAuthenticated, IsExpertUser] # Lock down to experts only

    def get_queryset(self):
        """
        This view MUST return a list of bookings filtered for the
        currently authenticated expert user.
        """
        # Get the user making the request
        user = self.request.user
        
        # Filter the Booking model where the 'expert' field is this user
        return Booking.objects.filter(
            expert=user
        ).select_related(
            'service', 'farmer' # Optimization to pre-fetch related data
        ).order_by('booking_time')
    
    
    # advisory/views.py in ExpertBookingViewSet
    from rest_framework.decorators import action

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        booking = self.get_object()
        # Add checks here if needed
        booking.status = 'completed'
        booking.save()
        return Response({'status': 'booking completed'})



# --- ADD THIS NEW VIEWSET ---
class FarmerBookingViewSet(viewsets.ReadOnlyModelViewSet):
    """
    An API endpoint for a Farmer to view THEIR OWN bookings.
    """
    serializer_class = BookingSerializer
    permission_classes = [IsAuthenticated, IsFarmerUser] # Locked down to farmers

    def get_queryset(self):
        """
        Crucially, this filters the bookings to only those where the
        'farmer' field matches the user making the request.
        """
        user = self.request.user
        return Booking.objects.filter(
            farmer=user
        ).select_related(
            'service', 'expert'
        ).order_by('-booking_time') # Show most recent first



class FarmerDashboardView(APIView):
    """
    Provides all the necessary data for the Farmer's dashboard in a single call.
    """
    permission_classes = [IsAuthenticated, IsFarmerUser]

    def get(self, request, *args, **kwargs):
        farmer = request.user
        
        # --- 1. Find the Next Upcoming Booking ---
        next_booking = Booking.objects.filter(
            farmer=farmer,
            status='confirmed',
            booking_time__gte=timezone.now() # Filter for bookings in the future
        ).select_related(
            'service', 'expert'
        ).order_by('booking_time').first() # Get the very next one
        
        # Serialize the booking data if it exists
        next_booking_data = BookingSerializer(next_booking).data if next_booking else None
        
        # --- 2. Get Recent Activity (Placeholder for now) ---
        # In the future, this would query Review models, new Formulation models, etc.
        # For now, we'll simulate with the 3 most recent completed bookings.
        recent_activity_bookings = Booking.objects.filter(
            farmer=farmer,
            status='completed'
        ).select_related(
            'service', 'expert'
        ).order_by('-booking_time')[:3]
        
        recent_activity_data = BookingSerializer(recent_activity_bookings, many=True).data

        # --- 3. Consolidate the data into a single response ---
        dashboard_data = {
            'next_booking': next_booking_data,
            'recent_activity': recent_activity_data, # This will be a list
        }
        
        return Response(dashboard_data)



class ExpertDashboardView(APIView):
    """
    Provides all necessary data for the Expert's dashboard in a single call.
    """
    permission_classes = [IsAuthenticated, IsExpertUser]

    def get(self, request, *args, **kwargs):
        expert = request.user
        now = timezone.now()
        
        # --- 1. CALCULATE STATISTICS ---
        
        # Upcoming appointments in the next 7 days
        next_7_days = now + timedelta(days=7)
        upcoming_count = Booking.objects.filter(
            expert=expert,
            status='confirmed',
            booking_time__range=[now, next_7_days]
        ).count()
        
        # Total earnings for the current month
        # We only count 'completed' bookings as earned revenue
        earnings_this_month = Booking.objects.filter(
            expert=expert,
            status='completed',
            booking_time__year=now.year,
            booking_time__month=now.month
        ).aggregate(
            total=Sum(F('service__price'), output_field=DecimalField())
        )['total'] or 0.00
        
        # --- THIS IS THE FIX ---
        # Convert the 80% to a Decimal before multiplying.
        expert_earnings = earnings_this_month * Decimal('0.80')

        # This is the rating fix from our prediction, which is still good practice to have.
        avg_rating_data = expert.received_reviews.aggregate(Avg('rating'))
        avg_rating_value = avg_rating_data.get('rating__avg')
        average_rating = round(avg_rating_value, 1) if avg_rating_value is not None else 0.0
        
        
        # --- 2. GET BOOKINGS FOR THE CALENDAR ---
        # Fetch all bookings for the calendar view
        all_my_bookings = Booking.objects.filter(
            expert=expert
        ).select_related('service', 'farmer')
        
        all_my_bookings_data = BookingSerializer(all_my_bookings, many=True).data
        
        # --- 3. GET ACTIONABLE ITEMS ---
        # Get recently completed bookings that are awaiting the expert's action (e.g., follow-up)
        # For now, let's just get the 3 most recently completed bookings.
        action_items = Booking.objects.filter(
            expert=expert,
            status='completed'
        ).order_by('-booking_time')[:3]
        
        action_items_data = BookingSerializer(action_items, many=True).data

        # --- 4. CONSOLIDATE THE DATA ---
        dashboard_data = {
            'stats': {
                'upcoming_count': upcoming_count,
                'monthly_earnings': expert_earnings,
                'average_rating': average_rating,
            },
            'calendar_bookings': all_my_bookings_data,
            'action_items': action_items_data,
        }
        
        return Response(dashboard_data)