# accounts/views.py

from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from rest_framework import generics, status, viewsets
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView

from core.permissions import Is<PERSON>armerUser
from core.views import BaseTenantViewSet
from .models import CustomUser
from .serializers import (
    FarmerRegistrationSerializer, CustomUserSerializer, 
    ExpertReadSerializer, ExpertAdminSerializer
)

# --- START: AUTHENTICATION VIEWS ---

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Customizes JWT token claims."""
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # Add custom claims
        token['username'] = user.username
        token['role'] = user.role
        token['first_name'] = user.first_name
        token['tenant_id'] = user.tenant.id if user.tenant else None
        token['has_completed_onboarding'] = user.has_completed_onboarding
        return token

class MyTokenObtainPairView(TokenObtainPairView):
    """Uses the custom serializer to return a token with custom claims."""
    serializer_class = MyTokenObtainPairSerializer

class RegisterUserView(generics.CreateAPIView):
    """Endpoint for new Farmer registration."""
    serializer_class = FarmerRegistrationSerializer
    permission_classes = [AllowAny]

class ForgotPasswordView(generics.GenericAPIView):
    """Endpoint to request a password reset email."""
    permission_classes = [AllowAny]
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        try:
            user = CustomUser.objects.get(email=email)
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            reset_link = f"http://localhost:5173/reset-password?token={token}&uid={uid}"
            send_mail(
                "Password Reset for AgriAdvisor",
                f"Click the link to reset your password: {reset_link}",
                settings.DEFAULT_FROM_EMAIL,
                [user.email]
            )
        except CustomUser.DoesNotExist:
            pass # Fail silently
        return Response({"detail": "If an account exists, an email has been sent."}, status=status.HTTP_200_OK)

class ResetPasswordView(generics.GenericAPIView):
    """Endpoint to set a new password using a token."""
    permission_classes = [AllowAny]
    def post(self, request, *args, **kwargs):
        token = request.data.get('token')
        uidb64 = request.data.get('uid')
        password = request.data.get('password')
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)
            if default_token_generator.check_token(user, token):
                user.set_password(password)
                user.save()
                return Response({"detail": "Password has been reset successfully."}, status=status.HTTP_200_OK)
        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
            pass # Fail silently
        return Response({"error": "The reset link is invalid or has expired."}, status=status.HTTP_400_BAD_REQUEST)

class MeView(generics.RetrieveAPIView):
    """Endpoint to get the current user's profile."""
    permission_classes = [IsAuthenticated]
    serializer_class = CustomUserSerializer
    def get_object(self):
        return self.request.user
        
# --- END: AUTHENTICATION VIEWS ---


# --- START: VIEWSETS ---

class ExpertViewSet(BaseTenantViewSet):
    """FOR ADMINS: Full CRUD for managing experts."""
    queryset = CustomUser.objects.filter(role='expert')
    def get_serializer_class(self): # Use different serializers for read/write
        if self.action in ['create', 'update', 'partial_update']:
            return ExpertAdminSerializer
        return ExpertReadSerializer

class FarmerViewSet(viewsets.ReadOnlyModelViewSet):
    """FOR ADMINS: Read-only list of farmers."""
    queryset = CustomUser.objects.filter(role='farmer')
    serializer_class = CustomUserSerializer
    permission_classes = [IsAuthenticated]
    def get_queryset(self):
        return self.queryset.filter(tenant=self.request.user.tenant)

class FarmerExpertViewSet(viewsets.ReadOnlyModelViewSet):
    """FOR FARMERS: Read-only list of available experts."""
    queryset = CustomUser.objects.filter(role='expert')
    serializer_class = ExpertReadSerializer
    permission_classes = [IsAuthenticated, IsFarmerUser]
    def get_queryset(self):
        return self.queryset.filter(tenant=self.request.user.tenant)

# --- END: VIEWSETS ---



