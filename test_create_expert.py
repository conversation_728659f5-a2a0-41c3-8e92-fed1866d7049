#!/usr/bin/env python3
import requests
import json

# Login
login_data = {'username': 'admin_user', 'password': 'admin123'}
response = requests.post('http://localhost:8001/api/accounts/token/', json=login_data)
token = response.json()['access']
headers = {'Authorization': f'Bearer {token}'}

# Create expert with different email
expert_data = {
    'full_name': 'New Expert Test',
    'email': '<EMAIL>',
    'expert_profile': {
        'specialty': 'Soil Management',
        'bio': 'Expert in soil health and management.'
    }
}

response = requests.post('http://localhost:8001/api/accounts/experts/', json=expert_data, headers=headers)
print(f'Status: {response.status_code}')
if response.status_code == 201:
    data = response.json()
    print('✅ Created expert successfully!')
    print(f'Name: {data.get("first_name", "")} {data.get("last_name", "")}')
    print(f'Rating: {data.get("average_rating", 0)} ({data.get("review_count", 0)} reviews)')
else:
    print(f'❌ Error: {response.text[:200]}')
