{"version": 3, "file": "main.js", "sources": ["src/main.ts"], "sourcesContent": ["import { Theme, createPlugin } from '@fullcalendar/common'\nimport './main.css'\n\nexport class BootstrapTheme extends Theme {\n}\n\nBootstrapTheme.prototype.classes = {\n  root: 'fc-theme-bootstrap5',\n  tableCellShaded: 'fc-theme-bootstrap5-shaded',\n  buttonGroup: 'btn-group',\n  button: 'btn btn-primary',\n  buttonActive: 'active',\n  popover: 'popover',\n  popoverHeader: 'popover-header',\n  popoverContent: 'popover-body',\n}\n\nBootstrapTheme.prototype.baseIconClass = 'bi'\nBootstrapTheme.prototype.iconClasses = {\n  close: 'bi-x-lg',\n  prev: 'bi-chevron-left',\n  next: 'bi-chevron-right',\n  prevYear: 'bi-chevron-double-left',\n  nextYear: 'bi-chevron-double-right',\n}\nBootstrapTheme.prototype.rtlIconClasses = {\n  prev: 'bi-chevron-right',\n  next: 'bi-chevron-left',\n  prevYear: 'bi-chevron-double-right',\n  nextYear: 'bi-chevron-double-left',\n}\n\n// wtf\nBootstrapTheme.prototype.iconOverrideOption = 'buttonIcons' // TODO: make TS-friendly\nBootstrapTheme.prototype.iconOverrideCustomButtonOption = 'icon'\nBootstrapTheme.prototype.iconOverridePrefix = 'bi-'\n\nconst plugin = createPlugin({\n  themeClasses: {\n    bootstrap5: BootstrapTheme,\n  },\n})\n\nexport default plugin\n"], "names": [], "mappings": ";;;;;;;;;;;IAGoC,kCAAK;IAAzC;;KACC;IAAD,qBAAC;AAAD,CADA,CAAoC,KAAK,GACxC;AAED,cAAc,CAAC,SAAS,CAAC,OAAO,GAAG;IACjC,IAAI,EAAE,qBAAqB;IAC3B,eAAe,EAAE,4BAA4B;IAC7C,WAAW,EAAE,WAAW;IACxB,MAAM,EAAE,iBAAiB;IACzB,YAAY,EAAE,QAAQ;IACtB,OAAO,EAAE,SAAS;IAClB,aAAa,EAAE,gBAAgB;IAC/B,cAAc,EAAE,cAAc;CAC/B,CAAA;AAED,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAA;AAC7C,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG;IACrC,KAAK,EAAE,SAAS;IAChB,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE,kBAAkB;IACxB,QAAQ,EAAE,wBAAwB;IAClC,QAAQ,EAAE,yBAAyB;CACpC,CAAA;AACD,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG;IACxC,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,iBAAiB;IACvB,QAAQ,EAAE,yBAAyB;IACnC,QAAQ,EAAE,wBAAwB;CACnC,CAAA;AAED;AACA,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,aAAa,CAAA;AAC3D,cAAc,CAAC,SAAS,CAAC,8BAA8B,GAAG,MAAM,CAAA;AAChE,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,KAAK,CAAA;IAE7C,MAAM,GAAG,YAAY,CAAC;IAC1B,YAAY,EAAE;QACZ,UAAU,EAAE,cAAc;KAC3B;CACF;;;;;"}