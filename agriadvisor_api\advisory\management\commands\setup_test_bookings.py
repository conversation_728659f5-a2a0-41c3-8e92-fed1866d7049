# advisory/management/commands/setup_test_bookings.py

from django.core.management.base import BaseCommand
from advisory.models import Booking
from accounts.models import CustomUser


class Command(BaseCommand):
    help = 'Set up test bookings for review testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-completed',
            action='store_true',
            help='Create a completed booking for the farmer to review',
        )
        parser.add_argument(
            '--list',
            action='store_true',
            help='List all bookings and their status',
        )

    def handle(self, *args, **options):
        if options['list']:
            self.list_bookings()
        elif options['create_completed']:
            self.create_completed_booking()
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please provide either --list or --create-completed'
                )
            )

    def list_bookings(self):
        """List all bookings and their status"""
        bookings = Booking.objects.all().select_related('service', 'expert', 'farmer')
        
        self.stdout.write(self.style.SUCCESS('\nAll Bookings:'))
        self.stdout.write('-' * 100)
        
        for booking in bookings:
            self.stdout.write(
                f"ID: {booking.id:2} | Status: {booking.status:12} | "
                f"Service: {booking.service.name:20} | "
                f"Expert: {booking.expert.first_name} {booking.expert.last_name:15} | "
                f"Farmer: {booking.farmer.first_name} {booking.farmer.last_name}"
            )

    def create_completed_booking(self):
        """Create a completed booking for testing reviews"""
        try:
            # Get the farmer_user and an expert with payments enabled
            farmer = CustomUser.objects.get(username='farmer_user')
            expert = CustomUser.objects.filter(
                role='expert',
                expert_profile__stripe_account_id__isnull=False
            ).first()
            
            if not expert:
                self.stdout.write(
                    self.style.ERROR('No expert with payment capabilities found')
                )
                return
            
            # Get a service
            from advisory.models import Service
            service = Service.objects.first()
            
            if not service:
                self.stdout.write(
                    self.style.ERROR('No service found')
                )
                return
            
            # Create a completed booking
            from datetime import datetime, timedelta
            booking = Booking.objects.create(
                service=service,
                expert=expert,
                farmer=farmer,
                booking_time=datetime.now() - timedelta(days=1),  # Yesterday
                status='completed',
                tenant=farmer.tenant
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Created completed booking (ID: {booking.id}) for farmer_user '
                    f'with {expert.first_name} {expert.last_name}'
                )
            )
            
        except CustomUser.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('farmer_user not found. Run create_test_users first.')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating booking: {str(e)}')
            )
