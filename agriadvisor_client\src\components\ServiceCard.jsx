// src/components/ServiceCard.jsx

import React from "react";
import { Card, Button } from "react-bootstrap";
import { useNavigate } from "react-router-dom"; // You correctly imported this

/**
 * A simple, read-only card for displaying a service to a Farmer.
 */
const ServiceCard = ({ service }) => {
  const navigate = useNavigate(); // You correctly initialized this

  const handleBookClick = () => {
    // This function is perfect
    navigate(`/new-booking/${service.id}`, { state: { service } });
  };

  const cardStyle = {
    boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
    transition: "transform 0.2s",
    cursor: "pointer",
  };

  return (
    <Card className="h-100 service-card" style={cardStyle}>
      <Card.Body className="d-flex flex-column">
        <Card.Title>{service.name}</Card.Title>
        <Card.Subtitle className="mb-2 text-muted">
          ${parseFloat(service.price).toFixed(2)} | {service.duration_minutes} minutes
        </Card.Subtitle>
        <Card.Text style={{ flexGrow: 1 }}>{service.description}</Card.Text>

        {/* --- THIS IS THE ONLY CHANGE NEEDED --- */}
        <Button variant="success" className="mt-auto" onClick={handleBookClick}>
          Book This Service
        </Button>
        {/* --- END OF CHANGE --- */}
      </Card.Body>
    </Card>
  );
};

export default ServiceCard;



