// src/components/BookingModal.jsx

import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const BookingModal = ({ show, onHide, onSave, booking, selectedDate, services, experts, farmers }) => {
  const [formData, setFormData] = useState({
    service_id: "",
    expert_id: "",
    farmer_id: "",
    booking_time: "",
    status: "confirmed", // Default status
  });

  const isEditing = booking !== null;

  useEffect(() => {
    if (show) {
      // Only update form when modal is shown
      if (isEditing) {
        // Pre-fill form for editing an existing booking
        setFormData({
          service_id: booking.service.id,
          expert_id: booking.expert.id,
          farmer_id: booking.farmer.id,
          // The backend sends a UTC string. We need to format it for the datetime-local input.
          booking_time: new Date(booking.booking_time).toISOString().slice(0, 16),
          status: booking.status,
        });
      } else {
        // Pre-fill form for creating a new booking
        setFormData({
          service_id: "",
          expert_id: "",
          farmer_id: "",
          // Use the date the user clicked on, defaulting to 09:00 AM
          booking_time: selectedDate ? `${selectedDate}T09:00` : "",
          status: "confirmed",
        });
      }
    }
  }, [booking, selectedDate, show]); // Rerun effect when these change

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>{isEditing ? "Edit Booking" : "Create New Booking"}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Service</Form.Label>
            <Form.Select name="service_id" value={formData.service_id} onChange={handleChange} required>
              <option value="">Select a Service</option>
              {services.map((s) => (
                <option key={s.id} value={s.id}>
                  {s.name}
                </option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Expert</Form.Label>
            <Form.Select name="expert_id" value={formData.expert_id} onChange={handleChange} required>
              <option value="">Select an Expert</option>
              {experts.map((e) => (
                <option key={e.id} value={e.id}>
                  {e.first_name} {e.last_name}
                </option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Farmer</Form.Label>
            <Form.Select name="farmer_id" value={formData.farmer_id} onChange={handleChange} required>
              <option value="">Select a Farmer</option>
              {farmers.map((f) => (
                <option key={f.id} value={f.id}>
                  {f.first_name} {f.last_name}
                </option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Booking Date & Time</Form.Label>
            <Form.Control
              type="datetime-local"
              name="booking_time"
              value={formData.booking_time}
              onChange={handleChange}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Status</Form.Label>
            <Form.Select name="status" value={formData.status} onChange={handleChange} required>
              <option value="pending_payment">Pending Payment</option>
              <option value="confirmed">Confirmed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </Form.Select>
          </Form.Group>
          <Button type="submit" style={{ display: "none" }} />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSubmit}>
          Save Booking
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default BookingModal;


