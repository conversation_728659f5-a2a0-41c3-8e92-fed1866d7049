/* Global styles to prevent flickering and layout shifts */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
}

/* Prevent layout shifts during loading */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Smooth transitions for specific elements only */
.container-fluid,
.container,
.card {
  transition: opacity 0.15s ease-in-out;
}

/* Prevent flash of unstyled content */
.container-fluid,
.container {
  opacity: 1;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Prevent spinner flickering */
.spinner-border {
  animation-duration: 0.75s;
}

/* Stable layout for cards */
.card {
  min-height: 120px;
}

/* Prevent button size changes */
.btn {
  min-width: 80px;
}

/* Ensure navigation links are clickable */
.nav-link,
a {
  pointer-events: auto !important;
  cursor: pointer;
}

/* Fix any z-index issues */
.nav,
.navbar,
.sidebar {
  z-index: 1000;
}
