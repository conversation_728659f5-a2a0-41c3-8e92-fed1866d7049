// src/pages/PayoutsPage.jsx

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, <PERSON><PERSON>, Table, Row, Col, Badge, ProgressBar } from "react-bootstrap";
import { getMyProfile } from "../services/authService";
import { createConnectAccount } from "../services/stripeService";
import { getMySchedule } from "../services/bookingService"; // <-- IMPORT for earnings
import { Link } from "react-router-dom";

// Add premium animations and effects
const premiumCSS = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .premium-card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
  }

  .earnings-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  .fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
`;

// Inject CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = premiumCSS;
  document.head.appendChild(styleSheet);
}

// Premium styling constants
const premiumStyles = {
  pageHeader: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '3rem 0',
    marginBottom: '2rem',
    borderRadius: '0 0 20px 20px',
    boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
  },
  stickyContent: {
    paddingTop: '20px'
  },
  premiumCard: {
    border: 'none',
    borderRadius: '15px',
    boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    overflow: 'hidden'
  },
  gradientHeader: {
    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    color: 'white',
    borderRadius: '15px 15px 0 0',
    padding: '1.5rem'
  },
  earningsCard: {
    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    color: 'white',
    borderRadius: '15px',
    padding: '2rem',
    textAlign: 'center',
    boxShadow: '0 15px 35px rgba(79, 172, 254, 0.3)'
  },
  statusBadge: {
    padding: '0.5rem 1rem',
    borderRadius: '25px',
    fontWeight: '600',
    fontSize: '0.9rem'
  },
  tableRow: {
    transition: 'background-color 0.2s ease',
    cursor: 'pointer'
  }
};

const PayoutsPage = () => {
  const [profile, setProfile] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch both profile and bookings in parallel
        const [profileData, bookingsData] = await Promise.all([getMyProfile(), getMySchedule()]);
        setProfile(profileData);
        setBookings(bookingsData);
      } catch (err) {
        setError("Failed to load your payouts data.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleConnectStripe = async () => {
    // ... (this function is the same as before)
  };

  // Calculate totals
  const completedBookings = bookings.filter((b) => b.status === "completed");
  const totalEarned = completedBookings.reduce((sum, b) => sum + parseFloat(b.expert_earnings), 0);

  if (loading)
    return (
      <Container className="text-center" style={{ marginTop: '5rem' }}>
        <div className="mx-auto" style={{ ...premiumStyles.earningsCard, maxWidth: '400px', padding: '3rem' }}>
          <Spinner animation="border" size="lg" style={{ color: 'white' }} />
          <h4 className="mt-3 mb-0">Loading your earnings...</h4>
          <p className="mt-2 opacity-75">Calculating your financial insights</p>
        </div>
      </Container>
    );

  if (error)
    return (
      <Container className="mt-4">
        <div style={premiumStyles.premiumCard} className="p-4">
          <Alert variant="danger" className="border-0 rounded-3">
            <Alert.Heading>
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              Unable to Load Payouts
            </Alert.Heading>
            {error}
          </Alert>
        </div>
      </Container>
    );

  return (
    <div>
      {/* Premium Page Header - Sticky */}
      <div style={{...premiumStyles.pageHeader, position: 'sticky', top: 0, zIndex: 1010}}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <h1 className="display-4 fw-bold mb-2">
                <i className="bi bi-wallet2 me-3"></i>
                Payouts & Earnings
              </h1>
              <p className="lead mb-0 opacity-90">
                Manage your payments and track your financial performance
              </p>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h2 mb-1">${totalEarned.toFixed(2)}</div>
                <small className="opacity-75">Total Earned</small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      <Container>
        {/* Premium Earnings Overview */}
        <Row className="mb-4">
          <Col lg={8}>
            <div style={premiumStyles.earningsCard} className="mb-4 fade-in-up earnings-shimmer">
              <Row className="align-items-center">
                <Col>
                  <h3 className="mb-1">
                    <i className="bi bi-graph-up-arrow me-2"></i>
                    Monthly Earnings
                  </h3>
                  <div className="display-4 fw-bold">${totalEarned.toFixed(2)}</div>
                  <p className="mb-0 opacity-75">
                    From {completedBookings.length} completed session{completedBookings.length !== 1 ? 's' : ''}
                  </p>
                </Col>
                <Col xs="auto">
                  <div className="text-center">
                    <div className="h1 mb-0">
                      <i className="bi bi-trophy-fill"></i>
                    </div>
                    <small>Expert Status</small>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
          <Col lg={4}>
            {/* Premium Stripe Connection Card */}
            <Card style={premiumStyles.premiumCard} className="h-100 premium-card-hover fade-in-up">
              <div style={premiumStyles.gradientHeader}>
                <h5 className="mb-0">
                  <i className="bi bi-credit-card me-2"></i>
                  Payment Status
                </h5>
              </div>
              <Card.Body className="d-flex flex-column justify-content-center text-center">
                {profile?.profile?.stripe_account_id ? (
                  <div>
                    <div className="mb-3">
                      <Badge bg="success" style={premiumStyles.statusBadge}>
                        <i className="bi bi-check-circle-fill me-2"></i>
                        Connected & Active
                      </Badge>
                    </div>
                    <p className="text-muted mb-3">
                      Your Stripe account is ready to receive payments
                    </p>
                    <small className="text-muted">
                      Account ID: {profile.profile.stripe_account_id.substring(0, 15)}...
                    </small>
                  </div>
                ) : (
                  <div>
                    <div className="mb-3">
                      <Badge bg="warning" style={premiumStyles.statusBadge}>
                        <i className="bi bi-exclamation-triangle-fill me-2"></i>
                        Setup Required
                      </Badge>
                    </div>
                    <p className="text-muted mb-3">
                      Connect your Stripe account to start receiving payments
                    </p>
                    <Button
                      variant="primary"
                      onClick={handleConnectStripe}
                      className="rounded-pill px-4"
                      style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}
                    >
                      <i className="bi bi-link-45deg me-2"></i>
                      Connect with Stripe
                    </Button>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Premium Earnings Report */}
        <Card style={premiumStyles.premiumCard} className="premium-card-hover fade-in-up">
          <div style={premiumStyles.gradientHeader}>
            <h4 className="mb-0">
              <i className="bi bi-bar-chart-line me-2"></i>
              Detailed Earnings Report
            </h4>
          </div>
          <Card.Body className="p-0">
            {completedBookings.length > 0 ? (
              <div className="table-responsive">
                <Table className="mb-0" hover>
                  <thead style={{ backgroundColor: '#f8f9fa' }}>
                    <tr>
                      <th className="border-0 py-3 px-4">
                        <i className="bi bi-calendar3 me-2 text-muted"></i>
                        Date Completed
                      </th>
                      <th className="border-0 py-3">
                        <i className="bi bi-briefcase me-2 text-muted"></i>
                        Service
                      </th>
                      <th className="border-0 py-3">
                        <i className="bi bi-person me-2 text-muted"></i>
                        Client
                      </th>
                      <th className="border-0 py-3 text-center">
                        <i className="bi bi-currency-dollar me-2 text-muted"></i>
                        Total Price
                      </th>
                      <th className="border-0 py-3 text-center">
                        <i className="bi bi-wallet2 me-2 text-muted"></i>
                        Your Earnings
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {completedBookings.map((booking) => (
                      <tr
                        key={booking.id}
                        style={premiumStyles.tableRow}
                        className="border-0"
                        onMouseEnter={(e) => e.target.closest('tr').style.backgroundColor = '#f8f9fa'}
                        onMouseLeave={(e) => e.target.closest('tr').style.backgroundColor = 'transparent'}
                      >
                        <td className="py-3 px-4 border-0">
                          <div className="fw-semibold">
                            {new Date(booking.booking_time).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </div>
                          <small className="text-muted">
                            {new Date(booking.booking_time).toLocaleDateString('en-US', {
                              weekday: 'long'
                            })}
                          </small>
                        </td>
                        <td className="py-3 border-0">
                          <div className="fw-semibold">{booking.service.name}</div>
                          <Badge bg="light" text="dark" className="mt-1">
                            Session #{booking.id}
                          </Badge>
                        </td>
                        <td className="py-3 border-0">
                          <div className="d-flex align-items-center">
                            <div
                              className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2"
                              style={{ width: '32px', height: '32px', fontSize: '0.8rem' }}
                            >
                              {booking.farmer.first_name[0]}{booking.farmer.last_name[0]}
                            </div>
                            <div>
                              <div className="fw-semibold">
                                {booking.farmer.first_name} {booking.farmer.last_name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 border-0 text-center">
                          <div className="fw-bold text-dark">
                            ${parseFloat(booking.service.price).toFixed(2)}
                          </div>
                        </td>
                        <td className="py-3 border-0 text-center">
                          <div
                            className="fw-bold text-white px-3 py-1 rounded-pill d-inline-block"
                            style={{
                              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                              fontSize: '0.9rem'
                            }}
                          >
                            ${parseFloat(booking.expert_earnings).toFixed(2)}
                          </div>
                          <div className="small text-muted mt-1">80% of total</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-5">
                <div className="mb-3">
                  <i className="bi bi-graph-up text-muted" style={{ fontSize: '3rem' }}></i>
                </div>
                <h5 className="text-muted">No completed sessions yet</h5>
                <p className="text-muted mb-4">
                  Your earnings will appear here once you complete your first consultation
                </p>
                <Button
                  variant="outline-primary"
                  as={Link}
                  to="/expert/dashboard"
                  className="rounded-pill px-4"
                >
                  <i className="bi bi-calendar-check me-2"></i>
                  View My Schedule
                </Button>
              </div>
            )}
          </Card.Body>
        </Card>

        {/* Premium Quick Actions */}
        <Row className="mt-4">
          <Col>
            <Card style={premiumStyles.premiumCard} className="premium-card-hover fade-in-up">
              <Card.Body className="text-center py-4">
                <h5 className="mb-3">
                  <i className="bi bi-lightning-charge-fill me-2 text-warning"></i>
                  Quick Actions
                </h5>
                <Row>
                  <Col md={4} className="mb-3 mb-md-0">
                    <Button
                      variant="outline-primary"
                      as={Link}
                      to="/expert/dashboard"
                      className="w-100 rounded-pill py-2"
                      style={{ borderWidth: '2px' }}
                    >
                      <i className="bi bi-speedometer2 me-2"></i>
                      Dashboard
                    </Button>
                  </Col>
                  <Col md={4} className="mb-3 mb-md-0">
                    <Button
                      variant="outline-success"
                      as={Link}
                      to="/expert/availability"
                      className="w-100 rounded-pill py-2"
                      style={{ borderWidth: '2px' }}
                    >
                      <i className="bi bi-calendar-plus me-2"></i>
                      Set Availability
                    </Button>
                  </Col>
                  <Col md={4}>
                    <Button
                      variant="outline-info"
                      as={Link}
                      to="/expert/profile"
                      className="w-100 rounded-pill py-2"
                      style={{ borderWidth: '2px' }}
                    >
                      <i className="bi bi-person-gear me-2"></i>
                      Edit Profile
                    </Button>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Floating Help Button */}
      <div
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 1000
        }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg"
          style={{
            width: '60px',
            height: '60px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            fontSize: '1.2rem'
          }}
          title="Need help with payouts?"
        >
          <i className="bi bi-question-lg"></i>
        </Button>
      </div>
    </div>
  );
};

export default PayoutsPage;



