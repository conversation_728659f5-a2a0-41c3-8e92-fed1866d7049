// src/components/Sidebar.jsx

import React from "react";
import { NavLink } from "react-router-dom";
import useAuth from "../hooks/useAuth";

const sidebarStyle = {
  background: "linear-gradient(180deg, #228B22, #006400)",
  color: "white",
  height: "100vh",
  padding: "20px",
  display: "flex",
  flexDirection: "column",
};

const navLinkStyle = {
  color: "rgba(255, 255, 255, 0.8)",
  textDecoration: "none",
  display: "block",
  padding: "10px 15px",
  borderRadius: "5px",
  marginBottom: "5px",
};

const activeNavLinkStyle = {
  backgroundColor: "rgba(255, 255, 255, 0.1)",
  color: "white",
};

const Sidebar = () => {
  const { logout } = useAuth();

  return (
    <div style={sidebarStyle}>
      <div>
        <h3 className="mb-4">
          <i className="bi bi-tree-fill me-2"></i>AgriAdvisor
        </h3>
        <ul className="nav flex-column">
          {/* --- THE ONLY CHANGES ARE IN THE "to" PROP BELOW --- */}
          <li className="nav-item">
            <NavLink
              to="/admin/dashboard" // <-- CORRECTED
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-speedometer2 me-2"></i>Dashboard
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              to="/admin/experts" // <-- CORRECTED
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-people me-2"></i>Experts
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              to="/admin/services" // <-- CORRECTED
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-grid me-2"></i>Services
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              to="/admin/bookings" // <-- CORRECTED
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-calendar-check me-2"></i>Bookings
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              to="/admin/farmers" // <-- CORRECTED
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-person-lines-fill me-2"></i>Farmers
            </NavLink>

            <NavLink
              to="/admin/announcements"
              style={({ isActive }) => (isActive ? { ...navLinkStyle, ...activeNavLinkStyle } : navLinkStyle)}
            >
              <i className="bi bi-megaphone-fill me-2"></i>Announcements
            </NavLink>
            
          </li>
        </ul>
      </div>

      <div className="mt-auto">
        <hr style={{ color: "rgba(255, 255, 255, 0.5)" }} />
        {/* Your logout button can stay here if you prefer it over the Header dropdown */}
        <button className="btn w-100 text-start" style={navLinkStyle} onClick={logout}>
          <i className="bi bi-box-arrow-right me-2"></i>Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar;


