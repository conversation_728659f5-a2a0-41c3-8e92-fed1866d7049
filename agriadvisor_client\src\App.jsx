// src/App.jsx

import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import useAuth from "./hooks/useAuth";

// Layouts, Pages, and Protector
import AdminLayout from "./layouts/AdminLayout";
import FarmerLayout from "./layouts/FarmerLayout";
import ExpertLayout from "./layouts/ExpertLayout";
import LoginPage from "./pages/LoginPage";
import RegistrationPage from "./pages/RegistrationPage";
import DashboardPage from "./pages/DashboardPage";
// ... import all your other pages
import ServicesPage from "./pages/ServicesPage";
import ExpertsPage from "./pages/ExpertsPage";
import BookingsPage from "./pages/BookingsPage";
import FarmersPage from "./pages/FarmersPage";
import AnnouncementsPage from "./pages/AnnouncementsPage";
import FarmerDashboardPage from "./pages/FarmerDashboardPage";
import BrowseServicesPage from "./pages/BrowseServicesPage";
import FarmerBookingsPage from "./pages/FarmerBookingsPage";
import NewBookingPage from "./pages/NewBookingPage";
import ExpertDashboardPage from "./pages/ExpertDashboardPage";
import PayoutsPage from "./pages/PayoutsPage";
import MyAvailabilityPage from "./pages/MyAvailabilityPage";
import ProfilePage from "./pages/ProfilePage";
import LiveSessionPage from "./pages/LiveSessionPage";
import ProtectedRoute from "./router/ProtectedRoute";

// A component for routes that should only be accessible when logged OUT
const PublicRoute = ({ children }) => {
  const { user, loading } = useAuth();

  // Don't redirect while still loading
  if (loading) {
    return <div>Loading...</div>;
  }

  return user ? <Navigate to="/" /> : children;
};

const RootRedirector = () => {
  const { user, loading } = useAuth();

  // Don't redirect while still loading
  if (loading) {
    return <div>Loading...</div>;
  }

  console.log("RootRedirector - user:", user);

  if (!user) return <Navigate to="/login" />;
  if (user.role === "admin") return <Navigate to="/admin/dashboard" />;
  if (user.role === "farmer") return <Navigate to="/farmer/dashboard" />;
  if (user.role === "expert") return <Navigate to="/expert/dashboard" />;
  return <Navigate to="/login" />; // Fallback
};

function App() {
  const { loading } = useAuth();

  // Display a global loading spinner while the app initializes the user session.
  if (loading) {
    return <div>Loading Application...</div>;
  }

  return (
    <Router>
      <Routes>
        {/* --- Public Routes --- */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <RegistrationPage />
            </PublicRoute>
          }
        />
        {/* We can add forgot-password here later */}

        {/* --- Protected Routes --- */}
        <Route element={<ProtectedRoute allowedRoles={["admin"]} />}>
          <Route path="/admin" element={<AdminLayout />}>
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="services" element={<ServicesPage />} />
            <Route path="experts" element={<ExpertsPage />} />
            <Route path="bookings" element={<BookingsPage />} />
            <Route path="farmers" element={<FarmersPage />} />
            <Route path="announcements" element={<AnnouncementsPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>
        </Route>

        <Route element={<ProtectedRoute allowedRoles={["farmer"]} />}>
          <Route path="/farmer" element={<FarmerLayout />}>
            <Route path="dashboard" element={<FarmerDashboardPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>
          {/* Routes that don't need the standard layout */}
          <Route path="/browse-services" element={<BrowseServicesPage />} />
          <Route path="/my-bookings" element={<FarmerBookingsPage />} />
          <Route path="/new-booking/:serviceId" element={<NewBookingPage />} />
          <Route path="/live-session/:bookingId" element={<LiveSessionPage />} />
        </Route>

        <Route element={<ProtectedRoute allowedRoles={["expert"]} />}>
          <Route path="/expert" element={<ExpertLayout />}>
            <Route path="dashboard" element={<ExpertDashboardPage />} />
            <Route path="payouts" element={<PayoutsPage />} />
            <Route path="availability" element={<MyAvailabilityPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>
          <Route path="/live-session/:bookingId" element={<LiveSessionPage />} />
        </Route>

        {/* --- Root Redirector --- */}
        <Route path="/" element={<RootRedirector />} />
        <Route
          path="*"
          element={
            <div>
              <h2>404 Not Found</h2>
            </div>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;




// // src/App.jsx

// import React, { useMemo } from "react";
// import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
// import useAuth from "./hooks/useAuth";

// // --- STRIPE IMPORTS ---
// import { loadStripe } from '@stripe/stripe-js';
// import { Elements } from '@stripe/react-stripe-js';
// // --- END STRIPE IMPORTS ---

// // Layouts
// import AdminLayout from "./layouts/AdminLayout";
// import FarmerLayout from "./layouts/FarmerLayout";
// import ExpertLayout from "./layouts/ExpertLayout";

// // Public Pages
// import LoginPage from "./pages/LoginPage";
// import RegistrationPage from "./pages/RegistrationPage";

// // Admin Pages
// import DashboardPage from "./pages/DashboardPage";
// import ServicesPage from "./pages/ServicesPage";
// import ExpertsPage from "./pages/ExpertsPage";
// import BookingsPage from "./pages/BookingsPage";
// import FarmersPage from "./pages/FarmersPage";

// import AnnouncementsPage from "./pages/AnnouncementsPage";

// // Farmer Pages
// import FarmerDashboardPage from "./pages/FarmerDashboardPage";
// import BrowseServicesPage from "./pages/BrowseServicesPage";
// import FarmerBookingsPage from "./pages/FarmerBookingsPage";

// import NewBookingPage from "./pages/NewBookingPage";

// // Expert Pages
// import ExpertDashboardPage from "./pages/ExpertDashboardPage";
// import PayoutsPage from "./pages/PayoutsPage";

// import ForgotPasswordPage from "./pages/ForgotPasswordPage";
// import ResetPasswordPage from "./pages/ResetPasswordPage";

// // The route protector
// import ProtectedRoute from './router/ProtectedRoute';

// import ProfilePage from "./pages/ProfilePage";

// import MyAvailabilityPage from "./pages/MyAvailabilityPage";

// /**
//  * A small component whose only job is to redirect the user
//  * based on their role after they have been authenticated.
//  */
// const RootRedirector = React.memo(() => {
//   const { user } = useAuth();

//   const redirectPath = useMemo(() => {
//     if (!user) {
//       return "/login";
//     }

//     switch (user.role) {
//       case 'admin':
//         return "/admin/dashboard";
//       case 'farmer':
//         return "/farmer/dashboard";
//       case 'expert':
//         return "/expert/dashboard";
//       default:
//         return "/login";
//     }
//   }, [user]);

//   return <Navigate to={redirectPath} replace />;
// });

// // --- LOAD STRIPE OUTSIDE THE COMPONENT ---
// const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// function App() {
//   const { user, loading } = useAuth();

//   if (loading) {
//     return <div>Loading Application...</div>;
//   }

//   return (
//     <Elements stripe={stripePromise}>
//       <Router>
//         <Routes>
//           {/* --- Public Routes --- */}
//           <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/" />} />
//           <Route path="/register" element={!user ? <RegistrationPage /> : <Navigate to="/" />} />
//           <Route path="/forgot-password" element={!user ? <ForgotPasswordPage /> : <Navigate to="/" />} />
//           <Route path="/reset-password" element={!user ? <ResetPasswordPage /> : <Navigate to="/" />} />

//           {/* --- Admin Protected Routes --- */}
//           <Route element={<ProtectedRoute allowedRoles={["admin"]} />}>
//             <Route element={<AdminLayout />}>
//               <Route path="/admin/dashboard" element={<DashboardPage />} />
//               <Route path="/admin/services" element={<ServicesPage />} />
//               <Route path="/admin/experts" element={<ExpertsPage />} />
//               <Route path="/admin/bookings" element={<BookingsPage />} />
//               <Route path="/admin/farmers" element={<FarmersPage />} />
//               <Route path="admin/announcements" element={<AnnouncementsPage />} />
//               <Route path="/admin/profile" element={<ProfilePage />} />
//             </Route>
//           </Route>

//           {/* --- Farmer Protected Routes --- */}
//           <Route element={<ProtectedRoute allowedRoles={["farmer"]} />}>
//             <Route element={<FarmerLayout />}>
//               <Route path="/farmer/dashboard" element={<FarmerDashboardPage />} />
//               <Route path="/browse-services" element={<BrowseServicesPage />} />
//               <Route path="/my-bookings" element={<FarmerBookingsPage />} />
//               <Route path="/new-booking/:serviceId" element={<NewBookingPage />} />
//               <Route path="/farmer/profile" element={<ProfilePage />} />
//             </Route>
//           </Route>

//           {/* --- Expert Protected Routes --- */}
//           <Route element={<ProtectedRoute allowedRoles={["expert"]} />}>
//             <Route element={<ExpertLayout />}>
//               <Route path="/expert/dashboard" element={<ExpertDashboardPage />} />
//               <Route path="/expert/payouts" element={<PayoutsPage />} />
//               <Route path="/expert/profile" element={<ProfilePage />} />
//               <Route path="/expert/availability" element={<MyAvailabilityPage />} />
//             </Route>
//           </Route>

//           {/* --- Root Redirector --- */}
//           <Route path="/" element={<RootRedirector />} />

//           {/* Optional: Fallback for any route not matched */}
//           <Route path="*" element={<Navigate to="/" />} />
//         </Routes>
//       </Router>
//     </Elements>
//   );
// }

// export default App;
